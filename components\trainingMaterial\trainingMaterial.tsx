"use client";
import React, { useEffect, useRef, useState } from "react";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import Image from "next/image";
import {
    Dropdown,
    Radio,
    Table,
    Menu,
    MenuProps,
    Avatar,
    Spin,
    Input,
    message,
    Modal,
    Button,
    Space,
    Select,
    Empty,
} from "antd";
import { forthGradeSubjects } from "@/src/libs/constants";
import { useRouter } from "next/navigation";
import type { ColumnsType } from "antd/es/table";
import { CircularProgress } from "@mui/material";
import trainingMaterialStyles from "./trainingMaterial.module.css";
import {
    materialDetail,
    getAllMaterial,
    createNewMaterial,
    getTrainingMaterialById,
    updateMaterial,
    deleteMaterial,
} from "@/src/services/trainingMaterial.api";
import { QueryParams, uploadFile } from "@/src/services/upload.api";
import { Roboto } from "next/font/google";
import { useTranslation } from "react-i18next";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface materialBySubject {
    srNo: number;
    subject?: string;
    id?: string;
    file?: string;
}

interface MaterialResponse {
    createdAt: string;
    file: string;
    fileName: string;
    id: string;
    isDeleted: boolean;
    serialNum: number;
    status: string;
    subject: string;
    updatedAt: string;
}

const { Option } = Select;

const TrainingMaterial = () => {
    const [isFileUploading, setIsFileUploading] = useState(false);
    const [selectedSubject, setSelectedSubject] = useState("ENGLISH");
    const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
    const [materialRowRecordId, setMaterialRowRecordId] = useState<any>(null);
    const [pageSize, setPageSize] = useState<number>(20);
    const [total, setTotal] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [data, setData] = useState<materialBySubject[]>([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [serialNo, setSerialNo] = useState<number | null>(null);
    const [subject, setSubject] = useState<any>();
    const [fileName, setFileName] = useState("");
    const [isTabledataLoading, setIsTabledataLoading] = useState(false);
    const [images, setImages] = useState<Array<string | undefined>>([
        undefined,
    ]);
    const [isCreateMaterialLoading, setIsCreateMaterialLoading] =
        useState(false);
    const [createMaterialRes, setCreateMaterialRes] = useState(null);
    const [materialRes, setMaterialRes] = useState<MaterialResponse | null>(
        null
    );
    const [fileUrl, setFileUrl] = useState(null);
    const [isDeleteModalVisible, setIsDeleteModalVisible] =
        useState<boolean>(false);
    const [isDelete, setIsDelete] = useState(false);
    const [selectedKeyOp, setSeletedKeyOp] = useState(null);
    const [modalDataLoading, setModalDataLoading] = useState(false);
    const [viewMaterialModalOpen, setViewMaterialModalOpen] = useState(false);

    const router = useRouter();
    const { t } = useTranslation();

    useEffect(() => {
        const session = localStorage.getItem("session");
        if (session) {
            fetchData(currentPage);
        } else {
            router.push("/");
        }
    }, [currentPage, pageSize, selectedSubject]);

    const fetchData = async (page: number = 1) => {
        setIsTabledataLoading(true);
        const queryParams: materialDetail = {
            skip: (page - 1) * pageSize,
            take: pageSize,
            orderBy: "createdAt|desc",
            subject: selectedSubject,
        };
        const authorization = localStorage.getItem("idToken");
        try {
            const response = await getAllMaterial(queryParams, authorization);

            if (response) {
                const updatedData = response.list.map(
                    (item: any, index: any) => ({
                        ...item,
                        srNo: (page - 1) * pageSize + index + 1,
                    })
                );
                setData(updatedData);
                setTotal(response.total);
                setIsTabledataLoading(false);
            }
        } catch (error: any) {
            if (error.message.includes("No")) {
                console.warn(t("No Training material found yet."));
            } else {
                message.error(t("Data could not be retrieved"));
            }
            setIsTabledataLoading(false);
            setData([]);
        }
    };

    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleMaterialChange = async (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const file = e.target.files?.[0];
        if (file) {
            if (file.type !== "application/pdf") {
                message.error(t("Only PDF files are allowed"));
                return;
            }

            const fileSizeLimit = 50 * 1024 * 1024;
            if (file.size > fileSizeLimit) {
                message.error(t("File size exceeds the limit of 50 MB"));
                return;
            }

            const trimmedFileName = file.name.replace(/\s+/g, "_");
            const queryParams: QueryParams = {
                fileName: trimmedFileName,
                fileType: "FILE",
                resourceType: "TRAINING",
            };
            const authorization = localStorage.getItem("idToken");

            try {
                setIsFileUploading(true);
                const response = await uploadFile(queryParams, authorization);
                if (response && response.preSignedUrl) {
                    const url = response.preSignedUrl;
                    try {
                        await fetch(url, {
                            method: "PUT",
                            body: file,
                            headers: {
                                "Content-Type": file.type,
                            },
                        });

                        setFileUrl(response.outPutUrl);
                        message.success(t("File uploaded successfully"));

                        if (fileInputRef.current) {
                            fileInputRef.current.value = "";
                        }
                        setIsFileUploading(false);
                    } catch (uploadError) {
                        message.error(t("Invalid presigned URL respons"));
                        setIsFileUploading(false);
                    }
                } else {
                    message.error(t("File upload failed"));
                    setIsFileUploading(false);
                }
            } catch (apiError) {
                message.error(t("Failed to retrieve presigned URL"));
                setIsFileUploading(false);
            }
        }
    };

    const handleCreteMaterial = async () => {
        if (subject === null && fileName === "" && fileUrl === null) {
            message.warning(
                t("Add details for the required fields for material")
            );
            return;
        }

        if (subject === null) {
            message.warning(t("Subject for material should not be empty"));
            return;
        }

        if (fileName === "") {
            message.warning(t("File name for material should not be empty"));
            return;
        }

        if (fileUrl === null) {
            message.warning(t("File should not be empty"));
            return;
        }

        try {
            setIsCreateMaterialLoading(true);
            const payload: materialDetail = {
                serialNum: serialNo,
                subject: subject,
                fileName: fileName,
                file: fileUrl ? fileUrl : "",
            };
            const authorization = localStorage.getItem("idToken");

            const createMaterialRes = await createNewMaterial(
                payload,
                authorization
            );
            if (createMaterialRes) {
                setIsCreateMaterialLoading(false);
                setIsModalVisible(false);
                setSerialNo(null);
                setSubject(null);
                setFileName("");
                setFileUrl(null);
                setSeletedKeyOp(null);
                fetchData(currentPage);
                message.success(t("Material created successfully"));
            }
        } catch (error) {
            message.error(t("Error creating material"));
            setIsModalVisible(true);
            setIsCreateMaterialLoading(false);
        }
    };

    const handleDeleteConfirm = async (id: string) => {
        try {
            setIsDelete(true);
            const authorization = localStorage.getItem("idToken");
            const queryParams: materialDetail = {
                materialId: id,
            };
            const response = await deleteMaterial(
                queryParams,
                id,
                authorization
            );
            if (response) {
                setIsDeleteModalVisible(false);
                fetchData(currentPage);
                message.success(t("Material deleted successfully"));
                setIsDelete(false);
            }
        } catch (error) {
            message.error(t("Failed to delete material"));
            setIsDelete(false);
        }
    };

    const fetchMaterialById = async (id: string) => {
        try {
            setModalDataLoading(true);
            const authorization = localStorage.getItem("idToken");
            const queryParams: materialDetail = {
                materialId: id,
            };
            const materialRes = await getTrainingMaterialById(
                queryParams,
                id,
                authorization
            );
            if (materialRes) {
                setMaterialRes(materialRes);
                setSerialNo(materialRes?.serialNum);
                setSubject(materialRes?.subject);
                setFileUrl(materialRes?.file);
                setFileName(materialRes?.fileName);
                setModalDataLoading(false);
            }
        } catch (error) {
            message.error(t("Failed to retrieve material"));
            setModalDataLoading(false);
        }
    };

    const handleEditMaterial = async (id: string) => {
        if (subject === null && fileName === "" && fileUrl === null) {
            message.warning(
                t("Add details for the required fields for material")
            );
            return;
        }

        if (subject === null) {
            message.warning(t("Subject for material should not be empty"));
            return;
        }

        if (fileName === "") {
            message.warning(t("File name for material should not be empty"));
            return;
        }

        if (fileUrl === null) {
            message.warning(t("File should not be empty"));
            return;
        }

        try {
            setIsCreateMaterialLoading(true);
            const payload: materialDetail = {
                serialNum: serialNo,
                subject: subject,
                fileName: fileName,
                file: fileUrl ? fileUrl : "",
            };
            const authorization = localStorage.getItem("idToken");

            const updateMaterialRes = await updateMaterial(
                id,
                payload,
                authorization
            );
            if (updateMaterialRes) {
                setIsCreateMaterialLoading(false);
                setIsModalVisible(false);
                setSeletedKeyOp(null);
                setSerialNo(null);
                setSubject(null);
                setFileName("");
                setFileUrl(null);
                fetchData(currentPage);
                message.success(t("Material updated successfully"));
            }
        } catch (error) {
            message.error(t("A material could not be updated"));
            setIsModalVisible(true);
            setIsCreateMaterialLoading(false);
        }
    };

    const handleItemClick = (key: any) => {
        setSelectedSubject(key);
        setDropdownVisible(false);
    };

    const handleRadioChange = (e: any) => {
        setSelectedSubject(e);
        setDropdownVisible(false);
    };

    const items: MenuProps["items"] = forthGradeSubjects.map((item) => ({
        key: item.value,
        label: (
            <div
                className="flex justify-between items-center"
                onClick={() => handleItemClick(item.value)}
            >
                <span
                    className={`text-[14px] font-roboto font-medium mr-[50px] ${
                        selectedSubject === item.value
                            ? "text-black"
                            : "text-gray-500"
                    }`}
                >
                    {item.label}
                </span>
                <Radio
                    value={item.value}
                    checked={selectedSubject === item.value}
                    className={`custom-radio ${
                        selectedSubject === item.value ? "accent-[#67A1A3]" : ""
                    }`}
                    onChange={() => handleRadioChange(item.value)}
                />
            </div>
        ),
    }));

    const handleCancel = () => {
        setIsModalVisible(false);
        setSerialNo(null);
        setSubject(null);
        setFileName("");
        setFileUrl(null);
        setSeletedKeyOp(null);
    };

    const handleMenuClick = async (e: any, record: any) => {
        const { key } = e;
        setSeletedKeyOp(key);
        setMaterialRowRecordId(record.id);
        if (key === "view") {
            setViewMaterialModalOpen(true);
            fetchMaterialById(record.id);
        } else if (key === "edit") {
            setIsModalVisible(true);
            fetchMaterialById(record.id);
        } else if (key === "delete") {
            setIsDeleteModalVisible(true);
        }
    };

    const menu = (record: any) => (
        <Menu onClick={(e) => handleMenuClick(e, record)}>
            <Menu.Item key="view">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/eye.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    {t("View")}
                </div>
            </Menu.Item>
            <Menu.Item key="edit">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[10px] mb-1"
                    />
                    {t("Edit")}
                </div>
            </Menu.Item>
            <Menu.Item key="delete">
                <div className="flex text-[14px] font-roboto text-[#F1676D]">
                    <Image
                        src="/images/trash.svg"
                        alt="visibility"
                        width={23}
                        height={23}
                        className="cursor-pointer mr-[7px] mb-1"
                    />
                    {t("delete")}
                </div>
            </Menu.Item>
        </Menu>
    );

    const materialBySubjects: ColumnsType<materialBySubject> = [
        {
            title: <div className="ml-2">{t("Sr. No.")}</div>,
            dataIndex: "srNo",
            key: "srNo",
            align: "left",
            className: `${roboto.className}`,
            width: 80,
            render: (srNo) => (
                <div className="pl-[20px] w-[100px]">{srNo}.</div>
            ),
        },
        {
            title: t("File Name"),
            dataIndex: "fileName",
            key: "fileName",
            align: "center",
            className: `${roboto.className}`,
            render: (fileName) => <div>{fileName ? fileName : "-"}</div>,
        },
        {
            title: t("Subject"),
            dataIndex: "subject",
            key: "subject",
            align: "center",
            className: `${roboto.className}`,
            render: (subject) => {
                const subjectLabel =
                    forthGradeSubjects.find((item) => item.value === subject)
                        ?.label || "-";
                return <div className="ml-[14px]">{subjectLabel}</div>;
            },
        },
        {
            title: t("File"),
            dataIndex: "file",
            key: "file",
            align: "center",
            className: `${roboto.className}`,
            render: (file) => {
                return (
                    <div className="flex justify-center items-center ml-[14px]">
                        {file ? (
                            <Image
                                src="/images/pdf.svg"
                                alt="File available"
                                width={30}
                                height={30}
                                className="cursor-pointer "
                            />
                        ) : (
                            "-"
                        )}
                    </div>
                );
            },
        },
        {
            title: t("More"),
            dataIndex: "more",
            key: "more",
            align: "center",
            className: `${roboto.className}`,
            width: 130,
            render: (more: string, record: materialBySubject) => {
                return record.id ? (
                    <Dropdown
                        overlay={menu(record)}
                        trigger={["click"]}
                        // placement="bottomRight"
                    >
                        <div
                            className="flex justify-center ml-[14px]"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <Image
                                src="/images/more.svg"
                                alt="More"
                                width={20}
                                height={20}
                                className="cursor-pointer"
                                onClick={(e) => e.preventDefault()}
                            />
                        </div>
                    </Dropdown>
                ) : (
                    <CircularProgress size={20} />
                );
            },
        },
    ];

    const paginationConfig = {
        pageSize: pageSize,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    const handlViewMaterialModalClose = () => {
        setViewMaterialModalOpen(false);
        setSerialNo(null);
        setSubject(null);
        setFileName("");
        setFileUrl(null);
        setSeletedKeyOp(null);
    };

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`${roboto.className} w-full bg-white text-[24px] text-black items-center mt-3`}
            >
                <div className="flex justify-between items-center px-4 mb-2">
                    <div className="flex flex-col">
                        <div className="flex">
                            <div className="flex items-center font-[600] text-[24px]">
                                {t("Training Materials")}
                            </div>
                            <Dropdown
                                menu={{ items }}
                                trigger={["click"]}
                                placement="bottomLeft"
                                open={dropdownVisible}
                                onOpenChange={setDropdownVisible}
                            >
                                <Image
                                    src={
                                        dropdownVisible
                                            ? "/images/arrow-up.svg"
                                            : "/images/arrow-bottom.svg"
                                    }
                                    alt="toggle dropdown"
                                    width={30}
                                    height={30}
                                    className="cursor-pointer pl-[9px] mt-[14px]"
                                />
                            </Dropdown>
                        </div>
                        <div className="text-black text-[16px] font-[400]">
                            {selectedSubject === "ENGLISH"
                                ? "English"
                                : selectedSubject === "GERMAN"
                                ? "German"
                                : "Maths"}
                        </div>
                    </div>
                    <div className="text-[25px] text-black flex items-center space-x-3">
                        <button
                            className="rounded-xl shadow-inner w-[150px] font-[500] h-[46px] text-[16px] px-2 bg-[#67A1A3] text-white flex justify-center items-center space-x-2"
                            onClick={() => {
                                setIsModalVisible(true);
                            }}
                        >
                            <span className="flex">{t("Add Materials")}</span>
                        </button>
                    </div>
                </div>
                {isTabledataLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <div className="w-full">
                        <Table
                            columns={materialBySubjects}
                            dataSource={data}
                            pagination={{
                                ...paginationConfig,
                                className: "custom-pagination custom-select",
                            }}
                            loading={isTabledataLoading}
                            bordered={false}
                            rowKey="srNo"
                            // className="custom-table"
                            className={`custom-table ${
                                data.length > 5
                                    ? "custom-table-scroll"
                                    : "custom-table-scroll-hide"
                            }  scrollbar font-[400]`}
                            // rowClassName="custom-table-row "
                            scroll={{
                                y: "65vh",
                            }}
                            locale={{
                                emptyText: (
                                    <div
                                        className={`h-[61vh] flex items-center justify-center font-roboto font-[500] text-gray-500`}
                                    >
                                        <Empty
                                            description={t("No data available")}
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        />
                                    </div>
                                ),
                            }}
                        />
                    </div>
                )}
                <Modal
                    open={isModalVisible}
                    onCancel={handleCancel}
                    footer={null}
                    className={`rounded-lg border-black w-[651px] h-[409px] ${trainingMaterialStyles.modal}`}
                    centered
                    width={651}
                    style={{ textAlign: "center", height: "409px" }}
                    closable={false}
                >
                    {modalDataLoading ? (
                        <div className="flex justify-center items-center h-[316px]">
                            <Spin size="default" />
                        </div>
                    ) : (
                        <>
                            <Space
                                direction="vertical"
                                style={{ width: "100%" }}
                                className="mt-2"
                            >
                                <div className="flex justify-between items-center w-full">
                                    <label className="text-[16px] flex justify-start font-roboto font-semibold w-[150px]">
                                        {t("Subject")} :
                                    </label>
                                    <Select
                                        placeholder={t("Select Subject")}
                                        value={subject}
                                        onChange={(value) => {
                                            setSubject(value);
                                        }}
                                        suffixIcon={
                                            <Image
                                                src="/images/arrowI.svg"
                                                alt="More"
                                                width={20}
                                                height={20}
                                            />
                                        }
                                        className="custom-select align-start text-[16px] font-roboto font-[400] w-[495px] h-[40px]"
                                    >
                                        {forthGradeSubjects.map((subject) => (
                                            <Option
                                                key={subject.value}
                                                value={subject.value}
                                            >
                                                {subject.label}
                                            </Option>
                                        ))}
                                    </Select>
                                </div>
                                <div className="flex justify-between items-center w-full">
                                    <label className="text-[16px] flex justify-start font-roboto font-semibold w-[150px]">
                                        {t("File Name")} :
                                    </label>
                                    <Input
                                        placeholder={t("File Name")}
                                        value={fileName}
                                        // onChange={(e) => {
                                        //     setFileName(e.target.value);
                                        // }}
                                        onChange={(e) =>
                                            setFileName(e.target.value)
                                        }
                                        onBlur={() =>
                                            setFileName(fileName.trim())
                                        }
                                        className="text-[14px] border-[#D3E3E3] bg-[#F6FAF9] font-roboto w-[495px] h-[40px]"
                                    />
                                </div>
                                <div className="flex items-center w-full">
                                    <label className="text-[16px] flex justify-start font-roboto font-semibold w-[140px]"></label>
                                    <label
                                        htmlFor={`inputFile`}
                                        className="flex rounded-md h-[124px] w-[124px] justify-center items-center cursor-pointer border-[#D3E3E3] bg-[#F6FAF9]"
                                        style={{
                                            border: "1px solid #D3E3E3",
                                            position: "relative",
                                        }}
                                    >
                                        <input
                                            id="inputFile"
                                            type="file"
                                            accept="application/pdf"
                                            style={{ display: "none" }}
                                            onChange={handleMaterialChange}
                                            ref={fileInputRef}
                                        />
                                        {isFileUploading ? (
                                            <div className="flex justify-center items-center w-full h-full absolute inset-0 z-10">
                                                <Spin size="small" />
                                            </div>
                                        ) : fileUrl !== null ? (
                                            <Image
                                                src="/images/pdf.svg"
                                                alt="More"
                                                width={100}
                                                height={100}
                                                className="cursor-pointer"
                                            />
                                        ) : (
                                            <p className="text-gray-300 font-roboto text-[14px]">
                                                {t("Add File")}
                                            </p>
                                        )}
                                    </label>
                                </div>
                            </Space>

                            <div className="mt-6 flex justify-center space-x-4">
                                <Button
                                    onClick={handleCancel}
                                    className={`${trainingMaterialStyles.button} bg-transparent font-roboto text-[#67A1A3] border-[1px] border-[#67A1A3] text-[18px] w-[287px] h-[45px] font-medium rounded-xl`}
                                >
                                    {t("cancel")}
                                </Button>
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        if (selectedKeyOp === "edit") {
                                            handleEditMaterial(
                                                materialRowRecordId
                                            );
                                        } else {
                                            handleCreteMaterial();
                                        }
                                    }}
                                    className={`${trainingMaterialStyles.button} bg-[#67A1A3] shadow-inner font-roboto text-white text-[18px] w-[287px] h-[45px] font-medium rounded-xl`}
                                    loading={isCreateMaterialLoading}
                                >
                                    {t("save")}
                                </Button>
                            </div>
                        </>
                    )}
                </Modal>
                <Modal
                    open={isDeleteModalVisible}
                    onCancel={() => setIsDeleteModalVisible(false)}
                    footer={null}
                    centered
                    width={300}
                    style={{ textAlign: "center" }}
                    closable={false}
                >
                    <Image
                        src="/images/modalDelete.svg"
                        alt="Delete"
                        width={100}
                        height={100}
                        className="mx-auto bg-white -mt-3"
                    />

                    <div className=" flex justify-center items-center w-full">
                        <h2 className="text-[16px] font-roboto font-[400] mb-8 w-[80%] flex justify-center items-center">
                            {t(
                                "Are you sure you want to delete this material?"
                            )}
                        </h2>
                    </div>
                    <div className="flex justify-center space-x-4">
                        <Button
                            onClick={() => {
                                setIsDeleteModalVisible(false);
                                setSeletedKeyOp(null);
                            }}
                            className="bg-transparent cancelbutton font-roboto text-[#F1676D] text-[18px] h-[46px] border-[1px] border-[#F1676D] w-[116px] font-medium rounded-xl"
                        >
                            {t("cancel")}
                        </Button>
                        <Button
                            type="primary"
                            className="bg-[#F1676D] shadow-inner deleteButton font-roboto text-white text-[18px] w-[116px] h-[46px] font-medium rounded-xl"
                            onClick={() => {
                                handleDeleteConfirm(materialRowRecordId);
                            }}
                            loading={isDelete}
                        >
                            {t("delete")}
                        </Button>
                    </div>
                </Modal>
                <Modal
                    open={viewMaterialModalOpen}
                    onCancel={handlViewMaterialModalClose}
                    footer={null}
                    centered
                    width="70%"
                    height="60%"
                    style={{
                        textAlign: "center",
                        height: "350px",
                        position: "absolute",
                        right: "7%",
                        top: "10%",
                    }}
                    closable={false}
                >
                    {modalDataLoading ? (
                        <div className="flex justify-center items-center h-[316px]">
                            <Spin size="default" />
                        </div>
                    ) : (
                        <>
                            <div className="flex justify-between items-center mb-4">
                                <p
                                    className={`text-lg font-semibold ${roboto.className}`}
                                >
                                    {materialRes?.fileName}
                                </p>
                                <Image
                                    src="/images/cross.svg"
                                    alt="cross"
                                    width={20}
                                    height={20}
                                    className="cursor-pointer"
                                    onClick={handlViewMaterialModalClose}
                                />
                            </div>

                            {/* PDF display */}
                            <embed
                                src={materialRes ? materialRes?.file : "-"}
                                type="application/pdf"
                                width="100%"
                                height="500px"
                            />
                        </>
                    )}
                </Modal>
            </div>
        </div>
    );
};

export default TrainingMaterial;

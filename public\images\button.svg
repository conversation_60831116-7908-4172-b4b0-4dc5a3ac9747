<svg width="288" height="51" viewBox="0 0 288 51" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_47_353)">
<rect x="0.5" y="0.5" width="287" height="50" rx="10" fill="#67A1A3"/>
</g>
<defs>
<filter id="filter0_i_47_353" x="0.5" y="0.5" width="287" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_47_353"/>
</filter>
</defs>
</svg>

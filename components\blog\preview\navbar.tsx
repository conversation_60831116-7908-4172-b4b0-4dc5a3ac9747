"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "next/font/google";
import { Button, Dropdown, Menu, Modal, Input } from "antd";
import { useRouter, usePathname } from "next/navigation";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const Navbar = ({
    isBlogViewFlag,
    isVideoViewFlag,
    selectedAnyBlog,
    setSelectedAnyBlog,
}: any) => {
    const [selectedButton, setSelectedButton] = useState("");
    const [visible, setVisible] = useState(false);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const path = usePathname();

    useEffect(() => {
        const segments = path.split("/");
        const page = segments[1];

        if (page && page !== "" && page !== "knowledge-rally") {
            setSelectedButton(page);
        }
    }, [path]);

    const handleViewMenuClick = () => {
        setDropdownOpen(!dropdownOpen);
    };

    const viewMenu = (
        <Menu
            onClick={handleViewMenuClick}
            className="w-[50vw] h-auto custom-menu-item"
        >
            <Menu.Item
                key="blog"
                className="text-start"
                style={{ backgroundColor: "transparent" }}
            >
                <div
                    className={`hover:text-[#004F53] pl-3 text-[18px] font-[500] cursor-pointer ${
                        roboto.className
                    } ${
                        selectedButton === "buisness-blog" ||
                        selectedButton === "school-blog" ||
                        selectedButton === "education-blog" ||
                        isBlogViewFlag === true
                            ? "border-2 border-[#004F53] border-opacity-65 px-2 py-1 rounded-[4px] text-[#004F53]"
                            : "text-[#67A1A3]"
                    }`}
                >
                    Blog
                </div>
            </Menu.Item>
            <Menu.Item
                key="video"
                className="text-start"
                style={{ backgroundColor: "transparent" }}
            >
                <div
                    className={`hover:text-[#004F53] pl-3 text-[18px] font-[500] cursor-pointer ${
                        roboto.className
                    } ${
                        selectedButton === "erklärvideo" ||
                        isVideoViewFlag === true
                            ? "border-2 border-[#004F53] border-opacity-65 px-2 py-1 rounded-[4px] text-[#004F53]"
                            : "text-[#67A1A3]"
                    }`}
                >
                    Erklärvideo
                </div>
            </Menu.Item>
            <Menu.Item
                key="faq"
                className="text-start"
                style={{ backgroundColor: "transparent" }}
            >
                <div
                    className={`hover:text-[#004F53] pl-3 text-[18px] font-[500] cursor-pointer ${
                        roboto.className
                    } ${
                        selectedButton === "faqs"
                            ? "border-2 border-[#004F53] border-opacity-65 px-2 py-1 rounded-[4px] text-[#004F53]"
                            : "text-[#67A1A3]"
                    }`}
                >
                    FAQs
                </div>
            </Menu.Item>
            <Menu.Item
                key="about"
                className="text-start"
                style={{ backgroundColor: "transparent" }}
            >
                <div
                    className={`hover:text-[#004F53] pl-3 text-[18px] font-[500] cursor-pointer ${
                        roboto.className
                    } ${
                        selectedButton === "about-us"
                            ? "border-2 border-[#004F53] border-opacity-65 px-2 py-1 rounded-[4px] text-[#004F53]"
                            : "text-[#67A1A3]"
                    }`}
                >
                    Über uns
                </div>
            </Menu.Item>
        </Menu>
    );

    useEffect(() => {
        const handleScroll = () => {
            setVisible(false);
            setDropdownOpen(false);
        };

        window.addEventListener("scroll", handleScroll);
        return () => {
            window.removeEventListener("scroll", handleScroll);
        };
    }, []);

    return (
        <>
            <div className="bg-[#FFFFFF] w-full h-[10vh] flex justify-between items-center shadow-lg py-4 px-4 md:pl-16 md:pr-5">
                {/* Desktop view */}
                <div
                    className="flex pl-3 gap-2 md:gap-3  items-center cursor-pointer"
                    onClick={() => {
                        setSelectedAnyBlog(!selectedAnyBlog);
                    }}
                >
                    <Image
                        src="/images/Logo_2.svg"
                        alt="login"
                        width={45}
                        height={45}
                        className="shadowed-element rounded-full w-[35px] h-[35px] md:w-[45px] md:h-[45px]"
                    />
                    <h1
                        className={`text-[#004F53] text-opacity-80 text-[24px] md:text-[30px] font-bold ${roboto.className}`}
                    >
                        Gymi
                    </h1>
                </div>
                <div className="flex items-center gap-4 md:gap-10">
                    <Image
                        src="/images/cross.svg"
                        alt="cross"
                        width={28}
                        height={28}
                        className="cursor-pointer mr-[10px]"
                        onClick={() => {
                            setSelectedAnyBlog(!selectedAnyBlog);
                        }}
                    />
                </div>

                {/* Mobile view Dropdown */}
                <div className="md:hidden flex items-center gap-2">
                    {/* <Dropdown
                        overlay={menu}
                        open={visible}
                        onOpenChange={setVisible}
                        trigger={["click"]}
                    > */}
                    <Button
                        type="primary"
                        style={{ backgroundColor: "#67A1A3" }}
                        className={`bg-[#67A1A3] button-shadow font-[500] text-white text-[14px] md:text-[16px] w-[100px] md:w-[140px] h-[35px] md:h-[46px] rounded-lg ${roboto.className}`}
                    >
                        Loslegen
                    </Button>
                    {/* </Dropdown> */}
                    <Button
                        type="text"
                        icon={
                            dropdownOpen ? (
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                >
                                    <path
                                        fillRule="evenodd"
                                        clipRule="evenodd"
                                        d="M18.8506 6.45041C19.21 6.09113 19.21 5.50862 18.8506 5.14934C18.4914 4.79006 17.9089 4.79006 17.5497 5.14934L12.0001 10.6988L6.45067 5.14934C6.09138 4.79006 5.50887 4.79006 5.14959 5.14934C4.79031 5.50862 4.79031 6.09113 5.14959 6.45041L10.6991 11.9999L5.14959 17.5493C4.79031 17.9087 4.79031 18.4911 5.14959 18.8505C5.50887 19.2097 6.09138 19.2097 6.45067 18.8505L12.0001 13.301L17.5497 18.8505C17.9089 19.2097 18.4914 19.2097 18.8506 18.8505C19.21 18.4911 19.21 17.9087 18.8506 17.5493L13.3012 11.9999L18.8506 6.45041Z"
                                        fill="#67A1A3"
                                    />
                                </svg>
                            ) : (
                                <svg
                                    className="mx-auto"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                >
                                    <path
                                        d="M20 7H4M20 12H4M20 17H4"
                                        stroke="#67A1A3"
                                        strokeWidth="1.5"
                                        strokeLinecap="round"
                                    />
                                </svg>
                            )
                        }
                        onClick={handleViewMenuClick}
                    />
                    <Dropdown
                        overlay={viewMenu}
                        open={dropdownOpen}
                        onOpenChange={() => {}}
                        trigger={["click"]}
                        placement="bottomRight"
                        className="mt-16 "
                    >
                        <div />
                    </Dropdown>
                </div>
            </div>
        </>
    );
};

export default Navbar;

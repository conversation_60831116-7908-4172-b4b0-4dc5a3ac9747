import { fetch } from "@/src/libs/helpers";

export interface QueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    practiceType?: string;
    subject?: string;
}

export interface Options {
    value?: string;
}

export interface quetionsDetail {
    serialNum?: number;
    grade?: string;
    level?: string;
    levelWiseNum?: number | null;
    subject?: string;
    category?: string;
    practiceType?: string;
    type?: string;
    question?: any;
    questionImage?: boolean;
    image?: string | null;
    answerType?: string;
    options?: Options[] | any;
    correctAnswer?: number | string | null | undefined;
    blanksCount?: number | null;
    answers?: string[] | any;
    images?: string[] | any;
    documentFiles?: string[] | any;
    audioFiles?: string[] | any;
}

export interface reorderQuestionPayload {
    grade?: string;
    level?: string;
    questionId?: string;
    newPosition?: Number;
    subject?: string;
}

export const getAllQuestions = async (
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/question/all-questions",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const addQuestion = async (
    payload: quetionsDetail,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/question",
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getQuestion = async (
    questionId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/question/${questionId}`,
        method: "GET",
        headers: {
            Authorization: authorization,
        },
    });
};

export const editQuestion = async (
    payload: quetionsDetail,
    questionId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/question/${questionId}`,
        method: "PUT",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deleteQuestion = async (
    questionId: any,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/question/${questionId}`,
        method: "DELETE",
        headers: {
            Authorization: authorization,
        },
    });
};

export const reorderQuestion = async (
    payload: reorderQuestionPayload,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/question/reorder`,
        method: "PUT",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

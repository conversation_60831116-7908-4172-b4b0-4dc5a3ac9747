"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { Input, Select, Button, message, Spin } from "antd";
import {
    schoolSupervision,
    schoolType,
    schoolBoardingType,
} from "@/src/libs/constants";
import {
    QueryParams,
    deleteUploadedFile,
    uploadFile,
} from "@/src/services/upload.api";
import {
    addSchoolQueryParams,
    highSchoolDetail,
    updateSchoolById,
    getSchoolById,
    addNewSchool,
} from "@/src/services/highSchool.api";
import { Dropdown, Menu } from "antd";
import { Roboto } from "next/font/google";
import TextArea from "antd/es/input/TextArea";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const { Option } = Select;

const AddHighSchool = () => {
    const [schoolName, setSchoolName] = useState("");
    const [postalCode, setPostalCode] = useState<string | null>(null);
    const [contact, setContact] = useState<string | null>(null);
    const [email, setEmail] = useState<string | null>(null);
    const [location, setLocation] = useState("");
    const [locationUrl, setLocationUrl] = useState("");
    const [state, setState] = useState("");
    const [selectSchoolType, setSelectSchoolType] = useState(null);
    const [selectSchoolSpecifications, setSelectSchoolSpecifications] =
        useState("");
    const [selectSchoolSupervision, setSelectSchoolSupervision] =
        useState(null);
    const [isBoarding, setIsBoarding] = useState(null);
    const [link, setLink] = useState("");
    const [description, setDescription] = useState("");
    const [loading, setLoading] = useState(false);
    const [isImageUploading, setIsImageUploading] = useState(false);
    const [isImageUploadingIndex, setIsImageUploadingIndex] =
        useState<number>();
    const [schoolImages, setSchoolImages] = useState<
        {
            label: string;
            id: number;
            value: string | null;
        }[]
    >([
        { label: "image1", id: 1, value: null },
        { label: "image2", id: 2, value: null },
        { label: "image3", id: 3, value: null },
        { label: "image4", id: 4, value: null },
        { label: "image5", id: 5, value: null },
    ]);
    const maxLength = 1000;
    const { t } = useTranslation();

    const handleDescriptionChange = (
        e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>
    ) => {
        const value = e.target.value;
        if (value.length <= maxLength) {
            setDescription(value);
        }
    };

    const handleDescriptionBlur = () => {
        setDescription(description.trim());
    };

    const router = useRouter();

    const handlePostalCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        const numericValue = value.replace(/\D/g, "");
        setPostalCode(numericValue);
    };

    const handleImageChange = async (
        e: React.ChangeEvent<HTMLInputElement>,
        index: number
    ) => {
        const file: any = e.target.files?.[0];
        if (file) {
            const trimmedFileName = file.name.replace(/\s+/g, "_");
            const queryParams: QueryParams = {
                fileName: trimmedFileName,
                fileType: "IMAGE",
                resourceType: "SCHOOL",
            };
            const authorization = localStorage.getItem("idToken");
            // return
            try {
                setIsImageUploading(true);
                setIsImageUploadingIndex(index);
                const response = await uploadFile(queryParams, authorization);

                if (response && response.preSignedUrl) {
                    const url = response.preSignedUrl;
                    try {
                        await fetch(url, {
                            method: "PUT",
                            body: file,
                            headers: {
                                "Content-Type": file.type,
                            },
                        });

                        const newSchoolImages = [...schoolImages];
                        newSchoolImages[index].value = response.outPutUrl;
                        setSchoolImages(newSchoolImages);
                        setIsImageUploading(false);
                    } catch (uploadError) {
                        message.error(t("Image upload failed"));
                        setIsImageUploading(false);
                    }
                } else {
                    message.error(t("Invalid presigned URL response"));
                    setIsImageUploading(false);
                }
            } catch (apiError) {
                message.error(t("Failed to get presigned URL"));
                setIsImageUploading(false);
            }
        }
    };

    const deleteSchoolImage = async (index: number) => {
        const imageUrl = schoolImages[index].value;

        if (imageUrl) {
            const queryParams: QueryParams = {
                fileName: imageUrl.split("/").pop(),
                fileType: "IMAGE",
                resourceType: "SCHOOL",
            };
            const authorization = localStorage.getItem("idToken");

            try {
                // Delete the image from storage
                await deleteUploadedFile(queryParams, authorization);

                // Update state to set value to null for the deleted image
                const newSchoolImages = [...schoolImages];
                newSchoolImages[index] = {
                    ...newSchoolImages[index],
                    value: null,
                };

                // Update state with the modified image array
                setSchoolImages(newSchoolImages);
            } catch (error) {
                message.error(t("Error deleting image"));
            }
        }
    };

    const handleAddFromGallery = (index: number) => {
        const inputFileElement = document.getElementById(`inputFile${index}`);
        if (inputFileElement) {
            (inputFileElement as HTMLInputElement).click();
        }
    };

    const menu = (index: any) => (
        <Menu>
            <Menu.Item
                key="1"
                onClick={() => handleAddFromGallery(index)}
                className="font-roboto"
            >
                <div className={`flex text-[14px] ${roboto.className}`}>
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px]"
                    />
                    {t("Add From gallery")}
                </div>
            </Menu.Item>
            {schoolImages?.[index]?.value !== null && (
                <Menu.Item
                    key="2"
                    onClick={() => deleteSchoolImage(index)}
                    className="font-roboto"
                >
                    <div
                        className={`text-[#F1676D] flex text-[14px] ${roboto.className}`}
                    >
                        <Image
                            src="/images/trash.svg"
                            alt="visibility"
                            width={23}
                            height={23}
                            className="cursor-pointer mr-[7px] mb-1"
                        />
                        {t("Delete this image")}
                    </div>
                </Menu.Item>
            )}
        </Menu>
    );

    const handleAddHighSchool = async () => {
        setLoading(true);
        const urlRegex =
            /((http|https):\/\/)?(www\.)?[a-zA-Z0-9@:%._\+~#?&//=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%._\+~#?&//=]*)/;

        if (schoolName === null || schoolName === "") {
            message.error(t("School Name should not be empty"));
            setLoading(false);
            return;
        }

        if (contact === null || contact === "") {
            message.error(t("contact should not be empty"));
            setLoading(false);
            return;
        }

        const allImagesUploaded = schoolImages[0]?.value !== null;

        // if (!allImagesUploaded) {
        //     message.error(t("Upload at least one image of the school")); //Upload at least one image of the school
        //     setLoading(false);
        //     return;
        // }

        if (locationUrl.trim() === "" || !urlRegex.test(locationUrl)) {
            message.error(t("Provide a valid location URL")); //Please provide a valid Location URL
            setLoading(false);
            return;
        }

        if (link.trim() === "" || !urlRegex.test(link)) {
            message.error(t("Provide a valid link URL")); //Provide a valid URL
            setLoading(false);
            return;
        }

        if (contact === null || contact === "") {
            message.error(t("Contact should not be empty"));
            setLoading(false);
            return;
        }

        if (postalCode === null || postalCode === "") {
            message.error(t("PostalCode should not be empty")); //PostalCode should not be empty
            setLoading(false);
            return;
        }

        if (location === null || location === "") {
            message.error(t("Location should not be empty")); //Location should not be empty
            setLoading(false);
            return;
        }

        if (state === null || state === "") {
            message.error(t("State should not be empty")); //State should not be empty
            setLoading(false);
            return;
        }

        if (
            selectSchoolSpecifications === null ||
            selectSchoolSpecifications === ""
        ) {
            message.error(t("Specifications should not be empty")); //Specifications should not be empty
            setLoading(false);
            return;
        }

        const payload: highSchoolDetail = {
            ...(allImagesUploaded
                ? {
                      picture: schoolImages
                          .filter((image) => image.value !== null)
                          .map((image) => image.value),
                  }
                : { picture: [] }),
            name: schoolName,
            postCode: postalCode,
            countryCode: "+91",
            email: "<EMAIL>",
            // email: email,
            contact: contact,
            location: location,
            locationUrl: locationUrl,
            state: state,
            schoolType: selectSchoolType,
            specialization: selectSchoolSpecifications,
            afternoonCare: selectSchoolSupervision,
            boardingSchool: isBoarding,
            description: description,
            website: link,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await addNewSchool(payload, authorization);
            if (
                response.statusCode === 400 &&
                response.message.includes("School")
            ) {
                message.error(t("School already exists"));
            } else if (response.statusCode === 400) {
                message.error(t("Fill all required field"));
            }

            if (response) {
                message.success(t("School added successfully"));
                router.push("/highSchool");
            }
        } catch (error: any) {
            if (error.message.includes("options.")) {
                message.error(t("Option field should not be empty"));
            } else {
                message.error(
                    t("Fill all required field") || t("Failed add school")
                );
            }
        }
        setLoading(false);
    };

    const handleEditSchool = async (schoolId: string) => {
        setLoading(true);
        if (schoolName === null || schoolName === "") {
            message.error(t("School Name should not be empty"));
            setLoading(false);
            return;
        }

        const urlRegex =
            /((http|https):\/\/)?(www\.)?[a-zA-Z0-9@:%._\+~#?&//=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%._\+~#?&//=]*)/;

        if (locationUrl.trim() === "" || !urlRegex.test(locationUrl)) {
            message.error(t("Provide a valid location URL"));
            setLoading(false);
            return;
        }

        if (link.trim() === "" || !urlRegex.test(link)) {
            message.error(t("Provide a valid link URL"));
            setLoading(false);
            return;
        }

        const allImagesUploaded = schoolImages[0]?.value !== null;

        // if (!allImagesUploaded) {
        //     message.error(t("Upload at least one image of the school"));
        //     setLoading(false);
        //     return;
        // }

        if (contact === null || contact === "") {
            message.error(t("Contact should not be empty")); //Contact should not be empty
            setLoading(false);
            return;
        }

        if (postalCode === null || postalCode === "") {
            message.error(t("PostalCode should not be empty")); //
            setLoading(false);
            return;
        }

        if (location === null || location === "") {
            message.error(t("Location should not be empty"));
            setLoading(false);
            return;
        }

        if (state === null || state === "") {
            message.error(t("State should not be empty"));
            setLoading(false);
            return;
        }

        if (
            selectSchoolSpecifications === null ||
            selectSchoolSpecifications === ""
        ) {
            message.error(t("Specifications should not be empty"));
            setLoading(false);
            return;
        }

        const payload: highSchoolDetail = {
            ...(allImagesUploaded
                ? {
                      picture: schoolImages
                          .filter((image) => image.value !== null)
                          .map((image) => image.value),
                  }
                : { picture: [] }),
            name: schoolName,
            postCode: postalCode,
            countryCode: "+91",
            email: "<EMAIL>",
            // email: email,
            contact: contact,
            location: location,
            locationUrl: locationUrl,
            state: state,
            schoolType: selectSchoolType,
            specialization: selectSchoolSpecifications,
            afternoonCare: selectSchoolSupervision,
            boardingSchool: isBoarding,
            description: description,
            website: link,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await updateSchoolById(
                schoolId,
                payload,
                authorization
            );
            message.success(t("School edited successfully")); //School edited successfully
            router.push("/highSchool");
        } catch (error: any) {
            message.error(error.message || t("Failed to edit School"));
        }
        setLoading(false);
    };

    const searchParams = useSearchParams();

    useEffect(() => {
        const id = searchParams.get("id");

        if (id) {
            const fetchSchool = async () => {
                const authorization = localStorage.getItem("idToken");
                const response = await getSchoolById(id, authorization);

                const updatedImages = response.picture
                    ? schoolImages.map((image, index) => ({
                          ...image,
                          value:
                              response.picture[index] !== undefined
                                  ? response.picture[index]
                                  : null,
                      }))
                    : schoolImages.map((image) => ({
                          ...image,
                          value: null,
                      }));

                setSchoolImages(updatedImages);
                setSchoolName(response.name);
                setPostalCode(response.postCode);
                setContact(response.contact);
                setEmail(response.email);
                setLocation(response.location);
                setLocationUrl(response.locationUrl);
                setState(response.state);
                setSelectSchoolType(response.schoolType);
                setSelectSchoolSpecifications(response.specialization);
                setSelectSchoolSupervision(response.afternoonCare);
                setIsBoarding(response.boardingSchool);
                setDescription(response.description);
                setLink(response.website);
                setVisibleInputs(
                    response?.picture !== null
                        ? response?.picture.length - 1
                        : 0
                );
            };
            fetchSchool();
        }
    }, [searchParams]);

    const [visibleInputs, setVisibleInputs] = useState(0);

    const handleAddMoreImages = () => {
        if (visibleInputs < 4) {
            setVisibleInputs(visibleInputs + 1);
        }
    };

    return (
        <div className={`${roboto.className}`}>
            <div className="w-full shadow-md text-[25px] text-black h-auto flex justify-between items-center px-4 py-3 z-10 bg-white">
                <div>
                    <h1
                        className={`${roboto.className} font-[700] text-[24px]`}
                    >
                        {searchParams.get("mode") === "view"
                            ? t("View High School")
                            : searchParams.get("mode") === "edit"
                            ? t("Edit High School")
                            : t("Add High School")}
                    </h1>
                </div>
                <div
                    className={`${roboto.className} text-[20px] text-black h-auto flex items-center px-4 gap-5`}
                >
                    <div className="flex justify-end">
                        {searchParams.get("mode") !== "view" && (
                            <>
                                {searchParams.get("mode") === "edit" ? (
                                    <Button
                                        type="primary"
                                        className={`${roboto.className} bg-[#67A1A3] mr-2 shadow-inner button text-white text-[18px] w-[180px] h-[46px] rounded-xl`}
                                        onClick={() =>
                                            handleEditSchool(
                                                searchParams.get("id") as string
                                            )
                                        }
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                ) : (
                                    <Button
                                        type="primary"
                                        className={`${roboto.className} bg-[#67A1A3] mr-2 shadow-inner button text-white text-[18px] w-[180px] h-[46px] rounded-xl`}
                                        onClick={handleAddHighSchool}
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                )}
                            </>
                        )}
                    </div>
                    <Image
                        src="/images/cross.svg"
                        alt="cross"
                        width={28}
                        height={28}
                        className="cursor-pointer mr-[10px]"
                        onClick={() => router.back()}
                    />
                </div>
            </div>
            <div className="flex justify-start w-full text-[18px] text-black font-semibold  px-4 sticky z-0 bg-white overflow-y-auto mt-5 h-[calc(100vh-100px)] custom-scroll">
                <div className="flex flex-col gap-4 w-[40%] mb-10">
                    <div className="w-full h-[259px] bg-[#D3E3E3] relative rounded-lg">
                        <Input
                            id="inputFile0"
                            type="file"
                            accept="image/*"
                            style={{ cursor: "pointer", display: "none" }}
                            onChange={(e) => handleImageChange(e, 0)}
                        />
                        {isImageUploading && isImageUploadingIndex === 0 ? (
                            <div className="flex justify-center items-center w-full h-full absolute inset-0 z-10">
                                <Spin size="default" />
                            </div>
                        ) : (
                            schoolImages[0]?.value && (
                                <img
                                    src={schoolImages[0].value}
                                    alt="Uploaded"
                                    className="w-full h-full object-cover rounded-lg"
                                />
                            )
                        )}
                        {searchParams.get("mode") !== "view" && (
                            <Dropdown
                                overlay={menu(0)}
                                trigger={["click"]}
                                placement="bottomRight"
                            >
                                <Image
                                    src="/images/more.svg"
                                    alt="More"
                                    width={20}
                                    height={20}
                                    onClick={(e) => e.preventDefault()}
                                    className="absolute top-3 right-2 cursor-pointer bg-[#D3E3E3] rounded-md"
                                    style={{ fontSize: "20px" }}
                                />
                            </Dropdown>
                        )}
                    </div>
                    <div className="flex gap-4">
                        {(visibleInputs >= 1 ||
                            schoolImages[1]?.value !== null) && (
                            <div className="w-[49%] h-[200px] cursor-pointer bg-[#D3E3E3] relative rounded-lg">
                                <label
                                    htmlFor="inputFile1"
                                    className="w-full h-full flex items-center justify-center cursor-pointer"
                                >
                                    <Input
                                        id="inputFile1"
                                        type="file"
                                        accept="image/*"
                                        style={{
                                            cursor: "pointer",
                                            display: "none",
                                        }}
                                        onChange={(e) =>
                                            handleImageChange(e, 1)
                                        }
                                        disabled={
                                            searchParams.get("mode") === "view"
                                        }
                                    />
                                    {schoolImages[1]?.value && (
                                        <img
                                            src={schoolImages[1].value}
                                            alt="Uploaded"
                                            className="w-full h-full object-cover rounded-lg"
                                        />
                                    )}
                                    {searchParams.get("mode") !== "view" && (
                                        <Dropdown
                                            overlay={menu(1)}
                                            trigger={["click"]}
                                            placement="bottomRight"
                                        >
                                            <Image
                                                src="/images/more.svg"
                                                alt="More"
                                                width={20}
                                                height={20}
                                                onClick={(e) =>
                                                    e.preventDefault()
                                                }
                                                className="absolute top-3 right-2 cursor-pointer bg-[#D3E3E3] rounded-md"
                                                style={{ fontSize: "20px" }}
                                            />
                                        </Dropdown>
                                    )}
                                </label>
                            </div>
                        )}
                        {(visibleInputs >= 2 ||
                            schoolImages[2]?.value !== null) && (
                            <div className="w-[49%] h-[200px] cursor-pointer bg-[#D3E3E3] relative rounded-lg">
                                <label
                                    htmlFor="inputFile2"
                                    className="w-full h-full flex items-center justify-center cursor-pointer"
                                >
                                    <Input
                                        id="inputFile2"
                                        type="file"
                                        accept="image/*"
                                        style={{
                                            cursor: "pointer",
                                            display: "none",
                                        }}
                                        onChange={(e) =>
                                            handleImageChange(e, 2)
                                        }
                                        disabled={
                                            searchParams.get("mode") === "view"
                                        }
                                    />
                                    {schoolImages[2]?.value && (
                                        <img
                                            src={schoolImages[2].value}
                                            alt="Uploaded"
                                            className="w-full h-full object-cover rounded-lg"
                                        />
                                    )}
                                    {searchParams.get("mode") !== "view" && (
                                        <Dropdown
                                            overlay={menu(2)}
                                            trigger={["click"]}
                                            placement="bottomRight"
                                        >
                                            <Image
                                                src="/images/more.svg"
                                                alt="More"
                                                width={20}
                                                height={20}
                                                onClick={(e) =>
                                                    e.preventDefault()
                                                }
                                                className="absolute top-3 right-2 cursor-pointer bg-[#D3E3E3] rounded-md"
                                                style={{ fontSize: "20px" }}
                                            />
                                        </Dropdown>
                                    )}
                                </label>
                            </div>
                        )}
                    </div>

                    <div className="flex gap-4">
                        {(visibleInputs >= 3 ||
                            schoolImages[3]?.value !== null) && (
                            <div className="w-[49%] h-[200px] cursor-pointer bg-[#D3E3E3] relative rounded-lg">
                                <label
                                    htmlFor="inputFile3"
                                    className="w-full h-full flex items-center justify-center cursor-pointer"
                                >
                                    <Input
                                        id="inputFile3"
                                        type="file"
                                        accept="image/*"
                                        style={{
                                            cursor: "pointer",
                                            display: "none",
                                        }}
                                        onChange={(e) =>
                                            handleImageChange(e, 3)
                                        }
                                        disabled={
                                            searchParams.get("mode") === "view"
                                        }
                                    />
                                    {schoolImages[3]?.value && (
                                        <img
                                            src={schoolImages[3].value}
                                            alt="Uploaded"
                                            className="w-full h-full object-cover rounded-lg"
                                        />
                                    )}
                                    {searchParams.get("mode") !== "view" && (
                                        <Dropdown
                                            overlay={menu(3)}
                                            trigger={["click"]}
                                            placement="bottomRight"
                                        >
                                            <Image
                                                src="/images/more.svg"
                                                alt="More"
                                                width={20}
                                                height={20}
                                                onClick={(e) =>
                                                    e.preventDefault()
                                                }
                                                className="absolute top-3 right-2 cursor-pointer bg-[#D3E3E3] rounded-md"
                                                style={{ fontSize: "20px" }}
                                            />
                                        </Dropdown>
                                    )}
                                </label>
                            </div>
                        )}
                        {(visibleInputs >= 4 ||
                            schoolImages[4]?.value !== null) && (
                            <div className="w-[49%] h-[200px] cursor-pointer bg-[#D3E3E3] relative rounded-lg">
                                <label
                                    htmlFor="inputFile4"
                                    className="w-full h-full flex items-center justify-center cursor-pointer"
                                >
                                    <Input
                                        id="inputFile4"
                                        type="file"
                                        accept="image/*"
                                        style={{
                                            cursor: "pointer",
                                            display: "none",
                                        }}
                                        onChange={(e) =>
                                            handleImageChange(e, 4)
                                        }
                                        disabled={
                                            searchParams.get("mode") === "view"
                                        }
                                    />
                                    {schoolImages[4]?.value && (
                                        <img
                                            src={schoolImages[4].value}
                                            alt="Uploaded"
                                            className="w-full h-full object-cover rounded-lg"
                                        />
                                    )}
                                    {searchParams.get("mode") !== "view" && (
                                        <Dropdown
                                            overlay={menu(4)}
                                            trigger={["click"]}
                                            placement="bottomRight"
                                        >
                                            <Image
                                                src="/images/more.svg"
                                                alt="More"
                                                width={20}
                                                height={20}
                                                onClick={(e) =>
                                                    e.preventDefault()
                                                }
                                                className="absolute top-3 right-2 cursor-pointer bg-[#D3E3E3] rounded-md"
                                                style={{ fontSize: "20px" }}
                                            />
                                        </Dropdown>
                                    )}
                                </label>
                            </div>
                        )}
                    </div>

                    {visibleInputs < 4 &&
                        searchParams.get("mode") !== "view" && (
                            <div className="flex justify-center items-center">
                                <Button
                                    type="primary"
                                    className="bg-white mr-2 button right-0 font-roboto text-[#67A1A3] text-[18px] w-[267px] h-[42px] font-medium rounded-xl mb-10 border-2 border-[#67A1A3]"
                                    onClick={handleAddMoreImages}
                                >
                                    {t("Add more images")}
                                </Button>
                            </div>
                        )}
                </div>
                <div className="ml-4 w-[60%]">
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px] block">
                            {t("School Name")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :
                        </h1>
                        <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={schoolName}
                            onChange={(e) => setSchoolName(e.target.value)}
                            onBlur={() => setSchoolName(schoolName.trim())}
                            placeholder={t("Enter School name")}
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("postal_code")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :
                        </h1>
                        <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={postalCode !== null ? postalCode : ""}
                            onChange={handlePostalCodeChange}
                            placeholder={t("Enter Postcode")}
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("Contact")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :
                        </h1>
                        <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={contact !== null ? contact : ""}
                            onChange={(e) => setContact(e.target.value)}
                            onBlur={() =>
                                setContact(
                                    contact !== null ? contact.trim() : ""
                                )
                            }
                            placeholder={t("Enter Contact")}
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div>
                    {/* <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">Email<span className="text-red-500 font-[400]">*</span> :</h1>
                        <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={email !== null ? email : ""}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder="Enter Contact"
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div> */}
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("location")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :
                        </h1>
                        <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={location}
                            onChange={(e) => setLocation(e.target.value)}
                            onBlur={() => setLocation(location.trim())}
                            placeholder={t("Enter Location")}
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("Location URL")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :
                        </h1>
                        <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={locationUrl}
                            onChange={(e) => setLocationUrl(e.target.value)}
                            onBlur={() => setLocationUrl(locationUrl.trim())}
                            placeholder={t("Enter Location URL")}
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("state")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :
                        </h1>
                        {/* <Select
                            className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                            placeholder="Select State"
                            value={state}
                            onChange={(value) => setState(value)}
                            onClick={(e) => {
                                if (searchParams.get("mode") === "view") {
                                    e.preventDefault();
                                }
                            }}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                            suffixIcon={
                                searchParams.get("mode") !== "view" && (
                                    <Image
                                        src="/images/arrowI.svg"
                                        alt="More"
                                        width={20}
                                        height={20}
                                    />
                                )
                            }
                        >
                            {schoolState.map((option) => (
                                <Option key={option.value} value={option.value}>
                                    {option.label}
                                </Option>
                            ))}
                        </Select> */}
                        <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={state}
                            onChange={(e) => setState(e.target.value)}
                            onBlur={() => setState(state.trim())}
                            placeholder={t("Enter State")}
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("school_type")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :{" "}
                        </h1>
                        <Select
                            className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                            placeholder="Select School Type"
                            value={selectSchoolType}
                            onChange={(value) => setSelectSchoolType(value)}
                            onClick={(e) => {
                                if (searchParams.get("mode") === "view") {
                                    e.preventDefault();
                                }
                            }}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                            suffixIcon={
                                searchParams.get("mode") !== "view" && (
                                    <Image
                                        src="/images/arrowI.svg"
                                        alt="More"
                                        width={20}
                                        height={20}
                                    />
                                )
                            }
                        >
                            {schoolType.map((option) => (
                                <Option key={option.value} value={option.value}>
                                    {option.label}
                                </Option>
                            ))}
                        </Select>
                        {/* <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={selectSchoolType}
                            onChange={(e) =>
                                setSelectSchoolType(e.target.value)
                            }
                            placeholder="Enter School Type"
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        /> */}
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("specialisation")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :{" "}
                        </h1>
                        {/* <Select
                            className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                            placeholder="Select Specification"
                            value={selectSchoolSpecifications}
                            onChange={(value) =>
                                setSelectSchoolSpecifications(value)
                            }
                            onClick={(e) => {
                                if (searchParams.get("mode") === "view") {
                                    e.preventDefault();
                                }
                            }}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                            suffixIcon={
                                searchParams.get("mode") !== "view" && (
                                    <Image
                                        src="/images/arrowI.svg"
                                        alt="More"
                                        width={20}
                                        height={20}
                                    />
                                )
                            }
                        >
                            {schoolSpecifications.map((option) => (
                                <Option key={option.value} value={option.value}>
                                    {option.label}
                                </Option>
                            ))}
                        </Select> */}
                        <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={selectSchoolSpecifications}
                            onChange={(e) =>
                                setSelectSchoolSpecifications(e.target.value)
                            }
                            onBlur={() =>
                                setSelectSchoolSpecifications(
                                    selectSchoolSpecifications.trim()
                                )
                            }
                            placeholder={t("Enter Specification")}
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("supervision")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :{" "}
                        </h1>
                        <Select
                            className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                            placeholder={t("Select Supervision")}
                            value={selectSchoolSupervision}
                            onChange={(value) =>
                                setSelectSchoolSupervision(value)
                            }
                            onClick={(e) => {
                                if (searchParams.get("mode") === "view") {
                                    e.preventDefault();
                                }
                            }}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                            suffixIcon={
                                searchParams.get("mode") !== "view" && (
                                    <Image
                                        src="/images/arrowI.svg"
                                        alt="More"
                                        width={20}
                                        height={20}
                                    />
                                )
                            }
                        >
                            {schoolSupervision.map((option) => (
                                <Option
                                    key={String(option.value)}
                                    value={option.value}
                                >
                                    {option.label}
                                </Option>
                            ))}
                        </Select>
                        {/* <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={selectSchoolSupervision}
                            onChange={(e) =>
                                setSelectSchoolSupervision(e.target.value)
                            }
                            placeholder="Enter Supervision"
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        /> */}
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("boarding_school")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :{" "}
                        </h1>
                        <Select
                            className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                            placeholder={t("Yes/No")}
                            value={isBoarding}
                            onChange={(value) => setIsBoarding(value)}
                            onClick={(e) => {
                                if (searchParams.get("mode") === "view") {
                                    e.preventDefault();
                                }
                            }}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                            suffixIcon={
                                searchParams.get("mode") !== "view" && (
                                    <Image
                                        src="/images/arrowI.svg"
                                        alt="More"
                                        width={20}
                                        height={20}
                                    />
                                )
                            }
                        >
                            {schoolBoardingType.map((option) => (
                                <Option key={option.label} value={option.value}>
                                    {option.label}
                                </Option>
                            ))}
                        </Select>
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            {t("description")}
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :
                        </h1>
                        {/* <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            placeholder="Enter Description"
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        /> */}
                        <TextArea
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            value={description}
                            onChange={handleDescriptionChange}
                            onBlur={handleDescriptionBlur}
                            placeholder={t("Enter Description")}
                            onClick={(e) => {
                                if (searchParams.get("mode") === "view") {
                                    e.preventDefault();
                                }
                            }}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div>
                    <div className="flex items-center mb-4">
                        <h1 className="w-[160px]">
                            Link
                            {searchParams.get("mode") !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :
                        </h1>
                        <Input
                            className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                            type="text"
                            value={link}
                            // onChange={(e) => setLink(e.target.value)}
                            onChange={(e) => setLink(e.target.value)}
                            onBlur={() => setLink(link.trim())}
                            placeholder={t("Enter School official link")}
                            readOnly={searchParams.get("mode") === "view"}
                            style={{
                                pointerEvents:
                                    searchParams.get("mode") === "view"
                                        ? "none"
                                        : "auto",
                            }}
                        />
                    </div>
                    {/* <div className="flex justify-end mt-10">
                        {searchParams.get("mode") !== "view" && (
                            <>
                                {searchParams.get("mode") === "edit" ? (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] mr-2 shadow-inner button right-0 font-roboto text-white text-[18px] w-[257px] h-[46px] font-medium rounded-xl"
                                        onClick={() =>
                                            handleEditSchool(
                                                searchParams.get("id") as string
                                            )
                                        }
                                        loading={loading}
                                    >
                                        Save
                                    </Button>
                                ) : (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] mr-2 shadow-inner button right-0 font-roboto text-white text-[18px] w-[257px] h-[46px] font-medium rounded-xl"
                                        onClick={handleAddHighSchool}
                                        loading={loading}
                                    >
                                        Save
                                    </Button>
                                )}
                            </>
                        )}
                    </div> */}
                </div>
            </div>
        </div>
    );
};

export default AddHighSchool;

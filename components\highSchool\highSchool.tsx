"use client";
import React, { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import {
    Dropdown,
    Radio,
    Table,
    Menu,
    MenuProps,
    Avatar,
    Spin,
    Input,
    message,
    Modal,
    Button,
    Space,
    Select,
    Col,
    Empty,
} from "antd";
import {
    addSchoolQueryParams,
    highSchoolDetail,
    deleteSchoolById,
    getAllSchool,
    highSchoolFile,
    importExcelFileForHighSchool,
} from "@/src/services/highSchool.api";
import {
    schoolState,
    schoolSupervision,
    schoolSpecifications,
    schoolType,
    schoolBoardingType,
} from "@/src/libs/constants";
import type { ColumnsType } from "antd/es/table";
import { Pop<PERSON><PERSON>, Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { CircularProgress } from "@mui/material";
import highSchoolStyle from "./highSchool.module.css";
import { debounce } from "lodash";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface highSchoolData {
    srNo: number;
    photo: string;
    schoolName: string;
    postalCode: number;
    location: string;
    state: string;
    schoolType: string;
    specialisations: string;
    Supervision: string;
    boardingSchool: boolean;
    id?: string;
    picture?: String[] | null;
}

const HighSchool = () => {
    const [searchQuery, setSearchQuery] = useState("");
    const [recordToDelete, setRecordToDelete] = useState<any>(null);
    const [pageSize, setPageSize] = useState<number>(20);
    const [total, setTotal] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [data, setData] = useState<highSchoolData[]>([]);
    const [response, setResponse] = useState<any>(null);
    const [isTabledataLoading, setIsabledataLoading] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] =
        useState<boolean>(false);
    const [isDelete, setIsDelete] = useState(false);
    const [isImportModalVisible, setImportModalVisible] = useState(false);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [debouncedSearchQuery, setDebouncedSearchQuery] =
        useState(searchQuery);
    const [debouncedSearchQueryIsChange, setDebouncedSearchQueryIsChange] =
        useState(false);
    const router = useRouter();
    const { t } = useTranslation();

    const handleSearch = useCallback(
        debounce((query: any) => {
            setDebouncedSearchQuery(query);
        }, 500),
        []
    );

    const handleInputChange = (e: any) => {
        setSearchQuery(e.target.value);
        handleSearch(e.target.value);
    };

    useEffect(() => {
        fetchData(currentPage);
        setDebouncedSearchQueryIsChange(false);
    }, [currentPage, pageSize, debouncedSearchQueryIsChange]);

    useEffect(() => {
        setCurrentPage(1);
        setDebouncedSearchQueryIsChange(true);
    }, [debouncedSearchQuery]);

    const fetchData = async (page: number = 1) => {
        // setIsLoading(true);
        setIsabledataLoading(true);
        const queryParams: addSchoolQueryParams = {
            skip: (page - 1) * pageSize,
            take: pageSize,
            search_column: "name",
            search: debouncedSearchQuery,
            orderBy: "createdAt|asc",
        };
        const authorization = localStorage.getItem("idToken");
        try {
            const response = await getAllSchool(queryParams, authorization);
            setResponse(response);
            if (response) {
                const updatedData = response.list.map(
                    (item: any, index: any) => ({
                        ...item,
                        srNo: (page - 1) * pageSize + index + 1,
                    })
                );
                setData(updatedData);
                setTotal(response.total);
            }
        } catch (error) {
            message.error(t("School data could not be retrieved"));
        }
        setIsabledataLoading(false);
    };

    const handleMenuClick = async (e: any, record: any) => {
        setRecordToDelete(record.id);
        e.domEvent.stopPropagation();
        const { key } = e;
        if (key === "view") {
            router.push(`/highSchool/addHighSchool?mode=view&id=${record.id}`);
        } else if (key === "edit") {
            router.push(`/highSchool/addHighSchool?mode=edit&id=${record.id}`);
        } else if (key === "delete") {
            setIsDeleteModalVisible(true);
        }
    };

    const handleDeleteConfirm = async (id: string) => {
        try {
            setIsDelete(true);
            const authorization = localStorage.getItem("idToken");
            const response = await deleteSchoolById(id, authorization);
            if (response) {
                setIsDeleteModalVisible(false);
                fetchData(currentPage);
                message.success(t("School deleted successfully")); //School deleted successfully
                setIsDelete(false);
            }
        } catch (error) {
            message.error(t("Failed to delete school")); //Failed to delete school
            setIsDelete(false);
            setIsDeleteModalVisible(true);
        }
    };

    const menu = (record: any) => (
        <Menu onClick={(e) => handleMenuClick(e, record)}>
            <Menu.Item key="view">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/eye.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    {t("View")}
                </div>
            </Menu.Item>
            <Menu.Item key="edit">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[10px] mb-1"
                    />
                    {t("Edit")}
                </div>
            </Menu.Item>
            <Menu.Item key="delete">
                <div className="flex text-[14px] font-roboto text-[#F1676D]">
                    <Image
                        src="/images/trash.svg"
                        alt="visibility"
                        width={23}
                        height={23}
                        className="cursor-pointer mr-[7px] mb-1"
                    />
                    {t("delete")}
                </div>
            </Menu.Item>
        </Menu>
    );

    const highSchoolTableColumns: ColumnsType<highSchoolData> = [
        {
            title: <div className="ml-2">{t("Sr. No.")}</div>,
            dataIndex: "srNo",
            key: "srNo",
            align: "left",
            className: `${roboto.className}`,
            width: 80,
            render: (srNo: string) => <div className="pl-[20px]">{srNo}.</div>,
        },
        {
            title: t("Image"),
            dataIndex: "picture",
            key: "picture",
            align: "center",
            width: 80,
            render: (picture: string) => (
                <div>
                    {picture === null || picture.length === 0 ? (
                        <Avatar
                            size="large"
                            className="shadow-lg custom-avatar-size"
                            icon={
                                <Image
                                    src="/images/sideBarM/school.svg"
                                    alt="school"
                                    width={12}
                                    height={12}
                                    style={{ width: "25px", height: "25px" }}
                                />
                            }
                        />
                    ) : (
                        <Avatar
                            size="large"
                            src={picture[0]}
                            alt="Profile"
                            className="border-none shadow-lg custom-avatar-size"
                        />
                    )}
                </div>
            ),
            className: `${roboto.className}`,
        },
        {
            title: t("School Name"),
            dataIndex: "name",
            key: "name",
            align: "center",
            className: `${roboto.className}`,
            render: (schoolName) => (
                <div className="text-center truncate w-full">
                    {schoolName ? schoolName : "-"}
                </div>
            ),
        },
        {
            title: t("postal_code"),
            dataIndex: "postCode",
            key: "postCode",
            align: "center",
            width: 100,
            className: `${roboto.className}`,
            render: (postcode) => (
                <div className="text-center">{postcode ? postcode : "-"}</div>
            ),
        },
        {
            title: t("location"),
            dataIndex: "location",
            key: "location",
            align: "center",
            className: `${roboto.className}`,
            render: (location) => (
                <div className="text-center ml-[14px]">
                    {location ? location : "-"}
                </div>
            ),
        },
        {
            title: t("state"),
            dataIndex: "state",
            key: "state",
            align: "center",
            width: 100,
            className: `${roboto.className}`,
            render: (state) => (
                <div className="text-center ml-[14px]">
                    {state ? state : "-"}
                </div>
            ),
        },
        {
            title: t("school_type"),
            dataIndex: "schoolType",
            key: "schoolType",
            align: "center",
            width: 100,
            className: `${roboto.className}`,
            render: (type) => {
                const typeLabel =
                    schoolType.find((item) => item.value === type)?.label ||
                    "-";
                return (
                    <div className="ml-[14px]">
                        {typeLabel === "-" ? type : typeLabel}
                    </div>
                );
            },
        },
        {
            title: t("specialisations"),
            dataIndex: "specialization",
            key: "specialization",
            align: "center",
            width: 150,
            className: `${roboto.className}`,
            render: (specialization) => {
                return (
                    <div className="truncate w-full text-center ml-[14px]">
                        {specialization || "-"}
                    </div>
                );
            },
        },
        {
            title: t("supervision"),
            dataIndex: "afternoonCare",
            key: "afternoonCare",
            align: "center",
            width: 100,
            className: `${roboto.className}`,
            render: (afternoonCare) => {
                const specificationLabel =
                    schoolSupervision.find(
                        (item) => item.value === afternoonCare
                    )?.label || "-";
                return (
                    <div className="ml-[14px]">
                        {specificationLabel === "-"
                            ? afternoonCare === true
                                ? "Yes"
                                : "No"
                            : specificationLabel}
                    </div>
                );
            },
        },
        {
            title: t("boarding_school"),
            dataIndex: "boardingSchool",
            key: "boardingSchool",
            align: "center",
            width: 120,
            className: `${roboto.className}`,
            render: (boardingSchool) => (
                <div className="text-center ml-[14px]">
                    {boardingSchool === true ? "Yes" : "No"}
                </div>
            ),
        },
        {
            title: t("More"),
            dataIndex: "more",
            key: "more",
            align: "center",
            className: `${roboto.className}`,
            width: 80,
            render: (more: string, record: highSchoolData) => {
                return record.id ? (
                    <Dropdown overlay={menu(record)} trigger={["click"]}>
                        <div
                            className="flex justify-center ml-[14px]"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <Image
                                src="/images/more.svg"
                                alt="More"
                                width={20}
                                height={20}
                                className="cursor-pointer"
                                onClick={(e) => e.preventDefault()}
                            />
                        </div>
                    </Dropdown>
                ) : (
                    <CircularProgress size={20} />
                );
            },
        },
    ];

    const paginationConfig = {
        pageSize: pageSize,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            const selectedFile = e.target.files[0];
            if (
                selectedFile.type !==
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" &&
                selectedFile.type !== "application/vnd.ms-excel"
            ) {
                message.error(t("Only Excel files are allowed"));
                return;
            }
            setSelectedFile(selectedFile);
        }
    };

    // Handle file upload for school
    const handleFileUploadForSchool = async () => {
        if (selectedFile === null) {
            message.error(t("First upload an Excel file"));
            return;
        }

        if (
            selectedFile.type !==
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" &&
            selectedFile.type !== "application/vnd.ms-excel"
        ) {
            message.error(t("Select Excel File Only."));
            return;
        }

        try {
            setIsUploading(true);
            const file = selectedFile;
            const importFileRes = await importExcelFileForHighSchool(file);
            if (importFileRes) {
                setIsUploading(false);
                if (
                    importFileRes.message.includes(
                        "Provided data has been created Successfully"
                    )
                ) {
                    message.success(importFileRes.message);
                    fetchData(currentPage);
                } else if (importFileRes.message.includes("Provided")) {
                    message.warning(importFileRes.message);
                } else if (importFileRes.message.includes("Given")) {
                    message.warning(importFileRes.message);
                } else {
                    message.warning(importFileRes.message);
                }
                setImportModalVisible(false);
                setSelectedFile(null);
            }
        } catch (error) {
            message.error(t("Failed to add data of school")); // Failed to add data of school
            setIsUploading(false);
        }
    };

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`${roboto.className} w-full bg-white text-[24px] text-black items-center mt-4`}
            >
                <div className="flex items-center justify-between px-4 mb-2">
                    <div
                        className={`flex items-center text-[26px] font-[600] ${roboto.className}`}
                    >
                        {t("Schools")}
                        <span
                            className={`text-gray-400 mt-[2px] text-[18px] ml-2 font-[500] ${roboto.className}`}
                        >
                            ({total})
                        </span>
                    </div>
                    <div className="text-[25px] text-black flex items-center space-x-3">
                        <div className="text-[16px] font-normal text-black flex items-center px-4 relative">
                            <input
                                type="text"
                                placeholder={t("search")}
                                className="bg-[#D9D9D9] bg-opacity-[20%] pl-10 w-[350px] py-2 rounded-lg h-10"
                                value={searchQuery}
                                onChange={handleInputChange}
                            />
                            <Image
                                src="/images/search_1.svg"
                                alt="toggle dropdown"
                                width={22}
                                height={22}
                                className="absolute left-7 top-1/2 transform -translate-y-1/2"
                            />
                        </div>
                        <button
                            className="rounded-xl h-[40px] text-[16px] px-2 bg-white text-[#67A1A3] border-[2px] border-[#A3CBC1] flex items-center space-x-2"
                            onClick={() => {
                                setImportModalVisible(true);
                            }}
                        >
                            <span className="flex">
                                {t("Import from Excel")}
                            </span>
                        </button>
                        <button
                            className="rounded-xl shadow-inner w-[150px] h-[40px] font-[500] text-[16px] px-2 bg-[#67A1A3] text-white flex justify-center items-center space-x-2"
                            onClick={() =>
                                router.push("/highSchool/addHighSchool")
                            }
                        >
                            <span className="flex">{t("Add School")}</span>
                        </button>
                    </div>
                </div>
                {isTabledataLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <div>
                        <Table
                            columns={highSchoolTableColumns}
                            dataSource={data}
                            pagination={{
                                ...paginationConfig,
                                className: "custom-pagination custom-select",
                            }}
                            bordered={false}
                            rowKey="srNo"
                            className={`custom-table ${
                                data.length > 5
                                    ? "custom-table-scroll"
                                    : "custom-table-scroll-hide"
                            } scrollbar font-[400]`}
                            rowClassName="custom-table-row"
                            loading={{
                                indicator: <Spin size="default" />,
                                spinning: isTabledataLoading,
                            }}
                            onRow={(record) => {
                                return {
                                    onClick: () => {
                                        if (record && record.picture === null) {
                                            router.push(
                                                `/highSchool/addHighSchool?mode=edit&id=${record.id}`
                                            );
                                        } else {
                                            router.push(
                                                `/highSchool/addHighSchool?mode=view&id=${record.id}`
                                            );
                                        }
                                    },
                                };
                            }}
                            scroll={{
                                y: "67vh",
                            }}
                            locale={{
                                emptyText: (
                                    <div
                                        className={`h-[61vh] flex items-center justify-center font-roboto font-[500] text-gray-500`}
                                    >
                                        <Empty
                                            description="No data available"
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        />
                                    </div>
                                ),
                            }}
                        />
                    </div>
                )}
            </div>
            <Modal
                open={isDeleteModalVisible}
                onCancel={() => setIsDeleteModalVisible(false)}
                footer={null}
                centered
                width={300} // Adjust the width as per your requirement (number or string)
                style={{ textAlign: "center" }}
                closable={false}
            >
                <Image
                    src="/images/modalDelete.svg"
                    alt="Delete"
                    width={100}
                    height={100}
                    className="mx-auto bg-white -mt-3"
                />

                <div className=" flex justify-center items-center w-full">
                    <h2 className="text-[16px] font-roboto font-[400] mb-8 w-[80%] flex justify-center items-center">
                        {t("Are you sure you want to delete this school?")}
                    </h2>
                </div>
                <div className="flex justify-center space-x-4">
                    <Button
                        onClick={() => setIsDeleteModalVisible(false)}
                        className="bg-transparent cancelbutton font-roboto text-[#F1676D] text-[18px] h-[46px] border-[1px] border-[#F1676D] w-[116px] font-medium rounded-xl"
                    >
                        {t("cancel")}
                    </Button>
                    <Button
                        type="primary"
                        className="bg-[#F1676D] shadow-inner deleteButton font-roboto text-white text-[18px] w-[116px] h-[46px] font-medium rounded-xl"
                        onClick={() => handleDeleteConfirm(recordToDelete)}
                        loading={isDelete}
                    >
                        {t("delete")}
                    </Button>
                </div>
            </Modal>
            <Modal
                open={isImportModalVisible}
                onCancel={() => {
                    setImportModalVisible(false);
                }}
                footer={null}
                className={`rounded-lg border-black ${highSchoolStyle.modal}`}
                centered
                width={420}
                style={{
                    textAlign: "center",
                }}
                closable={false}
            >
                <div
                    className={`text-[20px] w-full font-[500] flex justify-center items-center mb-5 ${roboto.className}`}
                >
                    <p className="w-[85%] ">
                        {t("Import High School Excel File")}
                    </p>
                </div>
                <div className="flex justify-between items-center w-full mt-5">
                    <label className="text-[16px] flex justify-start font-roboto font-semibold w-[70px]">
                        File :
                    </label>
                    <label
                        htmlFor="inputFile"
                        className="flex rounded-md h-[42px] w-full justify-center items-center cursor-pointer border-[#D3E3E3] bg-[#F6FAF9]"
                        style={{
                            border: "1px solid #D3E3E3",
                            position: "relative",
                        }}
                    >
                        <Input
                            id="inputFile"
                            type="file"
                            accept=".xlsx, .xls, .csv"
                            style={{ display: "none" }}
                            onChange={handleFileChange}
                        />
                        <p
                            className={`font-roboto text-[14px] ${
                                selectedFile ? "text-black" : "text-gray-300"
                            }`}
                        >
                            {selectedFile ? selectedFile.name : "Add File"}
                        </p>
                    </label>
                </div>
                <div className="mt-8 flex justify-center space-x-4">
                    <Button
                        type="primary"
                        onClick={() => {
                            setImportModalVisible(false);
                            setSelectedFile(null);
                        }}
                        style={{
                            backgroundColor: "white",
                            color: "#67A1A3",
                        }}
                        className={`w-[50%] h-[46px] text-[16px] leading-[16px] rounded-xl font-[500] bg-white text-[#67A1A3] border border-[#67A1A3] ${roboto.className}`}
                    >
                        {t("cancel")}
                    </Button>
                    <Button
                        type="primary"
                        className={`${highSchoolStyle.button} bg-[#67A1A3] shadow-inner text-white text-[16px] w-[50%] h-[46px] font-[500] rounded-xl ${roboto.className}`}
                        onClick={handleFileUploadForSchool}
                        loading={isUploading}
                    >
                        {t("Upload File")}
                    </Button>
                </div>
            </Modal>
        </div>
    );
};

export default HighSchool;

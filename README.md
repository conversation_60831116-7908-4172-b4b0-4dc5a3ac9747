This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:1465](http://localhost:1465) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

-   [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
-   [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

# **How to Setup Project**

<p>
<b vertical-align="middle">Download Visual studio code:</b> <a href="https://code.visualstudio.com/download" target="_blank"><img height="24" width="24" style="vertical-align: middle;" src="https://upload.wikimedia.org/wikipedia/commons/thumb/9/9a/Visual_Studio_Code_1.35_icon.svg/768px-Visual_Studio_Code_1.35_icon.svg.png?20210804221519"></a></p>

<!-- <p><b>Download Laragon:</b> <a href="https://laragon.org/download/index.html" target="_blank"><img src="https://laragon.org/logo.svg" style="vertical-align: middle;"></a></p> -->

## FLOW FOR FRONTEND CODE

1. Open the terminal of the laragon you have installed by above steps.
2. Go to the folder where you want to setup your project.
3. Write command `git clone {Repository name}` and hit Enter, repo folder will be created in your local system.
4. Run Command `yarn install`, It will take some time to install yarn packages those are available in package.json File.
5. After complete installation make sure the files .env and .env.local must be there into root directory.
6. Create .env and .env.local files and Copy and Paste below code into it.

```
    HOSTNAME=localhost
    PORT=1465
    NEXT_URL=http://localhost:${PORT}
    NEXT_ENDPOINT=http://${HOSTNAME}:${PORT}
    NEXT_API_ENDPOINT=https://api.gymi.org
```

7. Run command `yarn run dev`, You will find the URL into output of above command to run into the browser like below.

```
    yarn run v1.22.21
    $ next dev -p 1465
    ▲ Next.js 14.2.3
    - Local:        http://localhost:1465
    - Environments: .env

    ✓ Starting...
```

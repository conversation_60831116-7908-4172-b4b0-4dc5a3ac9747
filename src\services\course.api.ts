import { fetch } from "@/src/libs/helpers";

export interface courseQueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    search_column?: string;
    categoryId?: string | null;
    videoId?: string;
    status?: string;
}

export interface courseBody {
    category?: string;
    categoryId?: string;
    title?: string;
    videoUrl?: string;
    thumbnailUrl?: string;
    description?: string;
    videoId?: string;
    status?: string;
    newPosition?: number;
}

export const getAllCategory = async (
    params: courseQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/get-all-categories`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getAllTrainingCourse = async (
    params: courseQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/get-all-videos`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getAllTutrialsForUsers = async (
    params: courseQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/get-all-videos-user`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateCategory = async (
    categoryId: string,
    params: courseBody,
    payload: courseBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/${categoryId}/update-category`,
        method: "PUT",
        data: payload,
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateCourse = async (
    videoId: string,
    params: courseQueryParams,
    payload: courseBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/${videoId}/update-video`,
        method: "PUT",
        data: payload,
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateCategoryStatus = async (
    categoryId: string,
    params: courseQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/${categoryId}/change-category-status`,
        method: "PUT",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateCourseStatus = async (
    videoId: string,
    params: courseBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/${videoId}/change-video-status`,
        method: "PUT",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const createCategoryForCourse = async (
    payload: courseBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/add-category`,
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const createTrainingCourse = async (
    payload: courseBody,
    param: courseQueryParams,
    // categoryId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/add-video`,
        method: "POST",
        data: payload,
        params: param,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deleteCourse = async (
    videoId: string,
    queryParams: courseQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/${videoId}/delete-blog`,
        method: "DELETE",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const reorderCourse = async (
    payload: courseBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-course/reorder-video`,
        method: "PUT",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

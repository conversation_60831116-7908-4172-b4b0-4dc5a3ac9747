"use client";
import { usePathname } from "next/navigation";
import Sidebar from "@/components/sideBar/sideBar";
import { useEffect } from "react";
import { initFirebase } from "@/firebase.config";

export default function ClientLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const pathname = usePathname();
    const isLoginPage = pathname === "/";
    const isResetPasswordPage = pathname === "/resetPassword";
    let isPathAvailable = false;
    const staticPaths = [
        "/users",
        "/knowledgeRally",
        "/knowledgeRally/addQuestion",
        "/mails",
        "/highSchool",
        "/highSchool/addHighSchool",
        "/promotion",
        "/promotion/addPromotion",
        "/trainingMaterial",
        "/blog",
        "/training-course",
        "/manage-grade",
        "/upload-document",
        "/videoTutorial",
        "/broadcast-message",
        "/subscribedEmail",
    ];

    if (staticPaths.includes(pathname)) {
        isPathAvailable = true;
    }

    useEffect(() => {
        initFirebase();
    }, []);

    if (isLoginPage || isResetPasswordPage || !isPathAvailable) {
        return children;
    }

    return (
        <>
            <div className="flex">
                <Sidebar />
                <div className="flex flex-col w-[calc(100vw-260px)]">
                    <div className="w-full">{children}</div>
                </div>
            </div>
        </>
    );
}

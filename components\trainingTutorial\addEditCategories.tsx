"use client";
import React, { useState, useEffect } from "react";
import Navbar from "../Navbar/navbar";
import "@/app/globals.css";
import Image from "next/image";
import { Table, Spin, Button, message, Modal, Input, Empty } from "antd";
import type { ColumnsType } from "antd/es/table";
import {
    getAllCategory,
    updateCategoryStatus,
    courseQueryParams,
    createCategoryForCourse,
    updateCategory,
    courseBody,
} from "@/src/services/course.api";
import { CircularProgress, Switch } from "@mui/material";
import { Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import modalStyle from "./course.module.css";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface categoriesData {
    category: string;
    id: string;
    status: string;
}

const AddEditCategories = ({
    setIsAddEditCategory,
    isAddEditCategory,
}: any) => {
    const [isLoading, setIsLoading] = useState(false);
    const [isAddEditCategoryModal, setIsAddEditCategoryModal] = useState(false);
    const [isButtonLoading, setButtonIsLoading] = useState(false);
    const [categoryName, setCategoryName] = useState("");
    const [categoryId, setCategoryId] = useState("");
    const [data, setData] = useState<any[]>([]);
    const [mode, setMode] = useState<any>(null);
    const [total, setTotal] = useState<number>(0);
    const [response, setResponse] = useState<any>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [isTabledataLoading, setIsTabledataLoading] = useState(false);
    const [pageSize, setPageSize] = useState<number>(20);
    const { t } = useTranslation();

    const router = useRouter();

    useEffect(() => {
        fetchData(currentPage);
    }, [currentPage, pageSize]);

    const fetchData = async (page: number = 1) => {
        const authorization = localStorage.getItem("idToken");

        try {
            setIsTabledataLoading(true);
            const queryParams: courseQueryParams = {
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: "createdAt|desc",
                status: "ALL",
            };
            const response = await getAllCategory(queryParams, authorization);
            setResponse(response);
            if (response) {
                const updatedData = response.list.map(
                    (item: any, index: any) => ({
                        ...item,
                        srNo: (page - 1) * pageSize + index + 1,
                    })
                );
                setData(updatedData);
                setTotal(response.total);
            } else {
                message.error(t("Empty response data"));
            }
        } catch (error) {
            message.error(t("Data could not be retrieved"));
        }
        setIsTabledataLoading(false);
    };

    const updateStatusOfCategories = async (
        categoryId: string,
        recpord: any
    ) => {
        try {
            const idToken: any = localStorage.getItem("idToken");
            const newStatus =
                recpord?.status === "ENABLED" ? "DISABLED" : "ENABLED";
            const queryParams: courseBody = {
                categoryId: categoryId,
                status: newStatus,
            };
            const res = await updateCategoryStatus(
                categoryId,
                queryParams,
                idToken
            );
            if (res) {
                setData((data: any) =>
                    data.map((category: any, i: any) =>
                        categoryId === category?.id
                            ? { ...category, status: newStatus }
                            : category
                    )
                );
            }
        } catch (error) {
            message.error("Error updating category status!");
        }
    };

    const addNewCategory = async () => {
        try {
            setIsLoading(true);
            const idToken: any = localStorage.getItem("idToken");
            const payload: courseBody = {
                category: categoryName,
            };
            const res = await createCategoryForCourse(payload, idToken);
            if (res) {
                fetchData(currentPage);
                message.success(
                    `${categoryName} category is created successfully!`
                );
                setIsLoading(false);
                setIsAddEditCategoryModal(false);
                setCategoryName("");
                setCategoryId("");
            }
        } catch (error) {
            message.error("Error in Creating category!");
            setIsLoading(false);
        }
    };

    const editCategory = async () => {
        try {
            setIsLoading(true);
            const idToken: any = localStorage.getItem("idToken");
            const payload: courseBody = {
                category: categoryName,
            };
            const params: courseBody = {
                categoryId: categoryId,
            };
            const res = await updateCategory(
                categoryId,
                params,
                payload,
                idToken
            );
            if (res) {
                fetchData(currentPage);
                message.success(
                    `${categoryName} category is edited successfully!`
                );
                setIsLoading(false);
                setIsAddEditCategoryModal(false);
                setCategoryName("");
                setCategoryId("");
            }
        } catch (error) {
            message.error("Error in updating category!");
            setIsLoading(false);
        }
    };

    const categoriesDataColumns: ColumnsType<categoriesData> = [
        {
            title: <div className="ml-2">{t("Sr. No.")}</div>,
            dataIndex: "srNo",
            key: "srNo",
            align: "start",
            className: `${roboto.className}`,
            width: 80,
            render: (srNo) => <div className="pl-[18px]">{srNo}.</div>,
        },
        {
            title: "Category",
            dataIndex: "category",
            key: "category",
            align: "center",
            className: `${roboto.className}`,
            render: (email) => (
                <div className="px-[20px] flex justify-center">
                    <p className="text-left">{email}</p>
                </div>
            ),
        },
        {
            title: "Total Video",
            dataIndex: "videoCount",
            key: "videoCount",
            align: "center",
            className: `${roboto.className}`,
            render: (videoCount) => (
                <div className="px-[20px] flex justify-center">
                    <p className="text-left">{videoCount}</p>
                </div>
            ),
        },
        {
            title: "Last Updated at",
            dataIndex: "updatedAt",
            key: "updatedAt",
            align: "center",
            className: `${roboto.className}`,
            render: (updatedAt) => {
                const date = new Date(updatedAt);
                const formattedDate = date.toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "short",
                    year: "2-digit",
                });
                const formattedTime = date.toLocaleTimeString("en-GB", {
                    hour: "2-digit",
                    minute: "2-digit",
                });
                return (
                    <div className="px-[20px] flex justify-center">
                        <p className="text-left">
                            {formattedDate} : {formattedTime}
                        </p>
                    </div>
                );
            },
        },
        {
            title: "Edit",
            dataIndex: "",
            key: "",
            align: "center",
            className: `${roboto.className}`,
            width: "300px",
            render: (file) => {
                return (
                    <div className="flex justify-center items-center">
                        {file ? (
                            <Image
                                src="/images/edit.svg"
                                alt="File available"
                                width={20}
                                height={20}
                                className="cursor-pointer"
                                onClick={() => {
                                    setIsAddEditCategoryModal(true);
                                    setCategoryName(file.category);
                                    setCategoryId(file.id);
                                    setMode("edit");
                                }}
                            />
                        ) : (
                            "-"
                        )}
                    </div>
                );
            },
        },
        {
            title: "Status",
            dataIndex: "status",
            key: "status",
            align: "center",
            width: 150,
            render: (status: string, record: categoriesData) => (
                <div className="flex justify-center items-center cursor-pointer">
                    {record.id ? (
                        <Switch
                            // className="no-pointer-cursor"
                            checked={status.toLowerCase() === "enabled"}
                            onClick={(e) => e.stopPropagation()}
                            onChange={() =>
                                updateStatusOfCategories(record.id, record)
                            }
                            color="primary"
                            sx={{
                                "& .MuiSwitch-switchBase": {
                                    color: "#CBCBCB",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked": {
                                    color: "#67A1A3",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                                    {
                                        backgroundColor: "#67A1A3",
                                    },
                                "& .MuiSwitch-track": {
                                    backgroundColor:
                                        "rgba(211, 217, 231, 0.80)",
                                },
                            }}
                            size="medium"
                        />
                    ) : (
                        <CircularProgress size={20} />
                    )}
                </div>
            ),
            className: `${roboto.className}`,
        },
    ];

    const paginationConfig = {
        pageSize: pageSize,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    return (
        <div className={`${roboto.className} flex flex-col`}>
            <div
                className={`${roboto.className} shadow-md text-[25px] text-black font-bold h-auto flex justify-between items-center px-4 py-3 z-10 bg-white`}
            >
                <div>
                    <h1 className="text-[24px]">
                        {t("List of Tutorial Categories")}
                    </h1>
                </div>
                <div className="text-[20px] font-roboto text-black h-auto flex items-center px-4">
                    <Image
                        src="/images/cross.svg"
                        alt="cross"
                        width={28}
                        height={28}
                        className="cursor-pointer mr-[10px]"
                        onClick={() => setIsAddEditCategory(!isAddEditCategory)}
                    />
                </div>
            </div>
            <div className="font-roboto w-full bg-white text-[24px] text-black items-center px-4">
                <div className={`flex justify-between items-center mt-4`}>
                    <div className="flex items-center">
                        <div
                            className={`${roboto.className} flex items-center font-semibold text-[23px]`}
                        >
                            {t("Categories")}
                        </div>
                        <span className="text-gray-400 font-semibold text-[18px] font-roboto ml-1 mt-[2px]">
                            ({total})
                        </span>
                    </div>
                    <div className="text-[25px] text-black flex items-center space-x-3">
                        <Button
                            type="primary"
                            size="large"
                            onClick={() => {
                                setIsAddEditCategoryModal(true);
                                setMode("add");
                            }}
                            style={{ backgroundColor: "#67A1A3" }}
                            className="w-[150px] h-[40px] font-medium font-roboto bg-[#67A1A3] text-white shadow-inner"
                            loading={isButtonLoading}
                        >
                            {t("Add Category")}
                        </Button>
                    </div>
                </div>
                {isTabledataLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <>
                        <div className="mt-[26px]">
                            <Table
                                columns={categoriesDataColumns}
                                dataSource={data}
                                pagination={{
                                    ...paginationConfig,
                                    className:
                                        "custom-pagination custom-select",
                                }}
                                bordered={false}
                                rowKey="srNo"
                                className={`custom-table ${
                                    data.length > 5
                                        ? "custom-table-scroll"
                                        : "custom-table-scroll-hide"
                                }  scrollbar font-[400]`}
                                loading={{
                                    indicator: <Spin size="default" />,
                                    spinning: isTabledataLoading,
                                }}
                                scroll={{
                                    y: "66vh",
                                }}
                                locale={{
                                    emptyText: (
                                        <div
                                            className={`h-[61vh] flex items-center justify-center font-roboto font-[500]`}
                                        >
                                            <Empty
                                                description={t(
                                                    "No data available"
                                                )}
                                                image={
                                                    Empty.PRESENTED_IMAGE_SIMPLE
                                                }
                                            />
                                        </div>
                                    ),
                                }}
                            />
                        </div>
                    </>
                )}
            </div>
            <Modal
                title={
                    <div
                        className={`md:text-[20px] text-[16px] font-[500] text-center ${roboto.className}`}
                    >
                        {mode === "add"
                            ? t("Create Category")
                            : t("Edit Category")}
                    </div>
                }
                width={383}
                open={isAddEditCategoryModal}
                footer={null}
                closable={false}
                centered
            >
                <>
                    <div className="flex flex-col mt-5">
                        <Input
                            placeholder={t("Enter Category name")}
                            value={categoryName}
                            onChange={(e) => setCategoryName(e.target.value)}
                            onBlur={() => setCategoryName(categoryName.trim())}
                            readOnly={isLoading}
                            className={`rounded-lg shadowed-element h-[52px] w-full border-[#67A1A3] border-opacity-30 bg-[#F6FAF9] text-black text-[18px] font-[400] placeholder:text-[#8E9292] placeholder:font-[400] placeholder:text-opacity-50 placeholder:text-[16px] ${roboto.className}`}
                        />
                        <div className="flex justify-center gap-3 mt-6">
                            <Button
                                onClick={() => {
                                    setIsAddEditCategoryModal(false);
                                    setCategoryName("");
                                    setCategoryId("");
                                }}
                                className={`${modalStyle.button} bg-transparent text-[#67A1A3] text-[18px] h-[46px] border-[1px] border-[#67A1A3] w-full font-medium rounded-xl ${roboto.className}`}
                            >
                                {t("cancel")}
                            </Button>
                            <Button
                                type="primary"
                                className={`${modalStyle.button} bg-[#67A1A3] shadow-inner font-roboto text-white text-[18px] w-full h-[46px] font-medium rounded-xl ${roboto.className}`}
                                onClick={() => {
                                    if (mode === "add") {
                                        addNewCategory();
                                    } else if (mode === "edit") {
                                        editCategory();
                                    }
                                }}
                                style={{
                                    backgroundColor: "#67A1A3",
                                    color: "white",
                                    boxShadow:
                                        "inset 0 0 12px 0 rgba(0, 0, 0, 0.25)",
                                    opacity:
                                        categoryName === "" || isLoading
                                            ? 0.6
                                            : 1,
                                }}
                                loading={isLoading}
                                disabled={!categoryName || isLoading}
                            >
                                {t("save")}
                            </Button>
                        </div>
                    </div>
                </>
            </Modal>
        </div>
    );
};

export default AddEditCategories;

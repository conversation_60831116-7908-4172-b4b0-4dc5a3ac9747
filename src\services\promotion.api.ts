import { fetch } from "@/src/libs/helpers";

export interface addPromotionQueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    search_column?: string;
    status?: string;
    postId?: string;
}

export interface promotionDetails {
    picture?: string;
    title?: string;
    type?: string | null;
    currency?: string | null;
    price?: number | any;
    sponsored?: boolean | null;
    startingDate?: string | any;
    expirationDate?: string | any;
    place?: string | null;
    websiteLink?: string | null;
    description?: string;
}

export const getAllPromotionPost = async (
    queryParams: addPromotionQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/promotion/fetch-all-promotions",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const addNewPromotionPost = async (
    payload: promotionDetails,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/promotion/create-promotion",
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getPromotionPostById = async (
    postId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/promotion/${postId}/fetch`,
        method: "GET",
        headers: {
            Authorization: authorization,
        },
    });
};

export const updatePromotionPostById = async (
    postId: string,
    payload: promotionDetails,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/promotion/${postId}/update-promotion`,
        method: "PUT",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updatePromotionStatusById = async (
    postId: string,
    params: addPromotionQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/promotion/${postId}/update-status`,
        method: "PUT",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deletePromotionPostById = async (
    postId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/promotion/${postId}/delete-promotion`,
        method: "DELETE",
        headers: {
            Authorization: authorization,
        },
    });
};

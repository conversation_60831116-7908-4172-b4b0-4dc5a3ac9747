import { fetch } from "@/src/libs/helpers";

export interface blogQueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    search_column?: string;
    categoryId?: string;
    blogId?: string;
    status?: string;
}

export interface blogBody {
    category?: string;
    categoryId?: string;
    mainTitle?: string;
    subTitle?: string;
    image?: string;
    description?: string;
    blogId?: string;
    website?: string;
    status?: string;
    shortDescription?: string,
    newPosition?: number,
}

export const getAllCategory = async (
    params: blogQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/get-all-categories`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getAllBlogs = async (
    params: blogQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/get-all-blogs`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getAllBlogsForUsers = async (
    params: blogQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/get-all-blogs-user`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateCategory = async (
    categoryId: string,
    params: blogBody,
    payload: blogBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/${categoryId}/update-category`,
        method: "PUT",
        data: payload,
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateBlog = async (
    blogId: string,
    params: blogQueryParams,
    payload: blogBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/${blogId}/update-blog`,
        method: "PUT",
        data: payload,
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateCategoryStatus = async (
    categoryId: string,
    params: blogQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/${categoryId}/category-enable-disable`,
        method: "PUT",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateBlogStatus = async (
    blogId: string,
    params: blogBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/${blogId}/blog-enable-disable`,
        method: "PUT",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const createCategoryForBlog = async (
    payload: blogBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/create-category`,
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const createBlog = async (
    payload: blogBody,
    categoryId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/${categoryId}/create-blog`,
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deleteBlog = async (
    blogId: string,
    queryParams: blogQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/${blogId}/delete-blog`,
        method: "DELETE",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const reorderBlogs = async (
    payload: blogBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/blog/reorder`,
        method: "PUT",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

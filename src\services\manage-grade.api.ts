import { fetch } from "@/src/libs/helpers";

export const getAllGrade = async (
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/grade",
        method: "GET",
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateGradeStatus = async (
    gradeId: string,
    params: any,
    authorization: string
): Promise<any> => {
    return fetch({
        url: `/grade/${gradeId}/grade-enable-disable`,
        method: "PUT",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getAllForTecherGrade = async (
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/grade/get-enabled-grades",
        method: "GET",
        headers: {
            Authorization: authorization,
        },
    });
};

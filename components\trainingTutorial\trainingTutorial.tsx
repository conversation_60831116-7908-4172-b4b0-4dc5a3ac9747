"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import "@/app/globals.css";
import Image from "next/image";
import {
    Table,
    Spin,
    Button,
    message,
    Empty,
    Modal,
    Dropdown,
    Menu,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import {
    courseQueryParams,
    courseBody,
    getAllCategory,
    updateCourseStatus,
    getAllTrainingCourse,
    deleteCourse,
    reorderCourse,
} from "@/src/services/course.api";
import { CircularProgress, Switch } from "@mui/material";
import { Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { debounce } from "lodash";
import { useTranslation } from "react-i18next";
import SelectedCourse from "./preview/selectedBlog";
import AddEditCourseModal from "./addEditCourse";
import AddEditCategory from "./addEditCategories";
import {
    arrayMove,
    SortableContext,
    useSortable,
    verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { HolderOutlined } from "@ant-design/icons";
import { DndContext, closestCenter, DragEndEvent } from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const TrainingCourse = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] =
        useState(searchQuery);
    const [isButtonLoading, setButtonIsLoading] = useState(false);
    const [isAddEditCourseOpen, setIsAddEditCourseOpen] = useState(false);
    const [isAddEditCategory, setIsAddEditCategory] = useState(false);
    const [mode, setMode] = useState<any>(null);
    const [total, setTotal] = useState<number>(0);
    const [allTrainingCourse, setAllTrainingCourse] = useState<any[]>([]);
    const [record, setRecord] = useState<any>(null);
    const [fetchedCategories, setFetchedCategories] = useState<any>(null);
    const [selectedAnyCourse, setSelectedAnyCourse] = useState(false);
    const [reorderingIsOngoing, setReorderingIsOngoing] = useState(false);
    const { t } = useTranslation();

    const router = useRouter();

    useEffect(() => {
        fetchAllCourse();
        fetchAllCategory();
    }, [isAddEditCourseOpen, isAddEditCategory]);

    useEffect(() => {
        fetchAllCourse();
    }, [debouncedSearchQuery]);

    // const SortableRow = ({ ...props }) => {
    //     const { attributes, listeners, setNodeRef, transform, transition } =
    //         useSortable({
    //             id: props["data-row-key"],
    //         });

    //     const style = {
    //         ...props.style,
    //         transform: CSS.Transform.toString(
    //             transform && {
    //                 ...transform,
    //                 scaleY: 1,
    //             }
    //         ),
    //         transition,
    //     };

    //     const childrenArray = React.Children.toArray(props.children);

    //     return (
    //         <>
    //             {allTrainingCourse && allTrainingCourse.length > 0 && (
    //                 <tr {...props} ref={setNodeRef} style={style}>
    //                     <td
    //                         {...attributes}
    //                         {...listeners}
    //                         style={{ cursor: "move", padding: "8px" }}
    //                     >
    //                         <HolderOutlined className="pl-[12px]" />
    //                     </td>
    //                     {childrenArray.slice(1)}
    //                 </tr>
    //             )}
    //         </>
    //     );
    // };

    // const DraggableBodyRow = (props: any) => <SortableRow {...props} />;
    // const isReordering = useRef(false);

    // const handleDragEnd = (event: DragEndEvent) => {
    //     const { active, over } = event;

    //     // If no change in position or already reordering, return early
    //     if (active.id === over?.id || isReordering.current) {
    //         return;
    //     }

    //     isReordering.current = true;

    //     try {
    //         const oldIndex = allTrainingCourse.findIndex(
    //             (item: any) => item.id === active.id
    //         );
    //         const newIndex = allTrainingCourse.findIndex(
    //             (item: any) => item.id === over?.id
    //         );

    //         if (newIndex === -1) {
    //             return;
    //         }

    //         // Create updated data outside of setState
    //         const updatedData = arrayMove(
    //             allTrainingCourse,
    //             oldIndex,
    //             newIndex
    //         );
    //         const updatedWithSrNo = updatedData.map((item: any, index) => ({
    //             ...item,
    //             srNo: (currentPage - 1) * 10 + index + 1,
    //         }));

    //         // Update state once
    //         setAllTrainingCourse(updatedWithSrNo);

    //         // Make API call
    //         if (active?.id) {
    //             reorderQuestionById(active.id, newIndex + 1).finally(() => {
    //                 isReordering.current = false;
    //             });
    //         }
    //     } catch (error) {
    //         isReordering.current = false;
    //         console.error("Error in handleDragEnd:", error);
    //     }
    // };

    const reorderQuestionById = async (videoId: any, newPosition: number) => {
        const payload: courseBody = {
            videoId: videoId,
            newPosition: newPosition,
        };
        const authorization = localStorage.getItem("idToken");
        try {
            // setReorderingIsOngoing(true);
            const reorderRes = await reorderCourse(payload, authorization);
            if (reorderRes) {
                fetchAllCourseAfterReorder();
            }
        } catch (error) {
            console.error(t("Something went wrong")); // Something went wrong
            // setReorderingIsOngoing(false);
        }
    };

    const fetchAllCourseAfterReorder = async () => {
        try {
            setReorderingIsOngoing(true);
            const idToken: any = localStorage.getItem("idToken");
            const queryParams: courseQueryParams = {
                skip: 0,
                take: 100,
                orderBy: "serialNo|asc",
                include: "courseCategory",
                search_column: "title",
                search: debouncedSearchQuery,
                status: "ALL",
            };
            const res = await getAllTrainingCourse(queryParams, idToken);
            if (res) {
                setAllTrainingCourse(res.list);
                setReorderingIsOngoing(false);
                setTotal(res.total);
            } else {
                setReorderingIsOngoing(false);
            }
        } catch (error) {
            setReorderingIsOngoing(false);
            message.error("Error retrieving course!");
        }
    };

    const fetchAllCategory = async () => {
        try {
            const idToken: any = localStorage.getItem("idToken");
            const queryParams: courseQueryParams = {
                skip: 0,
                take: 100,
                orderBy: "createdAt|desc",
                status: "ALL",
            };
            const res = await getAllCategory(queryParams, idToken);
            if (res) {
                setFetchedCategories(res.list);
            }
        } catch (error) {
            message.error("Error retrieving Category!");
        }
    };

    const fetchAllCourse = async () => {
        try {
            setIsLoading(true);
            const idToken: any = localStorage.getItem("idToken");
            const queryParams: courseQueryParams = {
                skip: 0,
                take: 100,
                orderBy: "serialNo|asc",
                include: "courseCategory",
                search_column: "title",
                search: debouncedSearchQuery,
                status: "ALL",
            };
            const res = await getAllTrainingCourse(queryParams, idToken);
            if (res) {
                setAllTrainingCourse(res.list);
                setIsLoading(false);
                setTotal(res.total);
            } else {
                setIsLoading(false);
            }
        } catch (error) {
            setIsLoading(false);
            message.error("Error retrieving course!");
        }
    };

    const [isDeleteting, setIsDeleteing] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] =
        useState<boolean>(false);
    const [deleteCourseId, setDeleteCourseId] = useState("");

    const deteleCourseById = async (videoId: any) => {
        try {
            setIsDeleteing(true);
            const idToken: any = localStorage.getItem("idToken");
            const queryParams: courseQueryParams = {
                videoId: videoId,
            };
            const res = await deleteCourse(videoId, queryParams, idToken);
            if (res) {
                setIsDeleteModalVisible(false);
                setIsDeleteing(false);
                setDeleteCourseId("");
                fetchAllCourse();
            }
        } catch (error) {
            setIsDeleteing(false);
            message.error("Error in deleting the Course!");
        }
    };

    const updateStatusOfCourse = async (videoId: string, index: any) => {
        try {
            const idToken: any = localStorage.getItem("idToken");
            const newStatus =
                allTrainingCourse?.[index - 1]?.status === "ENABLED"
                    ? "DISABLED"
                    : "ENABLED";
            const queryParams: courseBody = {
                videoId: videoId,
                status: newStatus,
            };
            const res = await updateCourseStatus(videoId, queryParams, idToken);
            if (res && allTrainingCourse) {
                setAllTrainingCourse((allTrainingCourse: any) =>
                    allTrainingCourse.map((video: any, i: any) =>
                        video?.id === videoId
                            ? { ...video, status: newStatus }
                            : video
                    )
                );
            }
        } catch (error: any) {
            if (error.message.includes("Kurskategorie wurde deaktiviert.")) {
                message.error(t("Course category is disabled. Please enable the category to update the status!"));
            } else {
                message.error("Error updating Course status!");
            }
        }
    };

    const handleSearch = useCallback(
        debounce((query: any) => {
            setDebouncedSearchQuery(query); // update debounced query when user stops typing
        }, 500),
        []
    );

    const handleInputChange = (e: any) => {
        setSearchQuery(e.target.value);
        handleSearch(e.target.value); // call debounced function
    };

    const handleMenuClick = async (e: any, record: any) => {
        const { key } = e;
        setRecord(record);
        if (key === "view") {
            setIsAddEditCourseOpen(true);
            setMode("view");
        } else if (key === "edit") {
            setIsAddEditCourseOpen(true);
            setMode("edit");
        } else if (key === "delete") {
            setIsDeleteModalVisible(true);
        }
    };

    const menu = (record: any) => (
        <Menu onClick={(e) => handleMenuClick(e, record)}>
            <Menu.Item key="view">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/eye.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    {t("View")}
                </div>
            </Menu.Item>
            <Menu.Item key="edit">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[10px] mb-1"
                    />
                    {t("Edit")}
                </div>
            </Menu.Item>
            {/* <Menu.Item key="delete">
                <div className="flex text-[14px] font-roboto text-[#F1676D]">
                    <Image
                        src="/images/trash.svg"
                        alt="visibility"
                        width={23}
                        height={23}
                        className="cursor-pointer mr-[7px] mb-1"
                    />
                    {t("delete")}
                </div>
            </Menu.Item> */}
        </Menu>
    );

    const coursesDataColumns: ColumnsType<any> = [
        // {
        //     title: "",
        //     dataIndex: "sort",
        //     className: `${roboto.className}`,
        //     align: "center",
        //     width: 40,
        //     render: () => <SortableRow className="pl-3" />,
        // },
        {
            title: <div className="ml-2">{t("Sr. No.")}</div>,
            dataIndex: "serialNo",
            key: "serialNo",
            align: "left",
            className: `${roboto.className}`,
            width: 80,
            render: (srNo) => <div className="pl-[16px]">{srNo}.</div>,
        },
        {
            title: t("Title"),
            dataIndex: "title",
            key: "title",
            align: "center",
            className: `${roboto.className}`,
            render: (title) => <div>{title ? title : "-"}</div>,
        },
        {
            title: t("Description"),
            dataIndex: "description",
            key: "description",
            align: "center",
            className: `${roboto.className}`,
            render: (description) => {
                const maxLength = 80; // Set the maximum length of characters
                const truncatedDescription =
                    description && description.length > maxLength
                        ? `${description.substring(0, maxLength)}...`
                        : description || "-";
                return <div>{truncatedDescription}</div>;
            },
        },
        {
            title: t("Course Category"),
            dataIndex: "courseCategory",
            key: "courseCategory",
            align: "center",
            className: `${roboto.className}`,
            width: 170,
            render: (course) => (
                <div>{course?.category ? course?.category : "-"}</div>
            ),
        },
        {
            title: t("Status"),
            dataIndex: "status",
            key: "status",
            align: "center",
            className: `${roboto.className}`,
            width: 170,
            render: (status: string, record: any) => (
                <div className="flex justify-center items-center cursor-pointer">
                    {record.id ? (
                        <Switch
                            checked={status.toLowerCase() === "enabled"}
                            onChange={() =>
                                updateStatusOfCourse(record.id, record.serialNo)
                            }
                            color="primary"
                            sx={{
                                "& .MuiSwitch-switchBase": {
                                    color: "#CBCBCB",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked": {
                                    color: "#67A1A3",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                                    {
                                        backgroundColor: "#67A1A3",
                                    },
                                "& .MuiSwitch-track": {
                                    backgroundColor:
                                        "rgba(211, 217, 231, 0.80)",
                                },
                            }}
                            size="medium"
                        />
                    ) : (
                        <CircularProgress size={20} />
                    )}
                </div>
            ),
        },
        {
            title: t("More"),
            dataIndex: "more",
            key: "more",
            className: `${roboto.className}`,
            align: "center",
            width: 130,
            render: (more: string, record: any) => {
                return record.id ? (
                    <Dropdown
                        overlay={menu(record)}
                        trigger={["click"]}
                        // placement="bottomRight"
                    >
                        <div
                            className="flex justify-center"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <Image
                                src="/images/more.svg"
                                alt="More"
                                width={20}
                                height={20}
                                className="cursor-pointer"
                                onClick={(e) => e.preventDefault()}
                            />
                        </div>
                    </Dropdown>
                ) : (
                    <CircularProgress size={20} />
                );
            },
        },
    ];

    const [currentPage, setCurrentPage] = useState(1);
    const [isTabledataLoading, setIsTabledataLoading] = useState(false);
    const paginationConfig = {
        pageSize: 100,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    return (
        <div className="flex flex-col w-full">
            {!selectedAnyCourse && (
                <div>
                    {!isAddEditCourseOpen && !isAddEditCategory && (
                        <>
                            <Navbar />
                            <div
                                className={`${roboto.className} w-full bg-white text-[24px] text-black items-center mt-4`}
                            >
                                <div className="flex items-center justify-between px-4 mb-3">
                                    <div className="flex items-center font-[600] text-[23px]">
                                        {t("Training Course")}
                                        <span className="text-gray-400 font-semibold text-[18px] font-roboto ml-1 mt-[2px]">
                                            ({total})
                                        </span>
                                    </div>
                                    <div className="text-[25px] text-black flex items-center space-x-3">
                                        <div className="text-[16px] font-normal text-black h-auto flex items-center px-4 relative">
                                            <input
                                                type="text"
                                                placeholder={t("search")}
                                                className="bg-[#D9D9D9] bg-opacity-[20%] pl-10 w-[350px] py-2 rounded-lg h-10"
                                                value={searchQuery}
                                                onChange={handleInputChange} // call the input change handler
                                            />
                                            {/* <SearchOutlined className="absolute left-7 top-1/2 transform -translate-y-1/2"/> */}
                                            <img
                                                src="/images/search_1.svg"
                                                alt="toggle dropdown"
                                                width={20}
                                                height={20}
                                                className="absolute left-7 top-1/2 transform -translate-y-1/2"
                                            />
                                        </div>
                                        <Button
                                            type="primary"
                                            size="large"
                                            onClick={() => {
                                                setIsAddEditCourseOpen(true);
                                                setMode("add");
                                                setRecord(null);
                                            }}
                                            style={{
                                                backgroundColor: "#67A1A3",
                                            }}
                                            className={`${roboto.className} w-[150px] h-[40px] font-[500] font-roboto bg-[#67A1A3] text-white shadow-inner`}
                                            loading={isButtonLoading}
                                        >
                                            {t("Add Tutorial")}
                                        </Button>
                                        <Button
                                            type="primary"
                                            size="large"
                                            onClick={() => {
                                                setIsAddEditCategory(true);
                                            }}
                                            style={{
                                                backgroundColor: "#67A1A3",
                                            }}
                                            className={`${roboto.className} w-[150px] h-[40px] font-[500] font-roboto bg-[#67A1A3] text-white shadow-inner`}
                                            loading={isButtonLoading}
                                        >
                                            {t("Categories")}
                                        </Button>
                                    </div>
                                </div>
                                {isLoading ? (
                                    <div className="flex justify-center items-center h-[calc(100vh-150px)] mt-2">
                                        <Spin size="default" />
                                    </div>
                                ) : (
                                    <>
                                        {allTrainingCourse && (
                                            // <DndContext
                                            //     modifiers={[
                                            //         restrictToVerticalAxis,
                                            //     ]}
                                            //     onDragEnd={handleDragEnd}
                                            // >
                                            //     <SortableContext
                                            //         // items={data.map((item) => item.id)}
                                            //         items={
                                            //             allTrainingCourse &&
                                            //             allTrainingCourse
                                            //                 .map(
                                            //                     (item: any) =>
                                            //                         item.id
                                            //                 )
                                            //                 .filter(
                                            //                     (
                                            //                         id: any
                                            //                     ): id is string =>
                                            //                         !!id
                                            //                 )
                                            //         }
                                            //         strategy={
                                            //             verticalListSortingStrategy
                                            //         }
                                            //     >
                                            <Table
                                                columns={coursesDataColumns}
                                                dataSource={allTrainingCourse}
                                                pagination={{
                                                    ...paginationConfig,
                                                    className:
                                                        "custom-pagination custom-select",
                                                }}
                                                loading={
                                                    isTabledataLoading ||
                                                    reorderingIsOngoing
                                                }
                                                bordered={false}
                                                rowKey="id"
                                                // className="custom-table"
                                                className={`custom-table ${
                                                    coursesDataColumns.length >
                                                    5
                                                        ? "custom-table-scroll"
                                                        : "custom-table-scroll-hide"
                                                }  scrollbar font-[400]`}
                                                // components={{
                                                //     body: {
                                                //         row: DraggableBodyRow,
                                                //     },
                                                // }}
                                                scroll={{
                                                    y: "66vh",
                                                }}
                                                locale={{
                                                    emptyText: (
                                                        <div
                                                            className={`h-[61vh] flex items-center justify-center font-roboto font-[500]`}
                                                        >
                                                            <Empty
                                                                description={t(
                                                                    "No data available"
                                                                )}
                                                                image={
                                                                    Empty.PRESENTED_IMAGE_SIMPLE
                                                                }
                                                            />
                                                        </div>
                                                    ),
                                                }}
                                            />
                                            //     </SortableContext>
                                            // </DndContext>
                                        )}
                                    </>
                                )}
                            </div>
                        </>
                    )}
                </div>
            )}
            {isAddEditCourseOpen && (
                <AddEditCourseModal
                    isAddEditCourseOpen={isAddEditCourseOpen}
                    setIsAddEditCourseOpen={setIsAddEditCourseOpen}
                    fetchedCategories={fetchedCategories}
                    record={record}
                    setRecord={setRecord}
                    mode={mode}
                />
            )}
            {isAddEditCategory && (
                <AddEditCategory
                    isAddEditCategory={isAddEditCategory}
                    setIsAddEditCategory={setIsAddEditCategory}
                />
            )}
            {selectedAnyCourse && (
                <SelectedCourse
                    selectedAnyCourse={selectedAnyCourse}
                    setSelectedAnyCourse={setSelectedAnyCourse}
                    record={record}
                />
            )}
            <Modal
                open={isDeleteModalVisible}
                onCancel={() => setIsDeleteModalVisible(false)}
                footer={null}
                centered
                width={300}
                style={{ textAlign: "center" }}
                closable={false}
            >
                <Image
                    src="/images/modalDelete.svg"
                    alt="Delete"
                    width={100}
                    height={100}
                    className="mx-auto bg-white -mt-3"
                />

                <div className=" flex justify-center items-center w-full">
                    <h2 className="text-[16px] font-roboto font-[400] mb-8 w-[80%] flex justify-center items-center">
                        {t("Are you sure you want to delete this Course?")}
                    </h2>
                </div>
                <div className="flex justify-center space-x-4">
                    <Button
                        onClick={() => {
                            setIsDeleteModalVisible(false);
                            setDeleteCourseId("");
                        }}
                        className="bg-transparent cancelbutton font-roboto text-[#F1676D] text-[18px] h-[46px] border-[1px] border-[#F1676D] w-[116px] font-medium rounded-xl"
                    >
                        {t("cancel")}
                    </Button>
                    <Button
                        type="primary"
                        className="bg-[#F1676D] shadow-inner deleteButton font-roboto text-white text-[18px] w-[116px] h-[46px] font-medium rounded-xl"
                        onClick={() => {
                            deteleCourseById(deleteCourseId);
                        }}
                        loading={isDeleteting}
                    >
                        {t("delete")}
                    </Button>
                </div>
            </Modal>
        </div>
    );
};

export default TrainingCourse;

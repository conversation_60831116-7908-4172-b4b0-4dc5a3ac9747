import { fetch } from "@/src/libs/helpers";

export interface QueryParams {
    fileName?: string;
    fileType?: string;
    resourceType?: string;
}

const convertFileName = (fileName: any) => {

    const timestamp = Date.now();
    const extension = fileName.substring(fileName.lastIndexOf("."));
    const baseName = fileName.substring(0, fileName.lastIndexOf("."));
    const sanitizedBaseName = baseName.replace(/[\s()]/g, "_");

    return `${timestamp}_${sanitizedBaseName}${extension}`;
}

export const uploadFile = async (
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    const newFileName = convertFileName(queryParams.fileName)
    queryParams.fileName = newFileName;
    return fetch({
        url: "/upload/presinged-url",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deleteUploadedFile = async (
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/upload/delete-file",
        method: "DELETE",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

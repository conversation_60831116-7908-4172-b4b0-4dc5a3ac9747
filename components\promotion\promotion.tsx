"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import {
    Dropdown,
    Table,
    Menu,
    Spin,
    message,
    Modal,
    Button,
    Empty,
} from "antd";
import {
    addPromotionQueryParams,
    getAllPromotionPost,
    deletePromotionPostById,
    updatePromotionStatusById,
} from "@/src/services/promotion.api";
import { promotionType } from "@/src/libs/constants";
import { CircularProgress, Switch } from "@mui/material";
import type { ColumnsType } from "antd/es/table";
import { Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";

const robotoBold = Roboto({ weight: "700", subsets: ["latin"] });
const robotoMedium = Roboto({ weight: "500", subsets: ["latin"] });

interface promotionData {
    srNo: number;
    photo: string;
    name: string;
    location: string;
    id?: string;
    currency?: string;
    startingDate?: string;
    expirationDate?: string;
    createdAt?: string;
    status?: string;
}

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const Promotion = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [recordToDelete, setRecordToDelete] = useState<any>(null);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [data, setData] = useState<promotionData[]>([]);
    const [response, setResponse] = useState<any>(null);
    const [isDeleteModalVisible, setIsDeleteModalVisible] =
        useState<boolean>(false);
    const router = useRouter();
    const { t } = useTranslation();

    useEffect(() => {
        fetchData(currentPage);
    }, [currentPage, pageSize, searchQuery]);

    const fetchData = async (page: number = 1) => {
        setIsLoading(true);
        const queryParams: addPromotionQueryParams = {
            skip: (page - 1) * pageSize,
            take: pageSize,
            search: searchQuery,
            orderBy: "createdAt|desc",
        };
        const authorization = localStorage.getItem("idToken");
        try {
            const response = await getAllPromotionPost(
                queryParams,
                authorization
            );
            setResponse(response);
            if (response) {
                const updatedData = response.list.map(
                    (item: any, index: any) => ({
                        ...item,
                        srNo: (page - 1) * pageSize + index + 1,
                    })
                );
                setData(updatedData);
                setTotal(response.total);
            } else {
                message.error(t("Empty response data"));
            }
        } catch (error) {
            message.error(t("Data could not be retrieved"));
        }
        setIsLoading(false);
    };

    const updateStatusOfBlog = async (postId: string, recpord: any) => {
        try {
            const idToken: any = localStorage.getItem("idToken");
            const newStatus =
                recpord?.status === "ENABLED" ? "DISABLED" : "ENABLED";
            const queryParams: addPromotionQueryParams = {
                postId: postId,
                status: newStatus,
            };
            const res = await updatePromotionStatusById(
                postId,
                queryParams,
                idToken
            );
            if (res && data) {
                setData((promotion: any) =>
                    promotion.map((data: any, i: any) =>
                        postId === data?.id
                            ? { ...data, status: newStatus }
                            : data
                    )
                );
            }
        } catch (error: any) {
            message.error("Error updating promotion status!");
        }
    };

    const handleMenuClick = async (e: any, record: any) => {
        e.domEvent.stopPropagation();
        const { key } = e;
        if (key === "view") {
            router.push(`/promotion/addPromotion?mode=view&id=${record.id}`);
        } else if (key === "edit") {
            router.push(`/promotion/addPromotion?mode=edit&id=${record.id}`);
        } else if (key === "delete") {
            setIsDeleteModalVisible(true);
        }
    };

    const handleDeleteConfirm = async (id: string) => {
        try {
            const authorization = localStorage.getItem("idToken");
            const response = await deletePromotionPostById(id, authorization);
            setIsDeleteModalVisible(false);
            fetchData(currentPage);
            message.success(t("Promotion deleted successfully")); //"Promotion deleted successfully
        } catch (error) {
            message.error(t("Failed to delete Promotion Post")); //Failed to delete Promotion Post
        }
    };

    const menu = (record: any) => (
        <Menu onClick={(e) => handleMenuClick(e, record)}>
            <Menu.Item key="view">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/eye.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    {t("View")}
                </div>
            </Menu.Item>
            <Menu.Item key="edit">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[10px] mb-1"
                    />
                    {t("Edit")}
                </div>
            </Menu.Item>
            <Menu.Item key="delete">
                <div className="flex text-[14px] font-roboto text-[#F1676D]">
                    <Image
                        src="/images/trash.svg"
                        alt="visibility"
                        width={23}
                        height={23}
                        className="cursor-pointer mr-[7px] mb-1"
                    />
                    {t("delete")}
                </div>
            </Menu.Item>
        </Menu>
    );

    const promotionTableData: ColumnsType<promotionData> = [
        {
            title: <div className="ml-2">{t("Sr. No.")}</div>,
            dataIndex: "srNo",
            key: "srNo",
            align: "left",
            className: `${roboto.className}`,
            width: 80,
            render: (srNo) => <div className="pl-[20px]">{srNo}.</div>,
        },
        {
            title: t("Promotion Name"),
            dataIndex: "title",
            key: "title",
            align: "center",
            className: `${roboto.className}`,
            render: (schoolName) => (
                <div className="text-center">
                    {schoolName ? schoolName : "-"}
                </div>
            ),
        },
        {
            title: t("Type of Post"),
            dataIndex: "type",
            key: "type",
            align: "center",
            className: `${roboto.className}`,
            render: (typeOfPost) => {
                const type =
                    promotionType.find((item) => item.value === typeOfPost)
                        ?.label || "-";
                return <div className="text-center">{type}</div>;
            },
        },
        {
            title: t("Location"),
            dataIndex: "place",
            key: "place",
            align: "center",
            className: `${roboto.className}`,
            render: (location) => (
                <div className="text-center">{location ? location : "-"}</div>
            ),
        },
        {
            title: t("Time"),
            dataIndex: "time",
            key: "time",
            align: "center",
            className: `${roboto.className}`,
            render: (time, record) => {
                const startingDate = record.startingDate
                    ? new Date(record.startingDate).toLocaleTimeString(
                          "en-US",
                          {
                              hour: "2-digit",
                              minute: "2-digit",
                              second: "2-digit",
                          }
                      )
                    : "10:10:00";

                return <div className="text-center">{startingDate}</div>;
            },
        },

        {
            title: t("Price"),
            dataIndex: "price",
            key: "price",
            align: "center",
            className: `${roboto.className}`,
            render: (price, record) => (
                <div className="text-center">
                    {price ? `${price} ${record.currency} ` : "-"}
                </div>
            ),
        },
        {
            title: t("Starting Date"),
            dataIndex: "startingDate",
            key: "startingDate",
            align: "center",
            className: `${roboto.className}`,
            render: (startDate) => {
                const formattedDate = startDate
                    ? new Date(startDate)
                          .toLocaleDateString("en-GB", {
                              day: "2-digit",
                              month: "short",
                              year: "numeric",
                          })
                          .replace(/ /g, " ")
                    : "-";
                return <div className="text-center">{formattedDate}</div>;
            },
        },
        {
            title: t("Expiration Date"),
            dataIndex: "expirationDate",
            key: "expirationDate",
            align: "center",
            className: `${roboto.className}`,
            render: (expirationDate) => {
                const formattedDate = expirationDate
                    ? new Date(expirationDate)
                          .toLocaleDateString("en-GB", {
                              day: "2-digit",
                              month: "short",
                              year: "numeric",
                          })
                          .replace(/ /g, " ")
                    : "-";
                return <div className="text-center">{formattedDate}</div>;
            },
        },

        {
            title: t("Status"),
            dataIndex: "status",
            key: "status",
            align: "center",
            width: 100,
            render: (status: string, record: promotionData) => (
                <div className="flex justify-center items-center">
                    {record.id ? (
                        <Switch
                            checked={status.toLowerCase() === "enabled"}
                            onClick={(e) => e.stopPropagation()}
                            onChange={() => {
                                if (record.id) {
                                    updateStatusOfBlog(record.id, record);
                                }
                            }}
                            color="primary"
                            sx={{
                                "& .MuiSwitch-switchBase": {
                                    color: "#CBCBCB",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked": {
                                    color: "#67A1A3",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                                    {
                                        backgroundColor: "#67A1A3",
                                    },
                                "& .MuiSwitch-track": {
                                    backgroundColor:
                                        "rgba(211, 217, 231, 0.80)",
                                },
                            }}
                            size="medium"
                        />
                    ) : (
                        <CircularProgress size={20} />
                    )}
                </div>
            ),
            className: `${roboto.className}`,
        },
        {
            title: t("More"),
            dataIndex: "more",
            key: "more",
            align: "center",
            className: `${roboto.className}`,
            width: 100,
            render: (more: string, record: promotionData) => {
                const handleMoreMenuClick = () => {
                    if (record.id) {
                        setRecordToDelete(record.id);
                    }
                };

                return record.id ? (
                    <Dropdown
                        overlay={menu(record)}
                        trigger={["click"]}
                        onOpenChange={handleMoreMenuClick}
                    >
                        <div
                            className="flex justify-center"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <Image
                                src="/images/more.svg"
                                alt="More"
                                width={20}
                                height={20}
                                className="cursor-pointer"
                                onClick={(e) => e.preventDefault()}
                            />
                        </div>
                    </Dropdown>
                ) : (
                    <CircularProgress size={20} />
                );
            },
        },
    ];

    const paginationConfig = {
        pageSize: pageSize,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    return (
        <div className="flex flex-col">
            {/* <SideBar /> */}
            <Navbar />
            <div
                className={`${roboto.className} w-full bg-white text-[24px] text-black items-center mt-4`}
            >
                <div className="flex items-center justify-between px-4 mb-2">
                    <div
                        className={`flex items-center font-[600] text-[26px] ${robotoBold.className}`}
                    >
                        {t("Promotion")}
                        <span
                            className={`text-gray-400 mt-[2px] text-[18px] ml-2 ${robotoMedium.className}`}
                        >
                            ({total})
                        </span>
                    </div>
                    <div className="text-[25px] text-black flex items-center space-x-3">
                        <button
                            className="rounded-xl shadow-inner w-[150px] h-[40px] font-[500] text-[16px] px-2 bg-[#67A1A3] text-white flex justify-center items-center space-x-2"
                            onClick={() =>
                                router.push("/promotion/addPromotion")
                            }
                        >
                            <span className="flex">{t("Add Promotion")}</span>
                        </button>
                    </div>
                </div>
                {isLoading ? (
                    <div className="flex justify-center items-center  h-[calc(100vh-150px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <div className="w-full">
                        <Table
                            columns={promotionTableData}
                            dataSource={data}
                            pagination={{
                                ...paginationConfig,
                                className: "custom-pagination custom-select",
                            }}
                            bordered={false}
                            loading={{
                                indicator: <Spin size="default" />,
                                spinning: isLoading,
                            }}
                            rowKey="srNo"
                            // className="custom-table"
                            className={`custom-table ${
                                data.length > 5
                                    ? "custom-table-scroll"
                                    : "custom-table-scroll-hide"
                            }  scrollbar font-[400]`}
                            rowClassName="custom-table-row"
                            onRow={(record) => {
                                return {
                                    onClick: () => {
                                        router.push(
                                            `/promotion/addPromotion?mode=view&id=${record.id}`
                                        );
                                    },
                                };
                            }}
                            scroll={{
                                y: "67vh",
                            }}
                            locale={{
                                emptyText: (
                                    <div
                                        className={`h-[61vh] flex items-center justify-center font-roboto font-[500] text-gray-500`}
                                    >
                                        <Empty
                                            description={t("No data available")}
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        />
                                    </div>
                                ),
                            }}
                        />
                    </div>
                )}
            </div>
            <Modal
                open={isDeleteModalVisible}
                onCancel={() => setIsDeleteModalVisible(false)}
                footer={null}
                centered
                width={300} // Adjust the width as per your requirement (number or string)
                style={{ textAlign: "center" }}
                closable={false}
            >
                <Image
                    src="/images/modalDelete.svg"
                    alt="Delete"
                    width={100}
                    height={100}
                    className="mx-auto bg-white -mt-3"
                />
                <div className=" flex justify-center items-center w-full">
                    <h2 className="text-[16px] font-roboto font-[400] mb-8 w-[80%] flex justify-center items-center">
                        {t("Are you sure you want to delete this promotion?")}
                    </h2>
                </div>
                <div className="flex justify-center space-x-4">
                    <Button
                        onClick={() => setIsDeleteModalVisible(false)}
                        className="bg-transparent cancelbutton font-roboto text-[#F1676D] text-[18px] h-[46px] border-[1px] border-[#F1676D] w-[116px] font-medium rounded-xl"
                    >
                        {t("cancel")}
                    </Button>
                    <Button
                        type="primary"
                        className="bg-[#F1676D] shadow-inner deleteButton font-roboto text-white text-[18px] w-[116px] h-[46px] font-medium rounded-xl"
                        onClick={() => handleDeleteConfirm(recordToDelete)}
                        loading={isLoading}
                    >
                        {t("delete")}
                    </Button>
                </div>
            </Modal>
        </div>
    );
};

export default Promotion;

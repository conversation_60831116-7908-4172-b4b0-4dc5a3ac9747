import { fetch } from "@/src/libs/helpers";

export interface materialDetail {
    serialNum?: number | null;
    fileName?: string;
    subject?: string;
    file?: string;
    materialId?: string;
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    search_column?: string;
}

export const getAllMaterial = async (
    queryParams: materialDetail,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/training-material",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const createNewMaterial = async (
    payload: materialDetail,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/training-material/create",
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getTrainingMaterialById = async (
    queryParams: materialDetail,
    materialId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-material/${materialId}`,
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateMaterial = async (
    materialId: string,
    payload: materialDetail,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-material/${materialId}/update`,
        method: "PUT",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deleteMaterial = async (
    queryParams: materialDetail,
    materialId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/training-material/${materialId}/delete`,
        method: "DELETE",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

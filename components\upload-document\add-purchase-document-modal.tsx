import { Button, Input, message, Modal, Select, Space, Spin } from "antd";
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import uploadDocumentStyles from "./upload-document.module.css";
import { useTranslation } from "react-i18next";
import { grades } from "@/src/libs/constants";
import {
    createPurchaseWorkbook,
    uploadDocumentDetails,
} from "@/src/services/upload-document.api";
import { QueryParams, uploadFile } from "@/src/services/upload.api";
import { Roboto } from "next/font/google";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const { Option } = Select;

const AddPurchaseDocumentModal = ({
    isModalVisible,
    setIsModalVisible,
    fetchData,
    recordDataForEdit,
}: any) => {
    const { t } = useTranslation();
    const [grade, setGrade] = useState<any>();
    const [fileName, setFileName] = useState("");
    const [isFileUploading, setIsFileUploading] = useState(false);
    const [fileUrl, setFileUrl] = useState(null);
    const [isCreateDocumentLoading, setIsCreateDocumentLoading] =
        useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleDocumentChange = async (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const file = e.target.files?.[0];
        if (file) {
            if (file.type !== "application/pdf") {
                message.error(t("Only PDF files are allowed"));
                return;
            }

            const fileSizeLimit = 50 * 1024 * 1024;
            if (file.size > fileSizeLimit) {
                message.error(t("File size exceeds the limit of 50 MB"));
                return;
            }

            const trimmedFileName = file.name.replace(/\.[^/.]+$/, "");
            const queryParams: QueryParams = {
                fileName: trimmedFileName,
                fileType: "FILE",
                resourceType: "PURCHASE",
            };
            const authorization = localStorage.getItem("idToken");
            setFileName(trimmedFileName);

            try {
                setIsFileUploading(true);
                const response = await uploadFile(queryParams, authorization);

                if (response && response.preSignedUrl) {
                    const url = response.preSignedUrl;
                    try {
                        await fetch(url, {
                            method: "PUT",
                            body: file,
                            headers: {
                                "Content-Type": file.type,
                            },
                        });

                        setFileUrl(response.outPutUrl);
                        message.success(t("File uploaded successfully"));

                        if (fileInputRef.current) {
                            fileInputRef.current.value = "";
                        }
                        setIsFileUploading(false);
                    } catch (uploadError) {
                        message.error(t("Invalid presigned URL respons"));
                        setIsFileUploading(false);
                    }
                } else {
                    message.error(t("File upload failed"));
                    setIsFileUploading(false);
                }
            } catch (apiError) {
                message.error(t("Failed to retrieve presigned URL"));
                setIsFileUploading(false);
            }
        }
    };

    const handleCreatePurchasePdf = async () => {
        if (!grade || !fileName || !fileUrl) {
            message.warning(t("Please fill in all fields before proceeding"));
            return;
        }

        try {
            setIsCreateDocumentLoading(true);
            const payload: uploadDocumentDetails = {
                docUrl: fileUrl,
                grade,
                fileName,
            };
            const authorization = localStorage.getItem("idToken");
            const response = await createPurchaseWorkbook(
                payload,
                authorization
            );

            if (response) {
                setIsCreateDocumentLoading(false);
                setIsModalVisible(false);

                setGrade(null);
                setFileName("");
                setFileUrl(null);

                fetchData();
                message.success(
                    t("Purchase workbook PDF created successfully")
                );
            }
        } catch (error) {
            message.error(t("Error creating purchase workbook PDF"));
            setIsModalVisible(true);
            setIsCreateDocumentLoading(false);
        }
    };

    useEffect(() => {
        if (recordDataForEdit) {
            setGrade(recordDataForEdit.grade);
            setFileName(recordDataForEdit.fileName);
            setFileUrl(recordDataForEdit.docUrl);
        }
    }, [recordDataForEdit]);

    const handleCancel = () => {
        setIsModalVisible(false);
        setGrade(null);
        setFileUrl(null);
        setFileName("");
    };

    return (
        <div>
            <Modal
                title={
                    <div
                        className={`text-start text-[22px] font-[500] ${roboto.className}`}
                    >
                        Add Purchase workbook Pdf
                    </div>
                }
                open={isModalVisible}
                footer={null}
                className={`rounded-lg select-none border-black w-[651px] h-[409px] ${uploadDocumentStyles.modal} ${roboto.className}`}
                centered
                width={651}
                style={{ textAlign: "center", height: "409px" }}
                closable={false}
            >
                <>
                    <Space
                        direction="vertical"
                        style={{ width: "100%" }}
                        className="mt-2"
                    >
                        <div className="flex justify-between items-center w-full">
                            <label className="text-[16px] flex justify-start font-semibold w-[150px]">
                                {t("Grade")} :
                            </label>
                            <Select
                                placeholder={t("Select Grade")}
                                value={grade}
                                onChange={(value) => {
                                    setGrade(value);
                                }}
                                suffixIcon={
                                    <Image
                                        src="/images/arrowI.svg"
                                        alt="More"
                                        width={20}
                                        height={20}
                                    />
                                }
                                className="custom-select align-start text-[16px] font-[400] w-[495px] h-[40px]"
                            >
                                {grades.map((grade) => (
                                    <Option
                                        key={grade.value}
                                        value={grade.value}
                                    >
                                        {grade.value === "FIRST"
                                            ? "Grade 1"
                                            : grade.value === "SECOND"
                                            ? "Grade 2"
                                            : grade.value === "THIRD"
                                            ? "Grade 3"
                                            : "Grade 4"}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex justify-between items-center w-full">
                            <label className="text-[16px] flex justify-start font-semibold w-[150px]">
                                {t("File Name")} :
                            </label>
                            <Input
                                placeholder={t("File Name")}
                                value={fileName}
                                onChange={(e) => setFileName(e.target.value)}
                                onBlur={() => setFileName(fileName.trim())}
                                className={`text-[14px] border-[#D3E3E3] bg-[#F6FAF9] w-[495px] h-[40px] ${roboto.className}`}
                            />
                        </div>
                        <div className="flex items-center w-full">
                            <label className="text-[16px] flex justify-start font-semibold w-[140px]"></label>
                            <label
                                htmlFor={`inputFile`}
                                className="flex rounded-md h-[124px] w-[124px] justify-center items-center cursor-pointer border-[#D3E3E3] bg-[#F6FAF9]"
                                style={{
                                    border: "1px solid #D3E3E3",
                                    position: "relative",
                                }}
                            >
                                <input
                                    id="inputFile"
                                    type="file"
                                    accept="application/pdf"
                                    style={{ display: "none" }}
                                    onChange={handleDocumentChange}
                                    ref={fileInputRef}
                                />
                                {isFileUploading ? (
                                    <div className="flex justify-center items-center w-full h-full absolute inset-0 z-10">
                                        <Spin size="small" />
                                    </div>
                                ) : fileUrl !== null ? (
                                    <Image
                                        src="/images/pdf.svg"
                                        alt="More"
                                        width={100}
                                        height={100}
                                        className="cursor-pointer"
                                    />
                                ) : (
                                    <p className="text-gray-300 text-[14px]">
                                        {t("Add File")}
                                    </p>
                                )}
                            </label>
                        </div>
                    </Space>

                    <div className="mt-6 flex justify-center space-x-4">
                        <Button
                            onClick={handleCancel}
                            className={`${uploadDocumentStyles.button} bg-transparent text-[#67A1A3] border-[1px] border-[#67A1A3] text-[18px] w-[287px] h-[45px] font-[500] rounded-xl ${roboto.className}`}
                        >
                            {t("cancel")}
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                handleCreatePurchasePdf();
                            }}
                            className={`${uploadDocumentStyles.button} bg-[#67A1A3] shadow-inner text-white text-[18px] w-[287px] h-[45px] font-[500] rounded-xl ${roboto.className}`}
                            loading={isCreateDocumentLoading}
                        >
                            {t("save")}
                        </Button>
                    </div>
                </>
            </Modal>
        </div>
    );
};

export default AddPurchaseDocumentModal;

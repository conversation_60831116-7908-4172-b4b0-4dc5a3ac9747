"use client";
import React, { useState, useEffect } from "react";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import "@/app/globals.css";
import { Table, Spin, message, Empty } from "antd";
import { Switch } from "@mui/material";
import type { ColumnsType } from "antd/es/table";
import {
    updateGradeStatus,
    getAllGrade,
} from "@/src/services/manage-grade.api";
import { Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface subscribedUser {
    email: string;
    id: string;
}

const ManageGrade = () => {
    const [data, setData] = useState<subscribedUser[]>([]);
    const [isTableDataLoading, setIsTableDataLoading] = useState(false);
    const { t } = useTranslation();
    const router = useRouter();

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        const authorization = localStorage.getItem("idToken");
        try {
            setIsTableDataLoading(true);
            const response = await getAllGrade(authorization);

            if (response) {
                const updatedData = response.map(
                    (item: any, index: number) => ({
                        ...item,
                        sr_no: index + 1,
                    })
                );
                setData(updatedData);
            } else {
                console.error(t("Empty or invalid response data"));
            }
        } catch (error) {
            console.error("Fetch error:", error);
            message.error(t("Data could not be retrieved"));
        } finally {
            setIsTableDataLoading(false);
        }
    };

    const updateStatusOfGrade = async (gradeId: string, record: any) => {
        try {
            const idToken: any = localStorage.getItem("idToken");
            const newStatus =
                record?.status === "ENABLED" ? "DISABLED" : "ENABLED";
            const queryParams: any = {
                gradeId: gradeId,
                status: newStatus,
            };
            const res = await updateGradeStatus(gradeId, queryParams, idToken);
            if (res) {
                setData((data: any) =>
                    data.map((grade: any, i: any) =>
                        gradeId === grade?.id
                            ? { ...grade, status: newStatus }
                            : grade
                    )
                );
            }
        } catch (error) {
            message.error("Error updating category status!");
        }
    };

    const ManageGradeDataColumns: ColumnsType<subscribedUser> = [
        {
            title: <div className="ml-2">{t("Sr. No.")}</div>,
            dataIndex: "sr_no",
            key: "sr_no",
            align: "start",
            className: `${roboto.className}`,
            width: "100px",
            render: (srNo) => <div className="pl-[18px]">{srNo}.</div>,
        },
        {
            title: "Grade",
            dataIndex: "grade",
            key: "grade",
            align: "center",
            className: `${roboto.className}`,
            render: (grade) => (
                <div className="px-[20px] flex justify-center">
                    <p className="text-left">
                        {grade === "FIRST"
                            ? "Grade 1"
                            : grade === "SECOND"
                            ? "Grade 2"
                            : grade === "THIRD"
                            ? "Grade 3"
                            : "Grade 4"}
                    </p>
                </div>
            ),
        },
        {
            title: "No. of Questions",
            dataIndex: "questionCount",
            key: "questionCount",
            align: "center",
            className: `${roboto.className}`,
            render: (questionCount) => (
                <div className="px-[20px] flex justify-center">
                    <p className="text-left">{questionCount}</p>
                </div>
            ),
        },
        {
            title: t("Grade Status"),
            dataIndex: "status",
            key: "status",
            align: "center",
            width: "150px",
            className: `${roboto.className}`,
            render: (status: string, record: any) => (
                <div className="flex justify-center items-center cursor-pointer">
                    {record.id && (
                        <Switch
                            checked={status.toLowerCase() === "enabled"}
                            onChange={() =>
                                updateStatusOfGrade(record.id, record)
                            }
                            color="primary"
                            sx={{
                                "& .MuiSwitch-switchBase": {
                                    color: "#CBCBCB",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked": {
                                    color: "#67A1A3",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                                    {
                                        backgroundColor: "#67A1A3",
                                    },
                                "& .MuiSwitch-track": {
                                    backgroundColor:
                                        "rgba(211, 217, 231, 0.80)",
                                },
                            }}
                            size="medium"
                        />
                    )}
                </div>
            ),
        },
    ];

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`bg-white text-[24px] text-black items-center font-[400] mt-4 ${roboto.className}`}
            >
                <div className="flex items-center px-4 mb-5">
                    <div className="flex items-center font-[600] text-[23px]">
                        {t("Manage Grade")}
                    </div>
                </div>
                {isTableDataLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <div className="w-full">
                        <Table
                            columns={ManageGradeDataColumns}
                            dataSource={data}
                            bordered={false}
                            rowKey="id"
                            className={`custom-table ${
                                data.length > 5
                                    ? "custom-table-scroll"
                                    : "custom-table-scroll-hide"
                            }  scrollbar font-[400]`}
                            pagination={false}
                            loading={{
                                indicator: <Spin size="default" />,
                                spinning: isTableDataLoading,
                            }}
                            scroll={{
                                y: "66vh",
                            }}
                            locale={{
                                emptyText: (
                                    <div
                                        className={`h-[61vh] flex items-center justify-center font-roboto font-[500]`}
                                    >
                                        <Empty
                                            description={t("No data available")}
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        />
                                    </div>
                                ),
                            }}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default ManageGrade;

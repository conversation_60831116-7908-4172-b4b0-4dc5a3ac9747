import { fetch } from "@/src/libs/helpers";

export interface uploadDocumentParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    search_column?: string;
    subject?: string;
    purchaseDocId?: string;
    introDocId?: string;
    levelId?: string;
}

export interface uploadDocumentDetails {
    subject?: string;
    docUrl?: string;
    grade?: string;
    fileName?: string;
}

// Introduction Pdf APIs:
export const createIntroductionPdf = async (
    payload: uploadDocumentDetails,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/introduction-doc/add-doc`,
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getAllIntroductionPdf = async (
    params: uploadDocumentParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/introduction-doc/get-all-docs`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateIntroductionPdf = async (
    introDocId: string,
    params: uploadDocumentParams,
    payload: uploadDocumentDetails,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/introduction-doc/${introDocId}/update-doc`,
        method: "PUT",
        data: payload,
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deleteIntroductionPdf = async (
    introDocId: string,
    params: uploadDocumentParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/introduction-doc/${introDocId}/delete-doc`,
        method: "DELETE",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

// Purchase Workbook APIs:
export const createPurchaseWorkbook = async (
    payload: uploadDocumentDetails,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/purchase-information-doc/add-purchase-doc`,
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getPurchaseWorkbook = async (
    params: uploadDocumentParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/purchase-information-doc/get-all-purchase-docs`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deletePurchaseWorkbook = async (
    purchaseDocId: string,
    params: uploadDocumentParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/purchase-information-doc/${purchaseDocId}/delete-purchase-doc`,
        method: "DELETE",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

// Level Apis
export const getAllLevels = async (
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/level/get-level`,
        method: "GET",
        headers: {
            Authorization: authorization,
        },
    });
};

export const getLevelsByGrade = async (
    params: uploadDocumentDetails,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/level/get-grade-wise-levels`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};
"use client";
import React, { useState, useEffect, useCallback } from "react";
import Navbar from "../Navbar/navbar";
import "@/app/globals.css";
import Image from "next/image";
import {
    Dropdown,
    Radio,
    Table,
    Menu,
    MenuProps,
    Avatar,
    Spin,
    Input,
    message,
    Empty,
    DatePicker,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import moment from "moment";
import dayjs from "dayjs";
import {
    QueryParams,
    getChilUserdata,
    getChilUserdataV2,
    getParentUserdata,
    changeUserStatus,
} from "@/src/services/users.api";
import { UserOutlined } from "@ant-design/icons";
import { userType } from "@/src/libs/constants";
import { CircularProgress, Switch } from "@mui/material";
import { debounce } from "lodash";
import { Roboto } from "next/font/google";
import { useTranslation } from "react-i18next";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface User {
    srNo: number;
    profilePic?: string;
    username?: string;
    email?: string;
    status?: string;
    childName?: string;
    parentName?: string;
    childCode?: string;
    childGrade?: string;
    total?: number;
    id: string;
    avatarUrl: string;
}

const Users = () => {
    const [selectedUser, setSelectedUser] = useState("PARENT");
    const [searchQuery, setSearchQuery] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] =
        useState(searchQuery);
    const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState(true);
    const [data, setData] = useState<User[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [response, setResponse] = useState<any>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [isTabledataLoading, setIsTableDataLoading] = useState(false);
    const [dateOption, setDateOption] = useState("custom");
    const [dateRange, setDateRange] = useState<[any, any]>([null, null]);
    const [isFilterOpen, setIsFilterOpen] = useState(false);
    const [pageSize, setPageSize] = useState<number>(20);
    const { t } = useTranslation();

    const handleSearch = useCallback(
        debounce((query: any) => {
            setDebouncedSearchQuery(query); // update debounced query when user stops typing
        }, 500),
        []
    );

    useEffect(() => {
        if (currentPage === 1) {
            fetchData(1);
        } else {
            setCurrentPage(1);
        }
    }, [selectedUser, dateRange, debouncedSearchQuery]);

    useEffect(() => {
        fetchData(currentPage);
    }, [selectedUser, currentPage]);

    const fetchData = async (page: number = 1) => {
        const queryParams: QueryParams = {
            type: selectedUser,
            skip: (page - 1) * pageSize,
            take: pageSize,
            orderBy: "createdAt|desc",
            search: debouncedSearchQuery,
            ...(dateRange &&
                dateRange[0] &&
                dateRange[1] && {
                    startDate: dateRange[0].toISOString(),
                    endDate: dateRange[1].toISOString(),
                }),
        };
        const authorization = localStorage.getItem("idToken");
        if (selectedUser === "CHILD") {
            try {
                setIsTableDataLoading(true);
                const response = await getChilUserdataV2(
                    queryParams,
                    authorization
                );
                setResponse(response);
                if (response) {
                    const updatedData = response.list.map(
                        (item: any, index: any) => ({
                            ...item,
                            srNo: (page - 1) * pageSize + index + 1,
                        })
                    );
                    setData(updatedData);
                    setTotal(response.total);
                } else {
                    message.error(t("Empty response data")); // Empty response data
                }
            } catch (error) {
                message.error(t("Data could not be retrieved")); // Failed to fetch data
            }
            setIsTableDataLoading(false);
            setIsLoading(false);
        } else {
            try {
                setIsTableDataLoading(true);
                const response = await getParentUserdata(
                    queryParams,
                    authorization
                );
                setResponse(response);
                if (response) {
                    const updatedData = response.list.map(
                        (item: any, index: any) => ({
                            ...item,
                            srNo: (page - 1) * pageSize + index + 1,
                        })
                    );
                    setData(updatedData);
                    setTotal(response.total);
                } else {
                    message.error(t("Empty response data")); // Empty response data
                }
            } catch (error) {
                message.error(t("Data could not be retrieved")); // Failed to fetch data
            }
            setIsLoading(false);
            setIsTableDataLoading(false);
        }
    };

    console.log("dateRange", dateRange);

    const handleInputChange = (e: any) => {
        setSearchQuery(e.target.value);
        handleSearch(e.target.value); // call debounced function
    };

    const handleRadioChange = (e: any) => {
        setSelectedUser(e);
        setDropdownVisible(false);
    };

    const handleItemClick = (key: any) => {
        setSelectedUser(key);
        setDropdownVisible(false);
    };

    const handleToggle = async (id: string, page: number = 1) => {
        try {
            const authorization = localStorage.getItem("idToken");
            const response = await changeUserStatus(authorization, id);
            await fetchData(currentPage);
            // message.success(response.message);
            message.success("Parent Status updated successfully");
        } catch (error) {
            message.error("Failed to change status");
        }
    };

    const items: MenuProps["items"] = userType.map((item) => ({
        key: item.value,
        label: (
            <div
                className="flex justify-between items-center"
                onClick={() => handleItemClick(item.value)}
            >
                <span
                    className={`text-[14px] font-roboto font-medium mr-[50px] ${
                        selectedUser === item.value
                            ? "text-black"
                            : "text-gray-500"
                    }`}
                >
                    {item.label === "Child" ? t("child") : t("parent")}
                </span>
                <Radio
                    value={item.value}
                    checked={selectedUser === item.value}
                    className={`custom-radio ${
                        selectedUser === item.value ? "accent-[#67A1A3]" : ""
                    }`}
                    onChange={() => handleRadioChange(item.value)}
                />
            </div>
        ),
    }));

    // Filter for date range
    const filterItems: MenuProps["items"] = [
        {
            key: "today",
            label: (
                <div
                    className="flex justify-between items-center"
                    onClick={() => {
                        setDateOption("today");
                        setDateRange([
                            moment().utc().startOf("day"),
                            moment().utc().endOf("day"),
                        ]);
                        setIsFilterOpen(false);
                    }}
                >
                    <span
                        className={`text-[14px] font-roboto font-medium mr-[50px] ${
                            dateOption === "today"
                                ? "text-black bg-transparent"
                                : "text-gray-500"
                        }`}
                    >
                        {t("Today")}
                    </span>
                    <Radio
                        value="today"
                        checked={dateOption === "today"}
                        className={`custom-radio ${
                            dateOption === "today" ? "accent-[#67A1A3]" : ""
                        }`}
                        onChange={() => {
                            setDateOption("today");
                            setDateRange([
                                moment().utc().startOf("day"),
                                moment().utc().endOf("day"),
                            ]);
                            setIsFilterOpen(false);
                        }}
                    />
                </div>
            ),
        },
        {
            key: "week",
            label: (
                <div
                    className="flex justify-between items-center"
                    onClick={() => {
                        setDateOption("week");
                        setDateRange([
                            moment().utc().startOf("week"),
                            moment().utc().endOf("week"),
                        ]);
                        setIsFilterOpen(false);
                    }}
                >
                    <span
                        className={`text-[14px] font-roboto font-medium mr-[50px] ${
                            dateOption === "week"
                                ? "text-black"
                                : "text-gray-500"
                        }`}
                    >
                        {t("This Week")}
                    </span>
                    <Radio
                        value="week"
                        checked={dateOption === "week"}
                        className={`custom-radio ${
                            dateOption === "week" ? "accent-[#67A1A3]" : ""
                        }`}
                        onChange={() => {
                            setDateOption("week");
                            setDateRange([
                                moment().utc().startOf("week"),
                                moment().utc().endOf("week"),
                            ]);
                            setIsFilterOpen(false);
                        }}
                    />
                </div>
            ),
        },
        {
            key: "month",
            label: (
                <div
                    className="flex justify-between items-center"
                    onClick={() => {
                        setDateOption("month");
                        setDateRange([
                            moment().utc().startOf("month"),
                            moment().utc().endOf("month"),
                        ]);
                        setIsFilterOpen(false);
                    }}
                >
                    <span
                        className={`text-[14px] font-roboto font-medium mr-[50px] ${
                            dateOption === "month"
                                ? "text-black"
                                : "text-gray-500"
                        }`}
                    >
                        {t("This Month")}
                    </span>
                    <Radio
                        value="month"
                        checked={dateOption === "month"}
                        className={`custom-radio ${
                            dateOption === "month" ? "accent-[#67A1A3]" : ""
                        }`}
                        onChange={() => {
                            setDateOption("month");
                            setDateRange([
                                moment().utc().startOf("month"),
                                moment().utc().endOf("month"),
                            ]);
                            setIsFilterOpen(false);
                        }}
                    />
                </div>
            ),
        },
        {
            key: "custom",
            label: (
                <div
                    className="flex justify-between items-center"
                    onClick={() => {
                        setDateOption("custom");
                        setDateRange([null, null]);
                        setIsFilterOpen(false);
                    }}
                >
                    <span
                        className={`text-[14px] font-roboto font-medium mr-[50px] ${
                            dateOption === "custom"
                                ? "text-black"
                                : "text-gray-500"
                        }`}
                    >
                        {t("Custom")}
                    </span>
                    <Radio
                        value="custom"
                        checked={dateOption === "custom"}
                        className={`custom-radio ${
                            dateOption === "custom" ? "accent-[#67A1A3]" : ""
                        }`}
                        onChange={() => {
                            setDateOption("custom");
                            setDateRange([null, null]);
                            setIsFilterOpen(false);
                        }}
                    />
                </div>
            ),
        },
    ];

    const parentColumns: ColumnsType<User> = [
        {
            title: <div className="ml-2">{t("Sr. No.")}</div>,
            dataIndex: "srNo",
            key: "srNo",
            align: "left",
            width: 80,
            className: `${roboto.className}`,
            render: (srNo: string) => (
                <div className="pl-[20px] font-medium">{srNo}.</div>
            ),
        },
        {
            title: t("Image"),
            dataIndex: "profilePic",
            key: "profilePic",
            align: "center",
            width: 80,
            render: (profilePic: string) => (
                <div className="flex justify-center items-center">
                    {profilePic === null || profilePic === "" ? (
                        <Avatar
                            size="large"
                            className="shadow-lg custom-avatar-size"
                            icon={<UserOutlined />}
                        />
                    ) : (
                        <Avatar
                            size="large"
                            src={profilePic}
                            alt="Profile"
                            className="border-none shadow-lg custom-avatar-size"
                        />
                    )}
                </div>
            ),
            className: `${roboto.className}`,
        },
        {
            title: t("Full Name"),
            dataIndex: "username",
            key: "username",
            align: "center",
            className: `${roboto.className}`,
            render: (username) => <div>{username ? username : "-"}</div>,
        },
        {
            title: "Mail",
            dataIndex: "email",
            key: "email",
            align: "center",
            className: `${roboto.className}`,
            render: (email) => <div>{email ? email : "-"}</div>,
        },
        {
            title: "Last Used Plaform",
            dataIndex: "lastUsedPlatform",
            key: "lastUsedPlatform",
            align: "center",
            className: `${roboto.className}`,
            width: 150,
            render: (lastUsedPlatform) => (
                <div>
                    {lastUsedPlatform
                        ? lastUsedPlatform === "WEB"
                            ? "Website"
                            : "Application"
                        : "-"}
                </div>
            ),
        },
        {
            title: t("Last Sign-in Date"),
            dataIndex: "signinDate",
            key: "signinDate",
            align: "center",
            className: `${roboto.className}`,
            // render: (signupDate) => <div>{signupDate ? signupDate : "-"}</div>,
            render: (signinDate) => {
                if (signinDate !== null) {
                    // Ensure createdAt is a valid date
                    const date = new Date(signinDate);

                    // Format date: DD-MM-YYYY
                    const formattedDate = date.toLocaleDateString("en-GB", {
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                    });

                    // Format time: HH:MM AM/PM
                    const formattedTime = date
                        .toLocaleTimeString("en-US", {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: true, // Use 12-hour format
                        })
                        .toLowerCase(); // Convert 'AM/PM' to lowercase

                    return (
                        <div>
                            {formattedDate}, {formattedTime}
                        </div>
                    );
                } else {
                    return <div>-</div>;
                }
            },
        },
        {
            title: t("Sign-up Date"),
            dataIndex: "signupDate",
            key: "signupDate",
            align: "center",
            className: `${roboto.className}`,
            // render: (signupDate) => <div>{signupDate ? signupDate : "-"}</div>,
            render: (signupDate) => {
                if (signupDate !== null) {
                    // Ensure createdAt is a valid date
                    const date = new Date(signupDate);

                    // Format date: DD-MM-YYYY
                    const formattedDate = date.toLocaleDateString("en-GB", {
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                    });

                    // Format time: HH:MM AM/PM
                    const formattedTime = date
                        .toLocaleTimeString("en-US", {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: true, // Use 12-hour format
                        })
                        .toLowerCase(); // Convert 'AM/PM' to lowercase

                    return (
                        <div>
                            {formattedDate}, {formattedTime}
                        </div>
                    );
                } else {
                    return <div>-</div>;
                }
            },
        },
        {
            title: t("Parents Status"),
            dataIndex: "status",
            key: "status",
            align: "center",
            width: 150,
            render: (status: string, record: User) => (
                <div className="flex justify-center items-center cursor-pointer">
                    {record.id ? (
                        <Switch
                            checked={status.toLowerCase() === "enabled"}
                            onChange={() => handleToggle(record.id)}
                            color="primary"
                            sx={{
                                "& .MuiSwitch-switchBase": {
                                    color: "#CBCBCB",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked": {
                                    color: "#67A1A3",
                                },
                                "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                                    {
                                        backgroundColor: "#67A1A3",
                                    },
                                "& .MuiSwitch-track": {
                                    backgroundColor:
                                        "rgba(211, 217, 231, 0.80)",
                                },
                            }}
                            size="medium"
                        />
                    ) : (
                        <CircularProgress size={20} />
                    )}
                </div>
            ),
            className: `${roboto.className}`,
        },
    ];

    const childColumns: ColumnsType<User> = [
        {
            title: <div className="ml-2">{t("Sr. No.")}</div>,
            dataIndex: "srNo",
            key: "srNo",
            align: "left",
            className: `${roboto.className}`,
            width: 80,
            render: (srNo) => <div className="pl-[20px]">{srNo}.</div>,
        },
        {
            title: t("Image"),
            dataIndex: "avatar",
            key: "avatar",
            align: "center",
            width: 80,
            render: (avatar: string, record: User) => (
                <div>
                    {avatar === null || avatar === "" ? (
                        <Avatar
                            size="large"
                            className="border-none shadow-lg custom-avatar-size"
                        >
                            <UserOutlined />
                        </Avatar>
                    ) : avatar === "MALE" && record?.avatarUrl === null ? (
                        <Avatar
                            size="large"
                            src="/images/Avatar_male.svg"
                            alt="Male Profile"
                            className="border-none shadow-lg custom-avatar-size"
                        />
                    ) : avatar === "FEMALE" && record?.avatarUrl === null ? (
                        <Avatar
                            size="large"
                            src="/images/Avatar_female.svg"
                            alt="Female Profile"
                            className="border-none shadow-lg custom-avatar-size"
                        />
                    ) : (
                        <Avatar
                            size="large"
                            src={record?.avatarUrl}
                            alt="Profile"
                            className="border-none shadow-lg custom-avatar-size"
                        />
                    )}
                </div>
            ),
            className: `${roboto.className}`,
        },
        {
            title: t("Child Name"),
            dataIndex: "name",
            key: "name",
            align: "center",
            className: `${roboto.className}`,
        },
        {
            title: t("Parent Name"),
            dataIndex: "parentChildRelation",
            key: "parentChildRelation",
            align: "center",
            className: `${roboto.className}`,
            render: (parentChildRelation: any) => (
                <div>
                    {parentChildRelation &&
                    parentChildRelation[0]?.parent?.username
                        ? parentChildRelation[0]?.parent?.username
                        : "-"}
                </div>
            ),
        },
        {
            title: "Last Used Plaform",
            dataIndex: "lastUsedPlatform",
            key: "lastUsedPlatform",
            align: "center",
            className: `${roboto.className}`,
            width: 150,
            render: (lastUsedPlatform) => (
                <div>
                    {lastUsedPlatform
                        ? lastUsedPlatform === "WEB"
                            ? "Website"
                            : "Application"
                        : "-"}
                </div>
            ),
        },
        {
            title: t("Last Verified Date"),
            dataIndex: "signinDate",
            key: "signinDate",
            align: "center",
            className: `${roboto.className}`,
            render: (signupDate) => {
                if (signupDate !== null) {
                    const date = new Date(signupDate);

                    const formattedDate = date.toLocaleDateString("en-GB", {
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                    });
                    const formattedTime = date
                        .toLocaleTimeString("en-US", {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: true,
                        })
                        .toLowerCase();

                    return (
                        <div>
                            {formattedDate}, {formattedTime}
                        </div>
                    );
                } else {
                    return <div>-</div>;
                }
            },
        },
        {
            title: t("Question Count"),
            dataIndex: "attemptedQuestionCount",
            key: "attemptedQuestionCount",
            align: "center",
            className: `${roboto.className}`,
            width: 130,
            render: (attemptedQuestionCount: string) => (
                <div>{attemptedQuestionCount ? attemptedQuestionCount : 0}</div>
            ),
        },
        {
            title: t("Child's Code"),
            dataIndex: "accessCode",
            key: "accessCode",
            align: "center",
            className: `${roboto.className}`,
            width: 130,
            render: (accessCode: string) => (
                <div>{accessCode ? accessCode : "-"}</div>
            ),
        },
        {
            title: t("Child's Grade"),
            dataIndex: "grade",
            key: "grade",
            align: "center",
            className: `${roboto.className}`,
            width: 130,
            render: (grade: string | null) => (
                <div>
                    {grade === null
                        ? "-"
                        : grade === "THIRD"
                        ? "3rd Grade"
                        : "4th Grade"}
                </div>
            ),
        },
    ];

    const calculatePageSize = () => {
        const height = window.innerHeight;
        const rowsPerPage = Math.floor((height - 200) / 60);
        return rowsPerPage;
    };

    useEffect(() => {
        const handleResize = () => {
            const newSize = calculatePageSize();
        };

        handleResize();
        window.addEventListener("resize", handleResize);

        return () => window.removeEventListener("resize", handleResize);
    }, []);

    const paginationConfig = {
        pageSize: pageSize,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`${roboto.className} w-full bg-white text-[24px] text-black items-center mt-4`}
            >
                {isLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <>
                        <div className="flex items-center justify-between px-4 mb-2">
                            <div>
                                <div className="flex">
                                    <div className="flex items-center font-[600] text-[23px]">
                                        {t("users")}
                                        <span className="text-gray-400 font-semibold text-[18px] font-roboto ml-2">
                                            ({response ? response.total : 0})
                                        </span>
                                    </div>
                                    <Dropdown
                                        menu={{ items }}
                                        trigger={["click"]}
                                        placement="bottomLeft"
                                        open={dropdownVisible}
                                        onOpenChange={setDropdownVisible}
                                    >
                                        <Image
                                            src={
                                                dropdownVisible
                                                    ? "/images/arrow-up.svg"
                                                    : "/images/arrow-bottom.svg"
                                            }
                                            alt="toggle dropdown"
                                            width={30}
                                            height={30}
                                            className="cursor-pointer pl-[9px]"
                                        />
                                    </Dropdown>
                                </div>
                                <div className="text-black text-[16px] font-normal">
                                    {selectedUser === "CHILD"
                                        ? t("child")
                                        : t("parent")}
                                </div>
                            </div>
                            <div className="flex justify-center items-center">
                                <div className="text-[16px] font-normal text-black flex items-center px-4 relative">
                                    <Input
                                        size="large"
                                        placeholder={t("search")}
                                        prefix={
                                            <Image
                                                src="/images/search_1.svg"
                                                width={24}
                                                height={24}
                                                alt="search"
                                                className="mr-2"
                                            />
                                        }
                                        allowClear={{
                                            clearIcon: (
                                                <Image
                                                    src="/images/cross.svg"
                                                    width={22}
                                                    height={22}
                                                    alt="close"
                                                />
                                            ),
                                        }}
                                        value={searchQuery}
                                        onChange={handleInputChange}
                                        suffix={
                                            <div
                                                className="h-[38px] bg-[#F6FAF9CC] antd-search-filter-btn border-none border-[#D3E3E3] rounded-none rounded-tr-[10px] rounded-br-[10px] px-2 flex items-center gap-2 cursor-pointer relative"
                                                style={{
                                                    borderLeft:
                                                        "1.5px solid #D3E3E3",
                                                }}
                                            >
                                                <Dropdown
                                                    menu={{
                                                        items: filterItems,
                                                    }}
                                                    open={isFilterOpen}
                                                    onOpenChange={
                                                        setIsFilterOpen
                                                    }
                                                    trigger={["click"]}
                                                    placement="bottomRight"
                                                    overlayClassName={`${roboto.className}`}
                                                >
                                                    <Image
                                                        src={
                                                            dateOption ===
                                                                "today" ||
                                                            dateOption ===
                                                                "week" ||
                                                            dateOption ===
                                                                "month"
                                                                ? `/images/sideBar/selectedSomeFilter.svg`
                                                                : `/images/sideBar/filterIcon.svg`
                                                        }
                                                        width={24}
                                                        height={24}
                                                        alt="filter"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            setIsFilterOpen(
                                                                !isFilterOpen
                                                            );
                                                        }}
                                                    />
                                                </Dropdown>
                                            </div>
                                        }
                                        className="bg-[#F6FAF9CC] antd-search w-[350px] border rounded-[10px] border-[#D3E3E3] py-0 pr-0 h-[40px]"
                                        style={{
                                            boxShadow:
                                                "0px 2px 4px 0px #0000001A",
                                        }}
                                    />
                                </div>
                                <div className="flex items-center justify-center space-x-3">
                                    {dateOption === "custom" && (
                                        <DatePicker.RangePicker
                                            className={`${roboto.className} bg-[#D9D9D9] bg-opacity-[20%] rounded-lg h-[40px]`}
                                            style={{
                                                boxShadow:
                                                    "0px 2px 4px 0px #0000001A",
                                                width: "250px",
                                            }}
                                            format="DD-MM-YYYY"
                                            placeholder={[
                                                t("Start Date"),
                                                t("End Date"),
                                            ]}
                                            onChange={(dates: any) => {
                                                if (
                                                    dates &&
                                                    dates.length === 2
                                                ) {
                                                    const [start, end] = dates;
                                                    const startDate = start
                                                        .clone()
                                                        .startOf("day"); // 00:00:00
                                                    const endDate = end
                                                        .clone()
                                                        .endOf("day"); // 23:59:59
                                                    setDateRange([
                                                        startDate,
                                                        endDate,
                                                    ]);
                                                } else {
                                                    setDateRange([null, null]);
                                                }
                                            }}
                                            allowClear={true}
                                            popupClassName="custom-date-picker"
                                            disabledDate={(current) => {
                                                return (
                                                    current &&
                                                    current >
                                                        moment().endOf("day")
                                                );
                                            }}
                                            panelRender={(panel) => (
                                                <div className="ant-picker-panel-single">
                                                    {panel}
                                                </div>
                                            )}
                                        />
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="w-full">
                            {selectedUser === "CHILD" ? (
                                <Table
                                    columns={childColumns}
                                    dataSource={data}
                                    pagination={{
                                        ...paginationConfig,
                                        className:
                                            "custom-pagination custom-select",
                                    }}
                                    bordered={false}
                                    rowKey="srNo"
                                    className={`custom-table ${
                                        data.length > 5
                                            ? "custom-table-scroll"
                                            : "custom-table-scroll-hide"
                                    } scrollbar font-[400]`}
                                    loading={{
                                        indicator: <Spin size="default" />,
                                        spinning: isTabledataLoading,
                                    }}
                                    scroll={{
                                        y: "67vh",
                                    }}
                                    locale={{
                                        emptyText: (
                                            <div
                                                className={`h-[61vh] flex items-center justify-center font-roboto font-[500]`}
                                            >
                                                <Empty
                                                    description={t(
                                                        "No data available"
                                                    )}
                                                    image={
                                                        Empty.PRESENTED_IMAGE_SIMPLE
                                                    }
                                                />
                                            </div>
                                        ),
                                    }}
                                />
                            ) : (
                                <Table
                                    columns={parentColumns}
                                    dataSource={data}
                                    pagination={{
                                        ...paginationConfig,
                                        className:
                                            "custom-pagination custom-select",
                                    }}
                                    bordered={false}
                                    rowKey="srNo"
                                    className={`custom-table ${
                                        data.length > 5
                                            ? "custom-table-scroll"
                                            : "custom-table-scroll-hide"
                                    } scrollbar font-[400]`}
                                    loading={{
                                        indicator: (
                                            <Spin
                                                size="default"
                                                className="h-full mt-[100px] mb-[50px]"
                                            />
                                        ),
                                        spinning: isTabledataLoading,
                                    }}
                                    scroll={{
                                        y: "65vh",
                                    }}
                                    locale={{
                                        emptyText: (
                                            <div
                                                className={`h-[61vh] flex items-center justify-center font-roboto font-[500]`}
                                            >
                                                <Empty
                                                    description={t(
                                                        "No data available"
                                                    )}
                                                    image={
                                                        Empty.PRESENTED_IMAGE_SIMPLE
                                                    }
                                                />
                                            </div>
                                        ),
                                    }}
                                />
                            )}
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default Users;

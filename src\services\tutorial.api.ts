import { fetch } from "@/src/libs/helpers";

export interface addTutorialQueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    userType?: string;
}

export interface tutorialData {
    type?: string | null;
    value?: string | null;
}

export const getUserTutorial = async (
    queryParams: addTutorialQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/tutorial",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const addNewTutorialVideo = async (
    payload: tutorialData,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/tutorial",
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateTutorialVideo = async (
    tutorialId: string,
    payload: tutorialData,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/tutorial/${tutorialId}`,
        method: "PUT",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deleteTutorialVideo = async (
    tutorialId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/tutorial/${tutorialId}`,
        method: "DELETE",
        headers: {
            Authorization: authorization,
        },
    });
};

"use client";
import React, { useEffect, useState } from "react";
import SideBar from "@/app/sideBar/page";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { Input, Select, Button, message, Spin } from "antd";
import { promotionType } from "@/src/libs/constants";
import {
    QueryParams,
    deleteUploadedFile,
    uploadFile,
} from "@/src/services/upload.api";
import {
    blogQueryParams,
    blogBody,
    getAllCategory,
    updateBlog,
    createBlog,
} from "@/src/services/blog.api";
import { Dropdown, Menu } from "antd";
import { Roboto } from "next/font/google";
import { DatePicker } from "antd";
// import moment from "moment";
import moment, { Moment } from "moment";
import dayjs from "dayjs";
import TextArea from "antd/es/input/TextArea";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";
import modalStyle from "./blog.module.css";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const { Option } = Select;

const modules = {
    toolbar: [
        [{ header: "1" }, { header: "2" } /*{ 'font': [] }*/],
        [{ size: ["small", "medium", "large", "huge"] }],
        ["bold", "italic", "underline", "strike", "blockquote"],
        [
            { list: "ordered" },
            { list: "bullet" },
            { indent: "-1" },
            { indent: "+1" },
        ],
        ["link"],
        ["clean"],
    ],
};

const AddEditBlog = ({
    isAddEditBlogOpen,
    setIsAddEditBlogOpen,
    fetchedCategories,
    record,
    setRecord,
    mode,
}: any) => {
    const [maintitle, setMaintitle] = useState("");
    const [subtitle, setSubtitle] = useState("");
    const [category, setCategory] = useState<string | null>(null);
    const [description, setDescription] = useState("");
    const [loading, setLoading] = useState(false);
    const [shortDescription, setShortDescription] = useState("");
    const [blogImage, setBlogImage] = useState<
        {
            label: string;
            id: number;
            value: string | null;
        }[]
    >([{ label: "image1", id: 1, value: null }]);
    const [isImageUploadingAndDelete, setIsImageUploadingAndDelete] =
        useState(false);
    const { t } = useTranslation();

    // Function to handle text input and ensure max length
    // const handleChange = (
    //     e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>
    // ) => {
    //     const value = e.target.value;
    //     if (value.length <= maxLength) {
    //         setDescription(value);
    //     }
    // };

    const handleChange = (value: string) => {
        setDescription(value);
    };

    const handleBlur = () => {
        setDescription((prev) => prev.trim());
    };

    const router = useRouter();

    const handleImageChange = async (
        e: React.ChangeEvent<HTMLInputElement>,
        index: number
    ) => {
        const file = e.target.files?.[0];
        if (file) {
            const trimmedFileName = file.name.replace(/\s+/g, "_");
            const queryParams: QueryParams = {
                fileName: trimmedFileName,
                fileType: "IMAGE",
                resourceType: "BLOG",
            };
            const authorization = localStorage.getItem("idToken");

            try {
                setIsImageUploadingAndDelete(true);
                const response = await uploadFile(queryParams, authorization);

                if (response && response.preSignedUrl) {
                    const url = response.preSignedUrl;
                    try {
                        await fetch(url, {
                            method: "PUT",
                            body: file,
                            headers: {
                                "Content-Type": file.type,
                            },
                        });

                        const newBlogImages = [...blogImage];
                        newBlogImages[index].value = response.outPutUrl;
                        setBlogImage(newBlogImages);
                    } catch (uploadError) {
                        message.error(t("Image upload failed"));
                    }
                    setIsImageUploadingAndDelete(false);
                } else {
                    message.error(t("Invalid presigned URL response"));
                    setIsImageUploadingAndDelete(false);
                }
            } catch (apiError) {
                message.error(t("Failed to get presigned URL"));
                setIsImageUploadingAndDelete(false);
            }
        }
    };

    const deleteBlogImage = async (index: number) => {
        const imageUrl = blogImage[index].value;

        if (imageUrl) {
            const queryParams: QueryParams = {
                fileName: imageUrl.split("/").pop(),
                fileType: "IMAGE",
                resourceType: "BLOG",
            };
            const authorization = localStorage.getItem("idToken");
            try {
                setIsImageUploadingAndDelete(true);
                await deleteUploadedFile(queryParams, authorization);
                const newBlogImages = [...blogImage];
                newBlogImages[index] = {
                    ...newBlogImages[index],
                    value: null,
                };
                setBlogImage(newBlogImages);
                setIsImageUploadingAndDelete(false);
            } catch (error) {
                message.error(t("Error deleting image"));
                setIsImageUploadingAndDelete(false);
            }
        }
    };

    const handleAddFromGallery = (index: number) => {
        const inputFileElement = document.getElementById(`inputFile${index}`);
        if (inputFileElement) {
            (inputFileElement as HTMLInputElement).click();
        }
    };

    const menu = (index: any) => (
        <Menu>
            <Menu.Item
                key="1"
                onClick={() => handleAddFromGallery(index)}
                className="font-roboto"
            >
                <div className={`flex text-[14px] ${roboto.className}`}>
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    {t("Add From gallery")}
                </div>
            </Menu.Item>
            {blogImage?.[0]?.value !== null && (
                <Menu.Item
                    key="2"
                    onClick={() => deleteBlogImage(index)}
                    className="font-roboto"
                >
                    <div
                        className={`text-[#F1676D] flex text-[14px] ${roboto.className}`}
                    >
                        <Image
                            src="/images/trash.svg"
                            alt="visibility"
                            width={23}
                            height={23}
                            className="cursor-pointer mr-[7px] mb-1"
                        />
                        Delete this image
                    </div>
                </Menu.Item>
            )}
        </Menu>
    );

    const handleAddBlog = async () => {
        setLoading(true);

        if (
            (maintitle === null || maintitle === "") &&
            (subtitle === null || subtitle === "") &&
            category === null &&
            description === ""
        ) {
            message.warning(t("Fill all required field"));
            setLoading(false);
            return;
        }

        if (maintitle === null || maintitle === "") {
            message.warning(t("Blog Title should not be empty"));
            setLoading(false);
            return;
        } else if (maintitle.length > 100) {
            message.warning(t("Blog Title cannot exceed 100 characters"));
            setLoading(false);
            return;
        }

        if (category === null || category === "") {
            message.warning(t("Blog category should not be empty"));
            setLoading(false);
            return;
        }

        if (subtitle === null || subtitle === "") {
            message.warning(t("Blog subtitle should not be empty"));
            setLoading(false);
            return;
        } else if (subtitle.length > 255) {
            message.warning(t("Blog subtitle cannot exceed 255 characters"));
            setLoading(false);
            return;
        }

        if (description === null || description === "") {
            message.warning(t("Blog description should not be empty"));
            setLoading(false);
            return;
        }

        const allImagesUploaded = blogImage.every(
            (image) => image.value !== null
        );

        if (!allImagesUploaded) {
            message.warning(t("Please upload an image for the Blog"));
            setLoading(false);
            return;
        }

        const payload: blogBody = {
            mainTitle: maintitle,
            subTitle: subtitle,
            image: blogImage[0]?.value || "",
            description: description,
            shortDescription: shortDescription,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await createBlog(payload, category, authorization);
            if (response) {
                setIsAddEditBlogOpen(!isAddEditBlogOpen);
                message.success(t("Blog created successfully"));
            }
            router.push("/blog");
        } catch (error: any) {
            message.error(error.message || t("Failed to create the blog"));
        }
        setLoading(false);
    };

    const handleEditBlog = async () => {
        setLoading(true);

        if (
            (maintitle === null || maintitle === "") &&
            (subtitle === null || subtitle === "") &&
            category === null &&
            description === ""
        ) {
            message.error(t("Fill all required field"));
            setLoading(false);
            return;
        }

        if (maintitle === null || maintitle === "") {
            message.warning(t("Blog Title should not be empty"));
            setLoading(false);
            return;
        } else if (maintitle.length > 100) {
            message.warning(t("Blog Title cannot exceed 100 characters"));
            setLoading(false);
            return;
        }

        if (category === null || category === "") {
            message.warning(t("Blog category should not be empty"));
            setLoading(false);
            return;
        }

        if (subtitle === null || subtitle === "") {
            message.warning(t("Blog subtitle should not be empty"));
            setLoading(false);
            return;
        } else if (subtitle.length > 255) {
            message.warning(t("Blog subtitle cannot exceed 250 characters"));
            setLoading(false);
            return;
        }

        if (
            description === null ||
            description === "" ||
            description === "<p><br></p>"
        ) {
            message.warning(t("Blog description should not be empty"));
            setLoading(false);
            return;
        }

        const allImagesUploaded = blogImage.every(
            (image) => image.value !== null
        );

        if (!allImagesUploaded) {
            message.warning(t("Please upload an image for the blog"));
            setLoading(false);
            return;
        }

        const blogId = record?.id;

        const payload: blogBody = {
            mainTitle: maintitle,
            subTitle: subtitle,
            image: blogImage[0]?.value || "",
            description: description,
            shortDescription: shortDescription,
        };

        const parems: blogQueryParams = {
            blogId: blogId,
            categoryId: category,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await updateBlog(
                blogId,
                parems,
                payload,
                authorization
            );
            if (response) {
                setIsAddEditBlogOpen(!isAddEditBlogOpen);
                message.success(t("Blog successfully edited"));
            }
        } catch (error: any) {
            message.error(
                error.message === "Kategorie ist deaktiviert."
                    ? "Blog category is disabled. Please enable the category to edit!"
                    : t("Failed to edit the blog")
            );
        }
        setLoading(false);
    };

    useEffect(() => {
        if (record !== null && mode === "edit") {
            setMaintitle(record?.mainTitle);
            setSubtitle(record?.subTitle);
            setCategory(record?.categoryId);
            setDescription(record?.description);
            setShortDescription(record?.shortDescription);
            setBlogImage((prev) => [
                {
                    ...prev[0],
                    value: record?.image,
                },
            ]);
        }
    }, [record !== null && mode === "edit"]);

    return (
        <div className={`${roboto.className}`}>
            <div
                className={`${roboto.className} w-full shadow-md text-[25px] text-black font-bold h-auto flex justify-between items-center px-4 py-3 z-10 bg-white`}
            >
                <div>
                    <h1 className="text-[24px]">
                        {mode === "view"
                            ? t("View Blog")
                            : mode === "edit"
                            ? t("Edit Blog")
                            : t("Add Blog")}
                    </h1>
                </div>
                <div className="text-[20px] font-roboto text-black h-auto flex items-center px-4">
                    <div className="flex justify-end">
                        {mode !== "view" && (
                            <>
                                {mode === "edit" ? (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] mr-2 shadow-inner button right-0 font-roboto text-white text-[18px] w-[180px] h-[46px] font-medium rounded-xl"
                                        onClick={handleEditBlog}
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                ) : (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] mr-2 shadow-inner button right-0 font-roboto text-white text-[18px] w-[180px] h-[46px] font-medium rounded-xl"
                                        onClick={handleAddBlog}
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                )}
                            </>
                        )}
                    </div>
                    <Image
                        src="/images/cross.svg"
                        alt="cross"
                        width={28}
                        height={28}
                        className="cursor-pointer mr-[10px]"
                        onClick={() => {
                            setIsAddEditBlogOpen(!isAddEditBlogOpen);
                            setRecord(null);
                        }}
                    />
                </div>
            </div>
            <div className="mt-4 font-roboto w-full h-[calc(100vh-100px)] text-[18px] text-black font-semibold px-4 z-0 bg-white scrollbar overflow-y-auto">
                <div className="flex justify-start">
                    <div className="flex flex-col gap-4 w-1/2">
                        <div className="w-auto h-[259px] bg-[#D3E3E3] relative rounded-lg">
                            <Input
                                id="inputFile0"
                                type="file"
                                accept="image/*"
                                style={{ cursor: "pointer", display: "none" }}
                                onChange={(e) => handleImageChange(e, 0)}
                            />
                            {isImageUploadingAndDelete ? (
                                <div className="flex justify-center items-center w-full h-full absolute inset-0 z-10">
                                    <Spin size="default" />
                                </div>
                            ) : (
                                blogImage[0]?.value && (
                                    <img
                                        src={blogImage[0].value}
                                        alt="Uploaded"
                                        className="w-full h-full object-cover rounded-lg"
                                    />
                                )
                            )}
                            {mode !== "view" && (
                                <Dropdown
                                    overlay={menu(0)}
                                    trigger={["click"]}
                                    placement="bottomRight"
                                >
                                    <div
                                        className="absolute top-4 right-5 cursor-pointer bg-white border-opacity-80 rounded-md bg-opacity-60 border-2 w-[22px] h-[22px]"
                                        onClick={(e) => e.stopPropagation()}
                                    >
                                        <Image
                                            src="/images/more.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                            onClick={(e) => e.preventDefault()}
                                            className="cursor-pointer"
                                            style={{ fontSize: "20px" }}
                                        />
                                    </div>
                                </Dropdown>
                            )}
                        </div>
                    </div>
                    <div className="ml-4 flex flex-col justify-between">
                        <div>
                            <div className="flex items-center mb-4">
                                <h1 className="w-[150px]">
                                    Title{" "}
                                    {mode !== "view" && (
                                        <span className="text-red-500 font-[400]">
                                            *
                                        </span>
                                    )}{" "}
                                    :
                                </h1>
                                <TextArea
                                    className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                    value={maintitle}
                                    onChange={(e) =>
                                        setMaintitle(e.target.value)
                                    }
                                    onBlur={() =>
                                        setMaintitle(maintitle.trim())
                                    }
                                    placeholder={t("Enter title")}
                                    readOnly={mode === "view"}
                                    style={{
                                        pointerEvents:
                                            mode === "view" ? "none" : "auto",
                                    }}
                                />
                            </div>
                            <div className="flex items-center mb-4">
                                <h1 className="w-[150px]">
                                    {t("Subtitle")}{" "}
                                    {mode !== "view" && (
                                        <span className="text-red-500 font-[400]">
                                            *
                                        </span>
                                    )}{" "}
                                    :
                                </h1>
                                <TextArea
                                    className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                    value={subtitle}
                                    onChange={(e) =>
                                        setSubtitle(e.target.value)
                                    }
                                    onBlur={() => setSubtitle(subtitle.trim())}
                                    placeholder={t("Enter Subtitle")}
                                    readOnly={mode === "view"}
                                    style={{
                                        pointerEvents:
                                            mode === "view" ? "none" : "auto",
                                    }}
                                />
                            </div>
                            <div className="flex items-center mb-4">
                                <h1 className="w-[150px]">
                                    {t("Category")}
                                    {mode !== "view" && (
                                        <span className="text-red-500 font-[400]">
                                            *
                                        </span>
                                    )}{" "}
                                    :
                                </h1>
                                <Select
                                    className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[38vw] font-[400]"
                                    placeholder={t("Select Category")}
                                    value={category}
                                    onChange={(value) => setCategory(value)}
                                    onClick={(e) => {
                                        if (mode === "view") {
                                            e.preventDefault();
                                        }
                                    }}
                                    style={{
                                        pointerEvents:
                                            mode === "view" ? "none" : "auto",
                                    }}
                                    suffixIcon={
                                        mode !== "view" && (
                                            <Image
                                                src="/images/arrowI.svg"
                                                alt="More"
                                                width={20}
                                                height={20}
                                            />
                                        )
                                    }
                                >
                                    {fetchedCategories &&
                                        fetchedCategories.map((option: any) => (
                                            <Option
                                                key={option.id}
                                                value={option.id}
                                                disabled={
                                                    option.status !== "ENABLED"
                                                }
                                            >
                                                {option.category}
                                            </Option>
                                        ))}
                                </Select>
                            </div>
                            <div className="flex items-center mb-4">
                                <h1 className="w-[150px]">
                                    {t("Short description")}:
                                </h1>
                                <TextArea
                                    className="rounded-md font-roboto font-normal pl-[11px] h-[72px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9] resize-none overflow-hidden"
                                    value={shortDescription}
                                    onChange={(e) =>
                                        setShortDescription(e.target.value)
                                    }
                                    onBlur={() =>
                                        setShortDescription(
                                            shortDescription.trim()
                                        )
                                    }
                                    placeholder={t("Enter Short Description")}
                                    readOnly={mode === "view"}
                                    style={{
                                        pointerEvents:
                                            mode === "view" ? "none" : "auto",
                                    }}
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <p className={`text-[14px] text-[#67A1A3] ${roboto.className}`}>
                    *For best results, use a 1280x720 resolution or a 16:9
                    aspect ratio.
                </p>
                <div className="flex flex-col mb-5 mt-3">
                    <div className="flex items-start mb-2">
                        <h1 className="w-[150px]">
                            {t("description")}
                            {mode !== "view" && (
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>
                            )}{" "}
                            :
                        </h1>
                        <ReactQuill
                            className={`${roboto.className} customQuill rounded-md font-roboto font-normal h-[290px] ml-2 w-[80vw] border-[#D3E3E3] bg-[#F6FAF9]`}
                            style={{
                                border: "1px solid #D3E3E3",
                                borderRadius: "0.375rem",
                                backgroundColor: "#F6FAF9",
                                fontFamily: "Roboto",
                            }}
                            value={description}
                            onChange={(value: any) => handleChange(value)}
                            onBlur={handleBlur}
                            placeholder={t("Enter Blog Description")}
                            readOnly={mode === "view"}
                            modules={modules}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AddEditBlog;

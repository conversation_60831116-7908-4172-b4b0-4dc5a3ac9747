import React, {
    useMemo,
    useState,
    useCallback,
    useRef,
    useEffect,
} from "react";
import { useWavesurfer } from "@wavesurfer/react";
import Image from "next/image";

const Recorder = ({ url, bgColor }: any) => {
    // const [url, setUrl] = useState<any>(null);
    const [totalDuration, setTotalDuration] = useState(0);

    // useEffect(() => {
    //     const string = URL.createObjectURL(blob);
    //     setUrl(string);
    //     console.log("url====>", url);
    // }, []);

    const formatTime = (seconds: any) => {
        return [seconds / 60, seconds % 60]
            .map((v) => `0${Math.floor(v)}`.slice(-2))
            .join(":");
    };

    const containerRef = useRef(null);

    const { wavesurfer, isPlaying, currentTime } = useWavesurfer({
        container: containerRef,
        height: 40,
        // width: 1000,
        waveColor: "#F5F5F5",
        progressColor: "#FFFFFF",
        url: url,
    });

    useEffect(() => {
        if (wavesurfer) {
            // Add event listener for 'ready' event
            const onReady = () => {
                setTotalDuration(wavesurfer.getDuration());
            };
            wavesurfer.on("ready", onReady);
        }
    }, [wavesurfer]);

    const onPlayPause = useCallback(() => {
        wavesurfer && wavesurfer.playPause();
    }, [wavesurfer]);

    return (
        <div
            className="w-full rounded-[10px] p-2 flex gap-2 items-center"
            style={{ backgroundColor: bgColor }}
        >
            <div
                id="play"
                className="w-[32px] h-[32px] cursor-pointer flex items-center"
                onClick={onPlayPause}
            >
                <Image
                    src={
                        isPlaying
                            ? "/images/mails/pauseButton.svg"
                            : "/images/mails/Playbutton.svg"
                    }
                    width={32}
                    height={32}
                    alt=""
                    className="w-[32px] h-[32px] border rounded-lg object-cover"
                    style={{
                        filter:
                            bgColor === "#F4F4F4"
                                ? "grayscale(100%)"
                                : "invert(0)",
                        borderColor:
                            bgColor === "#F4F4F4" ? "gray" : "transparent",
                    }}
                />
            </div>
            <div className="w-[calc(100%-35px)] flex gap-1 items-center">
                <p
                    id="audio-timestamp"
                    className="mt-[2px] text-[12px] leading-[11.72px] font-[500] text-[#F4F4F4]"
                    style={{
                        color: bgColor === "#F4F4F4" ? "#000000" : "#F4F4F4",
                    }}
                >
                    {formatTime(currentTime)}
                </p>
                <div
                    className="w-[80%]"
                    ref={containerRef}
                    style={{
                        filter:
                            bgColor === "#F4F4F4" ? "invert(1)" : "invert(0)",
                    }}
                />
                <p
                    id="audio-total-duration"
                    className="text-[12px] leading-[11.72px] font-[500] text-[#F4F4F4]"
                    style={{
                        color: bgColor === "#F4F4F4" ? "#000000" : "#F4F4F4",
                    }}
                >
                    {formatTime(totalDuration)}
                </p>
            </div>
        </div>
    );
};

export default Recorder;

"use client";
import React, { useState, useEffect, useCallback, useRef } from "react";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import "@/app/globals.css";
import Image from "next/image";
import { Table, Spin, Button, message, Empty, Modal } from "antd";
import type { ColumnsType } from "antd/es/table";
import {
    blogQueryParams,
    blogBody,
    getAllCategory,
    updateBlogStatus,
    getAllBlogs,
    deleteBlog,
    reorderBlogs,
} from "@/src/services/blog.api";
import { CircularProgress, Switch } from "@mui/material";
import { Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { debounce } from "lodash";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";
import SelectedBlog from "./preview/selectedBlog";
import AddEditBlogModal from "./addEditBlog";
import AddEditCategory from "./addEditCategories";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const Blog = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] =
        useState(searchQuery);
    const [isButtonLoading, setButtonIsLoading] = useState(false);
    const [isAddEditBlogOpen, setIsAddEditBlogOpen] = useState(false);
    const [isAddEditCategory, setIsAddEditCategory] = useState(false);
    const [mode, setMode] = useState<any>(null);
    const [total, setTotal] = useState<number>(0);
    const [allBlogs, setAllBlogs] = useState<any>(null);
    const [record, setRecord] = useState<any>(null);
    const [fetchedCategories, setFetchedCategories] = useState<any>(null);
    const [selectedAnyBlog, setSelectedAnyBlog] = useState(false);
    const { t } = useTranslation();

    const router = useRouter();

    useEffect(() => {
        fetchAllBlogs();
        fetchAllCategory();
    }, [isAddEditBlogOpen, isAddEditCategory]);

    useEffect(() => {
        fetchAllBlogs();
    }, [debouncedSearchQuery]);

    const fetchAllCategory = async () => {
        try {
            const idToken: any = localStorage.getItem("idToken");
            const queryParams: blogQueryParams = {
                skip: 0,
                take: 100,
                orderBy: "createdAt|desc",
            };
            const res = await getAllCategory(queryParams, idToken);
            if (res) {
                setFetchedCategories(res.list);
            }
        } catch (error) {
            message.error("Error retrieving Category!");
        }
    };

    const fetchAllBlogs = async () => {
        try {
            setIsLoading(true);
            const idToken: any = localStorage.getItem("idToken");
            const queryParams: blogQueryParams = {
                skip: 0,
                take: 100,
                orderBy: "serialNo|asc",
                include: "category",
                search_column: "mainTitle",
                search: debouncedSearchQuery,
            };
            const res = await getAllBlogs(queryParams, idToken);
            if (res) {
                setAllBlogs(res.list);
                setIsLoading(false);
                setTotal(res.total);
            } else {
                setIsLoading(false);
            }
        } catch (error) {
            setIsLoading(false);
            message.error("Error retrieving blogs!");
        }
    };

    const fetchAllBlogsAfterReorder = async () => {
        try {
            const idToken: any = localStorage.getItem("idToken");
            const queryParams: blogQueryParams = {
                skip: 0,
                take: 100,
                orderBy: "serialNo|asc",
                include: "category",
                search_column: "mainTitle",
                search: debouncedSearchQuery,
            };
            const res = await getAllBlogs(queryParams, idToken);
            if (res) {
                setAllBlogs(res.list);
                setTotal(res.total);
            }
        } catch (error) {
            console.error("Error retrieving blogs!");
        }
    };

    const reorderBlog = async (blogId: string, newPosition: number) => {
        try {
            const idToken: any = localStorage.getItem("idToken");
            const payload: blogBody = {
                blogId: blogId,
                newPosition: newPosition,
            };
            setReordering(true); // Start loading state
            const res = await reorderBlogs(payload, idToken);
            if (res) {
                await fetchAllBlogsAfterReorder();
            }
        } catch (error) {
            message.error("Error in reordering the blogs!");
        } finally {
            setReordering(false); // End loading state
        }
    };

    // const [blogs, setBlogs] = useState(allBlogs || []);
    const [draggingIndex, setDraggingIndex] = useState<number | null>(null);
    const [reordering, setReordering] = useState<boolean>(false);
    const containerRef = useRef<HTMLDivElement>(null);

    const onDragStart = (index: number) => {
        if (reordering) return;
        setDraggingIndex(index);
    };

    const onDragOver = (e: React.DragEvent<HTMLDivElement>, index: number) => {
        e.preventDefault();
        if (reordering || draggingIndex === null) return;
        const container = containerRef.current;
        if (container) {
            const { top, bottom, height } = container.getBoundingClientRect();
            const cursorY = e.clientY;

            const scrollSpeed = 10; // Speed of scrolling
            const threshold = 50; // Pixels from the top or bottom to trigger scrolling

            if (cursorY - top < threshold) {
                // Scroll up
                container.scrollBy({ top: -scrollSpeed, behavior: "smooth" });
            } else if (bottom - cursorY < threshold) {
                // Scroll down
                container.scrollBy({ top: scrollSpeed, behavior: "smooth" });
            }
        }

        const reorderedBlogs = [...allBlogs];
        const draggedBlog = reorderedBlogs[draggingIndex];
        reorderedBlogs.splice(draggingIndex, 1);
        reorderedBlogs.splice(index, 0, draggedBlog);

        setDraggingIndex(index);
        setAllBlogs(reorderedBlogs);
    };

    const onDrop = async () => {
        if (reordering || draggingIndex === null) return;

        const reorderedBlogs = [...allBlogs];
        setAllBlogs(reorderedBlogs);

        const blogId = allBlogs[draggingIndex].id;

        if (allBlogs[draggingIndex].serialNo === draggingIndex + 1) {
            setDraggingIndex(null);
            return;
        }

        try {
            await reorderBlog(blogId, draggingIndex + 1); // Call the reorderBlog API
        } catch (error) {
            message.error("Reordering failed!");
        } finally {
            setDraggingIndex(null);
        }
    };

    const [isDeleteting, setIsDeleteing] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] =
        useState<boolean>(false);
    const [deleteBlogId, setDeleteBlogId] = useState("");

    const deteleBlogById = async (blogId: any) => {
        try {
            setIsDeleteing(true);
            const idToken: any = localStorage.getItem("idToken");
            const queryParams: blogQueryParams = {
                blogId: blogId,
            };
            const res = await deleteBlog(blogId, queryParams, idToken);
            if (res) {
                setIsDeleteModalVisible(false);
                setIsDeleteing(false);
                setDeleteBlogId("");
                fetchAllBlogs();
            }
        } catch (error) {
            setIsDeleteing(false);
            message.error("Error in deleting the blog!");
        }
    };

    const updateStatusOfBlog = async (blogId: string, index: any) => {
        try {
            const idToken: any = localStorage.getItem("idToken");
            const newStatus =
                allBlogs?.[index]?.status === "ENABLED"
                    ? "DISABLED"
                    : "ENABLED";
            const queryParams: blogBody = {
                blogId: blogId,
                status: newStatus,
            };
            const res = await updateBlogStatus(blogId, queryParams, idToken);
            if (res && allBlogs) {
                setAllBlogs((allBlogs: any) =>
                    allBlogs.map((blog: any, i: any) =>
                        i === index ? { ...blog, status: newStatus } : blog
                    )
                );
            }
        } catch (error: any) {
            if (error.message.includes("Kategorie ist deaktiviert")) {
                message.error(
                    t(
                        "Blog category is disabled. Please enable the category to update the status!"
                    )
                );
            } else {
                message.error("Error updating blog status!");
            }
        }
    };

    const handleSearch = useCallback(
        debounce((query: any) => {
            setDebouncedSearchQuery(query); // update debounced query when user stops typing
        }, 500),
        []
    );

    const handleInputChange = (e: any) => {
        setSearchQuery(e.target.value);
        handleSearch(e.target.value); // call debounced function
    };

    return (
        <div className="flex flex-col">
            {!selectedAnyBlog && (
                <>
                    {!isAddEditBlogOpen && !isAddEditCategory && (
                        <>
                            <Navbar />
                            <div
                                className={`${roboto.className} w-full bg-white text-[24px] text-black items-center mt-4`}
                            >
                                <div className="flex items-center justify-between px-4">
                                    <div className="flex items-center font-[600] text-[23px]">
                                        {t("Blog")}
                                        <span className="text-gray-400 font-semibold text-[18px] font-roboto ml-1 mt-[2px]">
                                            ({total})
                                        </span>
                                    </div>
                                    <div className="text-[25px] text-black flex items-center space-x-3">
                                        <div className="text-[16px] font-normal text-black flex items-center px-4 relative">
                                            <input
                                                type="text"
                                                placeholder={t("search")}
                                                className="bg-[#D9D9D9] bg-opacity-[20%] pl-10 w-[350px] py-2 rounded-lg"
                                                value={searchQuery}
                                                onChange={handleInputChange} // call the input change handler
                                            />
                                            {/* <SearchOutlined className="absolute left-7 top-1/2 transform -translate-y-1/2"/> */}
                                            <img
                                                src="/images/search_1.svg"
                                                alt="toggle dropdown"
                                                width={20}
                                                height={20}
                                                className="absolute left-7 top-1/2 transform -translate-y-1/2"
                                            />
                                        </div>
                                        <Button
                                            type="primary"
                                            size="large"
                                            onClick={() => {
                                                setIsAddEditBlogOpen(true);
                                                setMode("add");
                                                setRecord(null);
                                            }}
                                            style={{
                                                backgroundColor: "#67A1A3",
                                            }}
                                            className={`${roboto.className} w-[150px] h-[40px] font-[500] bg-[#67A1A3] text-white shadow-inner`}
                                            loading={isButtonLoading}
                                        >
                                            {t("Add Blog")}
                                        </Button>
                                        <Button
                                            type="primary"
                                            size="large"
                                            onClick={() => {
                                                setIsAddEditCategory(true);
                                            }}
                                            style={{
                                                backgroundColor: "#67A1A3",
                                            }}
                                            className={`${roboto.className} w-[150px] h-[40px] font-[500] bg-[#67A1A3] text-white shadow-inner`}
                                            loading={isButtonLoading}
                                        >
                                            {t("Categories")}
                                        </Button>
                                    </div>
                                </div>
                                {isLoading ? (
                                    <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                                        <Spin size="default" />
                                    </div>
                                ) : (
                                    <>
                                        {allBlogs?.length > 0 ? (
                                            <div
                                                className={`h-[calc(100vh-142px)] scrollbar overflow-auto grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 relative mt-5 ${roboto.className}`}
                                            >
                                                <div
                                                    ref={containerRef}
                                                    id="scrollableDiv"
                                                    className={`w-full h-[calc(100vh-142px)] scrollbar overflow-auto col-span-1 sm:col-span-2 md:col-span-3`}
                                                >
                                                    <div
                                                        className={`h-auto grid gap-6 sm:grid-cols-2 md:grid-cols-3 pb-4 px-4 overflow-auto scrollbar`}
                                                    >
                                                        {allBlogs?.map(
                                                            (
                                                                blog: any,
                                                                index: any
                                                            ) => (
                                                                <div
                                                                    key={
                                                                        blog.id ||
                                                                        index
                                                                    }
                                                                    className="h-auto border rounded-[10px] border-[#D3E3E3] shadow-md shadow-[#67A1A314] bg-white cursor-pointer"
                                                                    onClick={() => {
                                                                        setIsAddEditBlogOpen(
                                                                            true
                                                                        );
                                                                        setMode(
                                                                            "edit"
                                                                        );
                                                                        setRecord(
                                                                            blog
                                                                        );
                                                                    }}
                                                                    draggable={
                                                                        !reordering
                                                                    }
                                                                    onDragStart={() =>
                                                                        onDragStart(
                                                                            index
                                                                        )
                                                                    }
                                                                    onDragOver={(
                                                                        e
                                                                    ) =>
                                                                        onDragOver(
                                                                            e,
                                                                            index
                                                                        )
                                                                    }
                                                                    onDrop={
                                                                        onDrop
                                                                    }
                                                                >
                                                                    <div className="p-2 flex justify-between items-start">
                                                                        <h1 className="w-[100%] text-[18px] font-[500] break-words">
                                                                            {
                                                                                blog.mainTitle
                                                                            }
                                                                        </h1>
                                                                    </div>
                                                                    <div className="h-[238px] bg-[#D3E3E3] flex justify-center items-center relative">
                                                                        {blog.image !==
                                                                        null ? (
                                                                            <img
                                                                                src={
                                                                                    blog.image
                                                                                }
                                                                                alt="blog"
                                                                                className="w-full h-full object-cover"
                                                                            />
                                                                        ) : (
                                                                            <Image
                                                                                src="/images/Logo_2.svg"
                                                                                width={
                                                                                    80
                                                                                }
                                                                                height={
                                                                                    80
                                                                                }
                                                                                alt="logo"
                                                                                className="w-[80px] h-[80px]"
                                                                            />
                                                                        )}
                                                                    </div>
                                                                    <div
                                                                        className={`${roboto.className} px-2 pt-2 flex text-[14px] justify-between`}
                                                                    >
                                                                        <p className="bg-[#D3E3E3] rounded-lg px-2 py-[5px]">
                                                                            {
                                                                                blog
                                                                                    ?.category
                                                                                    ?.category
                                                                            }
                                                                        </p>
                                                                        <div className="gap-2 flex">
                                                                            <p
                                                                                className="hover:bg-[#D3E3E3] bg-white border-[#D3E3E3] border-2 rounded-lg px-2 py-1"
                                                                                onClick={(
                                                                                    e
                                                                                ) => {
                                                                                    e.stopPropagation();
                                                                                    setSelectedAnyBlog(
                                                                                        true
                                                                                    );
                                                                                    setRecord(
                                                                                        blog
                                                                                    );
                                                                                }}
                                                                            >
                                                                                Preview
                                                                            </p>
                                                                            <Image
                                                                                src="/images/trash.svg"
                                                                                alt="visibility"
                                                                                width={
                                                                                    23
                                                                                }
                                                                                height={
                                                                                    23
                                                                                }
                                                                                className="cursor-pointer mr-[7px] mb-1"
                                                                                onClick={(
                                                                                    e
                                                                                ) => {
                                                                                    e.stopPropagation();
                                                                                    setDeleteBlogId(
                                                                                        blog?.id
                                                                                    );
                                                                                    setIsDeleteModalVisible(
                                                                                        true
                                                                                    );
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                    <div className="p-2 flex justify-between items-start">
                                                                        <div className="w-[80%] break-words">
                                                                            <p className="text-[14px] text-[#000000] font-[400]">
                                                                                {
                                                                                    blog.subTitle
                                                                                }
                                                                                {blog.shortDescription !==
                                                                                    null &&
                                                                                    ": "}
                                                                                {blog.shortDescription !==
                                                                                    null && (
                                                                                    <span className="text-[#8E9292] text-[14px] font-[400]">
                                                                                        {blog
                                                                                            .shortDescription
                                                                                            .length >
                                                                                        35
                                                                                            ? blog.shortDescription.substring(
                                                                                                  0,
                                                                                                  35
                                                                                              ) +
                                                                                              "..."
                                                                                            : blog.shortDescription}
                                                                                    </span>
                                                                                )}
                                                                            </p>
                                                                        </div>
                                                                        <div className="flex justify-start items-center w-[20%] break-words">
                                                                            <Switch
                                                                                className="no-pointer-cursor"
                                                                                onClick={(
                                                                                    e
                                                                                ) =>
                                                                                    e.stopPropagation()
                                                                                }
                                                                                onChange={() =>
                                                                                    updateStatusOfBlog(
                                                                                        blog.id,
                                                                                        index
                                                                                    )
                                                                                }
                                                                                checked={
                                                                                    blog.status.toLowerCase() ===
                                                                                    "enabled"
                                                                                }
                                                                                color="primary"
                                                                                sx={{
                                                                                    "& .MuiSwitch-switchBase":
                                                                                        {
                                                                                            color: "#CBCBCB",
                                                                                        },
                                                                                    "& .MuiSwitch-switchBase.Mui-checked":
                                                                                        {
                                                                                            color: "#67A1A3",
                                                                                        },
                                                                                    "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track":
                                                                                        {
                                                                                            backgroundColor:
                                                                                                "#67A1A3",
                                                                                        },
                                                                                    "& .MuiSwitch-track":
                                                                                        {
                                                                                            backgroundColor:
                                                                                                "rgba(211, 217, 231, 0.80)",
                                                                                        },
                                                                                }}
                                                                                size="medium"
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            )
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        ) : (
                                            <div
                                                className={`h-[calc(100vh-148px)] w-full flex items-center justify-center font-roboto font-[500] mt-5 ${roboto.className}`}
                                            >
                                                <Empty
                                                    description={t(
                                                        "No data available"
                                                    )}
                                                    image={
                                                        Empty.PRESENTED_IMAGE_SIMPLE
                                                    }
                                                />
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </>
                    )}
                </>
            )}
            {isAddEditBlogOpen && (
                <AddEditBlogModal
                    isAddEditBlogOpen={isAddEditBlogOpen}
                    setIsAddEditBlogOpen={setIsAddEditBlogOpen}
                    fetchedCategories={fetchedCategories}
                    record={record}
                    setRecord={setRecord}
                    mode={mode}
                />
            )}
            {isAddEditCategory && (
                <AddEditCategory
                    isAddEditCategory={isAddEditCategory}
                    setIsAddEditCategory={setIsAddEditCategory}
                />
            )}
            {selectedAnyBlog && (
                <SelectedBlog
                    selectedAnyBlog={selectedAnyBlog}
                    setSelectedAnyBlog={setSelectedAnyBlog}
                    record={record}
                />
            )}
            <Modal
                open={isDeleteModalVisible}
                onCancel={() => setIsDeleteModalVisible(false)}
                footer={null}
                centered
                width={300}
                style={{ textAlign: "center" }}
                closable={false}
            >
                <Image
                    src="/images/modalDelete.svg"
                    alt="Delete"
                    width={100}
                    height={100}
                    className="mx-auto bg-white -mt-3"
                />

                <div className=" flex justify-center items-center w-full">
                    <h2 className="text-[16px] font-roboto font-[400] mb-8 w-[80%] flex justify-center items-center">
                        {t("Are you sure you want to delete this blog?")}
                    </h2>
                </div>
                <div className="flex justify-center space-x-4">
                    <Button
                        onClick={() => {
                            setIsDeleteModalVisible(false);
                            setDeleteBlogId("");
                        }}
                        className="bg-transparent cancelbutton font-roboto text-[#F1676D] text-[18px] h-[46px] border-[1px] border-[#F1676D] w-[116px] font-medium rounded-xl"
                    >
                        {t("cancel")}
                    </Button>
                    <Button
                        type="primary"
                        className="bg-[#F1676D] shadow-inner deleteButton font-roboto text-white text-[18px] w-[116px] h-[46px] font-medium rounded-xl"
                        onClick={() => {
                            deteleBlogById(deleteBlogId);
                        }}
                        loading={isDeleteting}
                    >
                        {t("delete")}
                    </Button>
                </div>
            </Modal>
        </div>
    );
};

export default Blog;

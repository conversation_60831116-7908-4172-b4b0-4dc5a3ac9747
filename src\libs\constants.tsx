export const Grade3rdLevel = [
    { value: "LEVEL_1", label: "Level 1" },
    { value: "LEVEL_2", label: "Level 2" },
    { value: "LEVEL_3", label: "Level 3" },
    { value: "LEVEL_4", label: "Level 4" },
    { value: "LEVEL_5", label: "Level 5" },
    { value: "LEVEL_6", label: "Level 6" },
];

export const Grade4thLevel = [
    { value: "LEVEL_1", label: "Level 1" },
    { value: "LEVEL_2", label: "Level 2" },
    { value: "LEVEL_3", label: "Level 3" },
    { value: "LEVEL_4", label: "Level 4" },
    { value: "LEVEL_5", label: "Level 5" },
    { value: "LEVEL_6", label: "Level 6" },
    { value: "LEVEL_7", label: "Level 7" },
    { value: "LEVEL_8", label: "Level 8" },
    { value: "LEVEL_9", label: "Level 9" },
];

export const grades = [
    { value: "FIRST", label: "1. Grade - GYM" },
    { value: "SECOND", label: "2. Grade - GYM" },
    { value: "THIRD", label: "3.Grade - VS" },
    { value: "FOURTH", label: "4. Grade - VS " },
];

export const thirdGradeSubjects = [
    { value: "GERMAN", label: "German" },
    { value: "MATHS", label: "Maths" },
];

export const forthGradeSubjects = [
    { value: "GERMAN", label: "German" },
    { value: "MATHS", label: "Maths" },
    { value: "ENGLISH", label: "English" },
];

export const practiceTypes = [
    { value: "NORMAL", label: "Regular" },
    { value: "WARM_UP", label: "Warm Up" },
];

export const questionTypes = [
    { value: "MCQ", label: "MCQ" },
    { value: "INPUT_BOX", label: "Inputbox" },
    { value: "KEY_RULE", label: "Key Rule" },
];

export const englishCategories = [
    { value: "GRAMMAR", label: "Grammar" },
    { value: "VOCABULARY", label: "Vocabulary" },
    { value: "TALKING", label: "Talking" },
    { value: "WRITING", label: "Writing" },
];

export const mathsCategories = [
    { value: "ARITHMETIC", label: "Arithmetic" },
    { value: "GEOMETRY", label: "Geometric" },
    { value: "MEASURES", label: "Measures" },
    { value: "FACTUAL_PROBLEMS", label: "Factual Problems" },
];

export const germanCategories = [
    { value: "ORTHOGRAPHY", label: "Orthography" },
    { value: "GRAMMAR", label: "Grammar" },
    { value: "WRITING", label: "Writing" },
    { value: "READING", label: "Reading" },
];

export const gradeFirstEnglishCategories = [
    { value: "VOCABULARY", label: "Vocabulary" },
    { value: "GRAMMAR", label: "Grammar" },
    { value: "READING", label: "Reading" },
    { value: "LISTENING", label: "Listening" },
    { value: "WRITING", label: "Writing" },
];

export const gradeFirstMathsCategories = [
    { value: "NUMBERS_DIMENSIONS", label: "Numbers & Dimensions" },
    { value: "VARIABLES_FUNCTIONS", label: "Variables & Functions" },
    { value: "GEOMETRY", label: "Geometry" },
    { value: "STATISTICS", label: "Statistics" },
];

export const gradeFirstGermanCategories = [
    { value: "ORTHOGRAPHY", label: "Orthography" },
    { value: "GRAMMAR", label: "Grammar" },
    { value: "READING", label: "Reading" },
    { value: "WRITING", label: "Writing" },
];

export const answerTypes = [
    { value: "TEXT", label: "Text" },
    { value: "IMAGE", label: "Image" },
];

export const userType = [
    { value: "PARENT", label: "Parents" },
    { value: "CHILD", label: "Child" },
];

export const videoTutorialType = [
    { value: "PARENT", label: "Parents" },
    { value: "CHILD", label: "Child" },
    // { value: "WORK_BOOK", label: "Workbook" },
    { value: "WORK_BOOK_CHILD", label: "Child Workbook" },
    { value: "WORK_BOOK_PARENT", label: "Parent Workbook" },
];

export const schoolType = [
    { value: "privat", label: "Privat" },
    { value: "öffentlich", label: "Öffentlich" },
];

export const broadcastType = [
    { value: "ALL", label: "All" },
    { value: "PARENT", label: "Parent" },
    { value: "CHILD", label: "Child" },
];

export const schoolSpecifications = [
    { value: "SECONDARY_SCHOOL", label: "Secondary School" },
    {
        value: "LATIN_OR_2ND_FOREIGN_LANGUAGE",
        label: "Latin or 2nd foreign language ",
    },
    { value: "ECONOMICS_GRAMMAR_SCHOOL", label: "Economics grammar school" },
    { value: "SPORTS_SPECIALISATION", label: "Sports specialisation" },
    { value: "MUSIC_SPECIALISATION", label: "Music specialisation" },
];

export const schoolSupervision = [
    { value: true, label: "Yes" },
    { value: false, label: "No" },
];

export const schoolState = [
    { value: "Berlin", label: "Berlin" },
    { value: "Bayern", label: "Bayern" },
    { value: "Niedersachsen ", label: "Niedersachsen" },
    { value: "Baden-Württemberg", label: "Baden-Württemberg" },
    { value: "Brandenburg ", label: "Brandenburg" },
    { value: "Hamburg", label: "Hamburg" },
    { value: "Bremen", label: "Bremen" },
];

export const schoolBoardingType = [
    { value: true, label: "Yes" },
    { value: false, label: "No" },
];

export const isPromotionSponser = [
    { value: true, label: "Yes" },
    { value: false, label: "No" },
];

export const promotionType = [
    { value: "PRODUCT", label: "Product" },
    { value: "SERVICES", label: "Services" },
];

export const promotionCurrency = [
    { value: "$", label: "$ (Dollar)" },
    { value: "€", label: "€ (Euro)" },
];

export const uploadDocumentTabs = [
    { value: "Introduction PDF", label: "Introduction PDF" },
    // { value: "Purchase workbook", label: "Purchase workbook" },
];

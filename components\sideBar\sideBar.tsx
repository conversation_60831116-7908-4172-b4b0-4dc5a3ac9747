"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter, usePathname } from "next/navigation";
import { <PERSON><PERSON> } from "next/font/google";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const SideBarComponents = () => {
    const [selectedButton, setSelectedButton] = useState("users");
    const [userType, setUserType] = useState("ADMIN");
    const router = useRouter();
    const path = usePathname();
    const { t } = useTranslation();

    useEffect(() => {
        const session = localStorage.getItem("session");
        const userType: any = localStorage.getItem("userType");
        const laguage: any = localStorage.getItem("gymiAdminLanguage");
        setUserType(userType);
        handleChangeLanguage(laguage);
        if (session) {
            const page = path.split("/")[1];

            if (page && page !== "" && page !== "users") {
                setSelectedButton(page);
            }
        } else {
            router.push("/");
        }
    }, []);

    const handleButtonClick = (button: any) => {
        router.prefetch(`/${button}`);
        setSelectedButton(button);
    };

    return (
        <div
            className={`w-[260px] min-w-[260px] h-[100vh] bg-gradient-to-t from-[#67A1A3E5] to-[#67A1A3CC] overflow-y-auto scrollbar-hide flex flex-col font-[500] ${roboto.className}`}
        >
            <div className="w-full flex gap-2 justify-center items-center pt-[30px] pr-4">
                <Image
                    src="/images/Logo_2.svg"
                    alt="login"
                    width={40}
                    height={40}
                    onClick={() =>
                        router.push(
                            `${
                                userType === "TEACHER"
                                    ? "/knowledgeRally"
                                    : "/users"
                            } `
                        )
                    }
                    className="cursor-pointer"
                />
                <h1
                    className={`text-white text-[24px] leading-[24px] font-[600] cursor-pointer ${roboto.className}`}
                    onClick={() =>
                        router.push(
                            `${
                                userType === "TEACHER"
                                    ? "/knowledgeRally"
                                    : "/users"
                            } `
                        )
                    }
                >
                    {t("gymi")}
                </h1>
            </div>

            <div className="flex flex-col justify-center items-center">
                <div
                    className={`flex flex-col ${
                        userType === "TEACHER"
                            ? "justify-start"
                            : "justify-center"
                    } items-center w-full h-[calc(100vh-130px)] my-[30px]`}
                >
                    <div className="flex flex-col items-center w-full space-y-1 sidebar-text overflow-y-scroll scrollbar h-full">
                        {userType === "ADMIN" && (
                            <button
                                className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                    selectedButton === "users"
                                        ? "bg-white"
                                        : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                }`}
                                onClick={() => {
                                    handleButtonClick("users");
                                    router.push("/users");
                                }}
                            >
                                <Image
                                    src={
                                        selectedButton === "users"
                                            ? "/images/sideBarM/user1.svg"
                                            : "/images/sideBarM/user.svg"
                                    }
                                    alt="users"
                                    width={20}
                                    height={20}
                                    className="mr-[10px]"
                                />
                                <span
                                    className={`text-[13px] flex items-center sidebar-text ${
                                        selectedButton === "users"
                                            ? "text-[#67A1A3]"
                                            : "text-white"
                                    } `}
                                >
                                    {t("users")}
                                </span>
                            </button>
                        )}
                        {(userType === "ADMIN" || userType === "TEACHER") && (
                            <button
                                className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                    selectedButton === "knowledgeRally"
                                        ? "bg-white"
                                        : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                } ${
                                    userType === "TEACHER" ? "mt-[16%]" : "mt-0"
                                }`}
                                onClick={() => {
                                    handleButtonClick("knowledgeRally");
                                    router.push("/knowledgeRally");
                                }}
                            >
                                <Image
                                    src={
                                        selectedButton === "knowledgeRally"
                                            ? "/images/sideBarM/owal1.svg"
                                            : "/images/sideBarM/owal.svg"
                                    }
                                    alt="owl"
                                    width={20}
                                    height={20}
                                    className="mr-[10px]"
                                />
                                <span
                                    className={` text-[13px] flex items-center ${
                                        selectedButton === "knowledgeRally"
                                            ? "text-[#67A1A3]"
                                            : "text-white"
                                    }`}
                                >
                                    {t("knowledge_rally")}
                                </span>
                            </button>
                        )}

                        {userType === "ADMIN" && (
                            <>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "mails"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    }`}
                                    onClick={() => {
                                        handleButtonClick("mails");
                                        router.push("/mails");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton === "mails"
                                                ? "/images/sideBarM/sms1.svg"
                                                : "/images/sideBarM/sms.svg"
                                        }
                                        alt="mails"
                                        width={20}
                                        height={2}
                                        className="mr-[10px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton === "mails"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        }`}
                                    >
                                        Mails
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "highSchool"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    }`}
                                    onClick={() => {
                                        handleButtonClick("highSchool");
                                        router.push("/highSchool");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton === "highSchool"
                                                ? "/images/sideBarM/school1.svg"
                                                : "/images/sideBarM/school.svg"
                                        }
                                        alt="school"
                                        width={20}
                                        height={20}
                                        className="mr-[10px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton === "highSchool"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        } `}
                                    >
                                        {t("high_school")}
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "promotion"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    } `}
                                    onClick={() => {
                                        handleButtonClick("promotion");
                                        router.push("/promotion");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton === "promotion"
                                                ? "/images/sideBarM/promotions1.svg"
                                                : "/images/sideBarM/promotions.svg"
                                        }
                                        alt="promotion"
                                        width={20}
                                        height={20}
                                        className="mr-[10px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton === "promotion"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        }`}
                                    >
                                        {t("promotions")}
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "trainingMaterial"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    }`}
                                    onClick={() => {
                                        handleButtonClick("trainingMaterial");
                                        router.push("/trainingMaterial");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton ===
                                            "trainingMaterial"
                                                ? "/images/sideBarM/document-copy1.svg"
                                                : "/images/sideBarM/document-copy.svg"
                                        }
                                        alt="trainig_mate"
                                        width={20}
                                        height={20}
                                        className="mr-[10px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton ===
                                            "trainingMaterial"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        }`}
                                    >
                                        {t("training_material")}
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "blog"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    }`}
                                    onClick={() => {
                                        handleButtonClick("blog");
                                        router.push("/blog");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton === "blog"
                                                ? "/images/sideBarM/blogc.svg"
                                                : "/images/sideBarM/blog.svg"
                                        }
                                        alt="subscribedEmail"
                                        width={22}
                                        height={22}
                                        className="mr-[8px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton === "blog"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        } `}
                                    >
                                        {t("Blog")}
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "training-course"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    }`}
                                    onClick={() => {
                                        handleButtonClick("training-course");
                                        router.push("/training-course");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton === "training-course"
                                                ? "/images/sideBarM/training.svg"
                                                : "/images/sideBarM/trainingOne.svg"
                                        }
                                        alt="subscribedEmail"
                                        width={22}
                                        height={22}
                                        className="mr-[8px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton === "training-course"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        } `}
                                    >
                                        {t("Training Course")}
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "manage-grade"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    } `}
                                    onClick={() => {
                                        handleButtonClick("manage-grade");
                                        router.push("/manage-grade");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton === "manage-grade"
                                                ? "/images/sideBarM/manage-grade.svg"
                                                : "/images/sideBarM/manage-grade1.svg"
                                        }
                                        alt="promotion"
                                        width={20}
                                        height={20}
                                        className="mr-[10px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton === "manage-grade"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        }`}
                                    >
                                        {t("Manage Grade")}
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "upload-document"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    } `}
                                    onClick={() => {
                                        handleButtonClick("upload-document");
                                        router.push("/upload-document");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton === "upload-document"
                                                ? "/images/sideBarM/upload-document.svg"
                                                : "/images/sideBarM/upload-document1.svg"
                                        }
                                        alt="upload-document"
                                        width={20}
                                        height={20}
                                        className="mr-[10px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton === "upload-document"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        }`}
                                    >
                                        {t("Upload Document")}
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "videoTutorial"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    }`}
                                    onClick={() => {
                                        handleButtonClick("videoTutorial");
                                        router.push("/videoTutorial");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton === "videoTutorial"
                                                ? "/images/sideBarM/video-play1.svg"
                                                : "/images/sideBarM/video-play.svg"
                                        }
                                        alt="videoTutorial"
                                        width={20}
                                        height={20}
                                        className="mr-[10px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton === "videoTutorial"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        }`}
                                    >
                                        {t("video_tutorial")}
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "broadcast-message"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    }`}
                                    onClick={() => {
                                        handleButtonClick("broadcast-message");
                                        router.push("/broadcast-message");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton ===
                                            "broadcast-message"
                                                ? "/images/sideBarM/broadcastc.svg"
                                                : "/images/sideBarM/broadcast.svg"
                                        }
                                        alt="broadcast-message"
                                        width={22}
                                        height={22}
                                        className="mr-[8px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton ===
                                            "broadcast-message"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        } `}
                                    >
                                        {t("Broadcast messages")}
                                    </span>
                                </button>
                                <button
                                    className={`flex items-center px-[14px] py-[10px] w-[80%] rounded-lg ${
                                        selectedButton === "subscribedEmail"
                                            ? "bg-white"
                                            : "bg-transparent hover:bg-white hover:bg-opacity-10"
                                    }`}
                                    onClick={() => {
                                        handleButtonClick("subscribedEmail");
                                        router.push("/subscribedEmail");
                                    }}
                                >
                                    <Image
                                        src={
                                            selectedButton === "subscribedEmail"
                                                ? "/images/sideBarM/subscribedEmail.svg"
                                                : "/images/sideBarM/subscribedEmail1.svg"
                                        }
                                        alt="subscribedEmail"
                                        width={22}
                                        height={22}
                                        className="mr-[8px]"
                                    />
                                    <span
                                        className={` text-[13px] flex items-center ${
                                            selectedButton === "subscribedEmail"
                                                ? "text-[#67A1A3]"
                                                : "text-white"
                                        } `}
                                    >
                                        {t("Subscribed Emails")}
                                    </span>
                                </button>
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SideBarComponents;

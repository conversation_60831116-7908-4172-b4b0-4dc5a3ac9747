import { fetch } from "@/src/libs/helpers";

export interface addSchoolQueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    search_column?: string;
}

export interface picture {
    value?: string;
}

export interface highSchoolDetail {
    picture?: picture[] | any;
    name?: string;
    postCode?: string | null;
    email?: string | null;
    countryCode?: string | null;
    contact?: string | null;
    location?: string;
    state?: string | null;
    schoolType?: string | null;
    locationUrl?: string | null;
    specialization?: string | null;
    afternoonCare?: string | null;
    boardingSchool?: boolean | null;
    description?: string;
    website?: string;
}

export interface highSchoolFile {
    file?: any;
}

export const getAllSchool = async (
    queryParams: addSchoolQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/School",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const addNewSchool = async (
    payload: highSchoolDetail,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/School",
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getSchoolById = async (
    schoolId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/School/${schoolId}`,
        method: "GET",
        headers: {
            Authorization: authorization,
        },
    });
};

export const updateSchoolById = async (
    schoolId: string,
    payload: highSchoolDetail,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/School/${schoolId}`,
        method: "PUT",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deleteSchoolById = async (
    schoolId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/School/${schoolId}`,
        method: "DELETE",
        headers: {
            Authorization: authorization,
        },
    });
};

export const importExcelFileForHighSchool = async (file: any): Promise<any> => {
    const formData = new FormData();
    formData.append("file", file);
    return fetch({
        url: "/email-csv/upload",
        method: "POST",
        data: formData, // Use 'body' with FormData
        headers: {
            "Content-Type": "multipart/form-data",
        },
    });
};

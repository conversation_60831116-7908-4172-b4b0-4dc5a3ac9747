@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 255, 255, 255;
    --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
    :root {
        --foreground-rgb: 255, 255, 255;
        --background-start-rgb: 255, 255, 255;
        --background-end-rgb: 255, 255, 255;
    }
}

body {
    color: rgb(var(--foreground-rgb));
    background: linear-gradient(
            to bottom,
            transparent,
            rgb(var(--background-end-rgb))
        )
        rgb(var(--background-start-rgb));
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.custom-table .ant-table {
    width: 100%;
}

.custom-table .ant-table-thead > tr > th {
    background: none;
    color: #67a1a3;
    border-bottom: none;
    font-weight: 500;
    font-size: 14px;
}

.custom-table .ant-table-tbody > tr > td {
    border-bottom: none;
}

.custom-table th.ant-table-cell::before {
    content: none;
    display: none;
}
.custom-table th.ant-table-cell,
.custom-table td.ant-table-cell {
    padding: 8px !important;
}

@font-face {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2)
        format("woff2");
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
        U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122,
        U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

.button:hover {
    background-color: #67a1a3 !important;
    color: white !important;
}

.deleteButton:hover {
    background-color: #f1676d !important;
    color: white !important;
}

.cancelbutton:hover {
    background-color: #f1676d !important;
    border: #f1676d !important;
    color: white !important;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 30px 60px -12px inset,
        rgba(0, 0, 0, 0.3) 0px 18px 36px -18px inset;
}

.ant-select-selector {
    border: none;
}

.shadow-inner {
    box-shadow: rgba(50, 50, 93, 0.25) 0px 30px 60px -12px inset,
        rgba(0, 0, 0, 0.3) 0px 18px 36px -18px inset;
}

.shadow-allSide {
    box-shadow: rgba(14, 30, 37, 0.12) 0px 2px 4px 0px,
        rgba(14, 30, 37, 0.32) 0px 2px 16px 0px;
}

.custom-pagination {
    padding-right: 50px;
    position: fixed;
    bottom: 0;
    right: 0;
    margin: 10px;
}

.custom-pagination .ant-select-selector {
    display: none;
}

.custom-pagination .ant-pagination-options {
    display: none;
}

/* Custom Pagination Styles */
.custom-pagination .ant-pagination-item-active {
    background-color: #d9d9d9;
    border-color: #d9d9d9;
}

.custom-pagination .ant-pagination-item-active a {
    color: #3aa0cc;
}

.custom-select .ant-select-selector {
    border-color: #d3e3e3 !important;
    font-family: "Roboto", sans-serif !important;
    font: bold !important;
    background-color: #f6faf9 !important;
}

@keyframes pulse {
    0%,
    100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.5);
    }
}

.spinner {
    animation: pulse 1.5s infinite;
}

.custom-caret::placeholder {
    color: transparent; /* This is a workaround for applying the custom caret */
}

.custom-caret {
    caret-color: black; /* Set the color of the caret */
}

.custom-caret:focus {
    outline: none;
    border-left: 5px solid transparent; /* Adds space for the wider caret */
    border-right: 5px solid transparent; /* Adds space for the wider caret */
    background-position: left bottom; /* Adjust to match the caret position */
    background-size: 2px 1.2em; /* Adjust the width and height */
    background-repeat: no-repeat;
}

.custom-avatar-size {
    width: 42px;
    height: 42px;
    line-height: 50px; /* Ensure the icon is vertically centered */
    font-size: 24px; /* Adjust icon size if necessary */
}

.custom-radio .ant-radio-inner {
    border-color: #67a1a3;
}

.custom-radio .ant-radio-checked .ant-radio-inner {
    border-color: #67a1a3;
    background-color: #67a1a3;
}

.custom-radio .ant-radio-checked::after {
    border-color: #67a1a3;
}

.shadowed-element {
    box-shadow: rgba(0, 0, 0, 0.15) 0px 2px 15px;
}

.custom-table-row {
    cursor: pointer;
}

.align-start .ant-select-selection-placeholder,
.align-start .ant-select-selection-item {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Align items to the start */
}

.custom-selection .ant-select-selector {
    border-color: #a3cbc1 !important; /* Custom border color */
    background-color: #f6faf9 !important; /* Custom background color */
}

.custom-selection .ant-select-selection-placeholder,
.custom-selection .ant-select-selection-item {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Align items to the start */
}

/* Optional: Adjust the border color on focus */
.custom-selection .ant-select-selector:focus,
.custom-selection .ant-select-selector:active,
.custom-selection .ant-select-selector.ant-select-focused {
    border-color: #a3cbc1 !important;
}

.ant-picker-disabled {
    background-color: #f6faf9 !important;
    pointer-events: none;
}

.ant-picker-disabled .ant-picker-input {
    background-color: #f6faf9 !important;
}

.ant-picker-disabled .ant-picker-input input {
    color: black !important;
}

/* Scollbar */
.scrollbar::-webkit-scrollbar {
    width: 5px;
    height: 100%;
}

.scrollbar::-webkit-scrollbar-thumb {
    background-color: #e5e5e5;
    border-radius: 3px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #ababab;
}

.custom-hr {
    border: none;
    border-top: 1px solid #d3e3e3;
    margin-left: 0px;
    margin-right: 0px;
}

.custom-segmented .ant-segmented-item-label {
    width: 120px;
}

.ant-segmented-item,
.ant-segmented-thumb {
    border-radius: 9px !important;
}

.ant-segmented-item-selected,
.ant-segmented-thumb {
    background-color: #67a1a3 !important;
    color: white !important;
}

.ant-segmented-item-label {
    width: 100px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ant-segmented-item {
    margin-right: 4px;
}

.ant-segmented-item:last-child {
    margin-right: 0;
}

.ant-segmented-item-label {
    padding: 0 10px;
}

.custom-password-field .ant-input::placeholder {
    font-family: "Roboto", sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 14px;
    color: #8e9292;
}

.custom-table-scroll .ant-table-body {
    scrollbar-width: thin; /* Make the scrollbar thin */
    scrollbar-color: #8e9292 #f1f1f1; /* Thumb color and track color */
}

.custom-table-scroll-hide .ant-table-body {
    scrollbar-width: none; /* Make the scrollbar thin */
    scrollbar-color: #8e9292 #f1f1f1; /* Thumb color and track color */
}

.sidebar-text {
    font-size: 13px !important;
    text-align: left !important;
}

/* Side Bar scroll style */
.sidebar::-webkit-scrollbar {
    width: 3px;
    height: 100%;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: #e5e5e5;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background-color: #cccccc00;
}

/* Remove default borders from Quill */
.customQuill .ql-container {
    border: none !important; /* Remove default border */
    border-radius: 0.375rem !important;
    background-color: #f6faf9 !important;
    box-shadow: 0 0 0 1px #d3e3e3; /* Apply custom border as shadow */
}

.customQuill .ql-editor {
    border: none !important; /* Remove inner border */
    border-radius: 0.375rem !important;
    background-color: #f6faf9 !important;
}

/* Optional: Remove border on the toolbar if present */
.customQuill .ql-toolbar {
    border: none !important;
    border-radius: 0.375rem 0.375rem 0 0 !important;
    background-color: #f6faf9;
}

/* Scrollbar design */
.custom-scroll::-webkit-scrollbar,
.ant-table-body::-webkit-scrollbar {
    width: 5px !important;
    height: 100% !important;
}

.custom-scroll::-webkit-scrollbar-thumb,
.ant-table-body::-webkit-scrollbar-thumb {
    background-color: #e5e5e5 !important;
    border-radius: 3px !important;
}

.custom-scroll::-webkit-scrollbar-thumb:hover,
.ant-table-body::-webkit-scrollbar-thumb:hover {
    background-color: #ababab !important;
}

.ql-size-medium {
    font-size: 16px !important; /* Adjust if needed */
}

.custom-date-picker {
    --primary-color: #67a1a3 !important;
}

.custom-date-picker
    .ant-picker-cell-in-view.ant-picker-cell-selected
    .ant-picker-cell-inner {
    background-color: #67a1a3 !important;
}

.custom-date-picker
    .ant-picker-cell-in-view.ant-picker-cell-range-start
    .ant-picker-cell-inner,
.custom-date-picker
    .ant-picker-cell-in-view.ant-picker-cell-range-end
    .ant-picker-cell-inner {
    background-color: #67a1a3 !important;
}

.custom-date-picker .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
    background-color: #67a1a380 !important;
}

.custom-date-picker
    .ant-picker-cell-in-view.ant-picker-cell-today
    .ant-picker-cell-inner::before {
    border-color: #67a1a3 !important;
}

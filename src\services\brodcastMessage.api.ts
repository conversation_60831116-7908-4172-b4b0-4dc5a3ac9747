import { fetch } from "@/src/libs/helpers";

export interface brodcastMessageParam {
    serialNum?: number | null;
    fileName?: string;
    subject?: string;
    file?: string;
    materialId?: string;
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    search_column?: string;
    filter?: string;
}

export interface brodcastMessageBody {
        title?: string | null;
        body?: string;
        broadcastType?: string;
    }
    

export const getAllBrodcastMessage = async (
    queryParams: brodcastMessageParam,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/broadcast/get-all-broadcasts",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const toSendBrodcastMessage = async (
    payload: brodcastMessageBody,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/broadcast/notification",
        method: "POST",
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

"use client";
import React, { useState, useEffect } from "react";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import "@/app/globals.css";
import { message } from "antd";
import {
    uploadDocumentParams,
    getAllIntroductionPdf,
    getPurchaseWorkbook,
} from "@/src/services/upload-document.api";
import { Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { uploadDocumentTabs } from "@/src/libs/constants";
import IntriductionPdfTable from "./introduction-pdf-table";
import PurchaseWorkbookTable from "./purchase-workbook-tabe";
import AddIntoductionDocumentModal from "./add-intoduction-document-modal";
import AddPurcahseDocumentModal from "./add-purchase-document-modal";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface subscribedUser {
    email: string;
    id: string;
}

const UploadDocument = () => {
    const { t } = useTranslation();
    const [isIntroductionPdfTableLoading, setIsIntroductionPdfTableLoading] =
        useState(false);
    const [introductionpdftabledata, setIntroductionPdfTableData] = useState<
        subscribedUser[]
    >([]);
    const [isPurchaseWorkbookTableLoading, setIsPurchaseWorkbookTableLoading] =
        useState(false);
    const [purchaseWorkbookTabledata, setPurchaseWorkbookTableData] = useState<
        subscribedUser[]
    >([]);
    const [selectedTab, setSelectedTab] = useState(uploadDocumentTabs[0].value);
    const [isIntroModalVisible, setIsIntroModalVisible] = useState(false);
    const [isPurcahseModalVisible, setIsPurcahseModalVisible] = useState(false);
    const [pageSize, setPageSize] = useState<number>(20);
    const [total, setTotal] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState(1);
    const router = useRouter();

    useEffect(() => {
        fetchIntroductionPdfData();
        // fetchPurchaseWorkbookData();
    }, []);

    const fetchIntroductionPdfData = async (page: number = 1) => {
        const authorization = localStorage.getItem("idToken");
        try {
            setIsIntroductionPdfTableLoading(true);
            const params: uploadDocumentParams = {
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: "createdAt|desc",
                // include: "level",
            };
            const response = await getAllIntroductionPdf(params, authorization);

            if (response) {
                const updatedData = response.list.map(
                    (item: any, index: number) => ({
                        ...item,
                        sr_no: index + 1,
                    })
                );
                setIntroductionPdfTableData(updatedData);
            } else {
                console.error(t("Empty or invalid response"));
            }
        } catch (error) {
            console.error("Fetch error:", error);
            message.error(t("Data could not be retrieved"));
        } finally {
            setIsIntroductionPdfTableLoading(false);
        }
    };

    // const fetchPurchaseWorkbookData = async (page: number = 1) => {
    //     const authorization = localStorage.getItem("idToken");
    //     try {
    //         setIsPurchaseWorkbookTableLoading(true);
    //         const params: uploadDocumentParams = {
    //             skip: (page - 1) * pageSize,
    //             take: pageSize,
    //             orderBy: "createdAt|desc",
    //             include: "level",
    //         };
    //         const response = await getPurchaseWorkbook(params, authorization);

    //         if (response) {
    //             const updatedData = response.map(
    //                 (item: any, index: number) => ({
    //                     ...item,
    //                     sr_no: index + 1,
    //                 })
    //             );
    //             setPurchaseWorkbookTableData(updatedData);
    //         } else {
    //             console.error(t("Empty or invalid response"));
    //         }
    //     } catch (error) {
    //         console.error("Fetch error:", error);
    //         message.error(t("Data could not be retrieved"));
    //     } finally {
    //         setIsPurchaseWorkbookTableLoading(false);
    //     }
    // };

    const paginationConfig = {
        pageSize: pageSize,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`bg-white w-full text-[24px] text-black items-center px-4 font-[400] mt-4 ${roboto.className}`}
            >
                <div className="flex items-center">
                    <div className="flex items-center font-[600] text-[24px]">
                        {t("Upload Document ")}
                    </div>
                </div>
                <div className="mt-[12px] flex justify-between items-center w-full font-[400]">
                    <div className="space-x-3">
                        {uploadDocumentTabs.map((tab) => (
                            <button
                                key={tab.value}
                                onClick={() => setSelectedTab(tab.value)}
                                className={`rounded-[10px] w-auto h-[40px] text-[16px] px-4 cursor-default ${
                                    selectedTab === tab.value
                                        ? "bg-[#67A1A3] text-white"
                                        : "bg-[#D3E3E3] text-[#67A1A3]"
                                }`}
                            >
                                {tab.label}
                            </button>
                        ))}
                    </div>
                    {introductionpdftabledata.length < 6 && (
                        <button
                            className="rounded-[10px] w-auto h-[45px] min-w-[170px] text-[16px] px-4 bg-[#67A1A3] text-white flex justify-center items-center"
                            style={{
                                boxShadow: "0px 0px 12px 0px #00000040 inset",
                            }}
                            onClick={() => {
                                selectedTab === "Purchase workbook"
                                    ? setIsPurcahseModalVisible(true)
                                    : setIsIntroModalVisible(true);
                            }}
                        >
                            <span className="flex">{t("Add Document ")}</span>
                        </button>
                    )}
                </div>
                <div className="">
                    {selectedTab === "Introduction PDF" ? (
                        <IntriductionPdfTable
                            isLoading={isIntroductionPdfTableLoading}
                            data={introductionpdftabledata}
                            paginationConfig={paginationConfig}
                            fetchData={fetchIntroductionPdfData}
                        />
                    ) : (
                        <PurchaseWorkbookTable
                            isLoading={isPurchaseWorkbookTableLoading}
                            data={purchaseWorkbookTabledata}
                            // paginationConfig={paginationConfig}
                            // fetchData={fetchPurchaseWorkbookData}
                        />
                    )}
                </div>
            </div>
            <AddIntoductionDocumentModal
                isModalVisible={isIntroModalVisible}
                setIsModalVisible={setIsIntroModalVisible}
                fetchData={fetchIntroductionPdfData}
            />
            <AddPurcahseDocumentModal
                isModalVisible={isPurcahseModalVisible}
                setIsModalVisible={setIsPurcahseModalVisible}
                // fetchData={fetchPurchaseWorkbookData}
            />
        </div>
    );
};

export default UploadDocument;

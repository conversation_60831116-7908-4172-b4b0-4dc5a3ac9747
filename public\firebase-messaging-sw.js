importScripts(
    "https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"
);
importScripts(
    "https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js"
);

firebase.initializeApp({
    apiKey: "AIzaSyD51wSkS8IOe8uuYaa2loHlIJk6tUa1u7A",
    authDomain: "gymi-a1511.firebaseapp.com",
    projectId: "gymi-a1511",
    storageBucket: "gymi-a1511.appspot.com",
    messagingSenderId: "910525138413",
    appId: "1:910525138413:web:5e31a1fdde6ed7476a93e4",
});

const messaging = firebase.messaging();

// ✅ Manually handle background notifications
messaging.onBackgroundMessage((payload) => {
    // console.log("Received background message:", payload);

    if (!payload.notification) {
        console.warn("No notification payload found, skipping.");
        return;
    }

    const notificationTitle = payload.notification.title;
    const notificationOptions = {
        body: payload.notification.body,
        icon: payload.notification.icon,
        data: { url: payload.fcmOptions?.link || "/" },
    };

    // ✅ Prevent Duplicate Notifications
    self.registration.getNotifications().then((existingNotifications) => {
        const alreadyExists = existingNotifications.some(
            (n) =>
                n.title === notificationTitle &&
                n.body === notificationOptions.body
        );
        if (!alreadyExists) {
            // console.log("Showing notification:", notificationTitle);
            self.registration.showNotification(
                notificationTitle,
                notificationOptions
            );
        } else {
            // console.log("Duplicate notification prevented:", notificationTitle);
        }
    });
});

// self.addEventListener("notificationclick", (event) => {
//     console.log("Notification clicked:", event.notification);
//     event.notification.close();

//     event.waitUntil(
//         clients.matchAll({ type: "window", includeUncontrolled: true }).then((clientList) => {
//             for (let client of clientList) {
//                 if (client.url === "/mails" && "focus" in client) {
//                     return client.focus();
//                 }
//             }
//             return clients.openWindow("/mails");
//         })
//     );
// });

self.addEventListener("notificationclick", (event) => {
    // console.log("Notification clicked:", event.notification);
    event.notification.close();

    event.waitUntil(
        clients
            .matchAll({ type: "window", includeUncontrolled: true })
            .then((clientList) => {
                const targetPath = "/mails"; // Path to append
                const baseUrl = self.location.origin; // Gets 'https://yourdomain.com'

                for (let client of clientList) {
                    if (client.url.startsWith(baseUrl) && "focus" in client) {
                        // console.log("Navigating to:", baseUrl + targetPath);
                        client.navigate(baseUrl + targetPath);
                        return client.focus();
                    }
                }
                // console.log("Opening new window:", baseUrl + targetPath);
                return clients.openWindow(baseUrl + targetPath);
            })
    );
});

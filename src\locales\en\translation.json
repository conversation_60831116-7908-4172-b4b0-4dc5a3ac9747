{"_Component_": "Side Bar Admin", "gymi": "<PERSON><PERSON><PERSON>", "Welcome to GYMi": "Welcome to GYMi", "users": "Users", "knowledge_rally": "Knowledge Rally", "Subscribed Emails": "Subscribed Emails", "video_tutorial": "Video Tutorial", "logout": "Logout", "high_school": "High School", "promotions": "Promotions", "training_material": "Training Material", "update_profile": "Update Profile", "No data available": "No data available", "Image": "Image", "Full Name": "Full Name", "Parents Status": "Parents Status", "Child Name": "Child Name", "Parent Name": "Parent Name", "Child's Code": "Child's Code", "Child's Grade": "Child's Grade", "Change Password": "Change Password", "Welcome back,": "Welcome back,", "View": "View", "Edit": "Edit", "Questions": "Questions", "Subject": "Subject", "More": "More", "Type": "Type", "___Component_add_question___": "add question", "Add Question": "Add Question", "Are you sure you want to delete this question?": "Are you sure you want to delete this question?", "Select Correct Answer": "Select Correct Answer", "Practice Type": "Practice Type", "Question Type": "Question Type", "category": "Category", "Question": "Question", "Add Image": "Add Image", "Add More": "Add More", "Answer Type": "Answer Type", "Add Option": "Add Option", "Correct Answer": "Correct Answer", "Answer": "Correct Answer", "View Question": "View Question", "Edit Question": "Edit Question", "___Component_mails___": "mails components", "You have not selected any mails": "You have not selected any mails", "delete_message_popup": "Are you sure you want to delete this message?", "delete_messages_popup": "Are you sure you want to delete this messages?", "Send a mails to the user by select any user": "Send a mails to the user by select any user", "_Component_Nav_Bar": "Nav Bar Child and Parent", "hello": "Hello", "_Component_Knowledgerally": "Knowledgerally Child and Parent", "username": "Username", "this_user_name_is_exists": "This username already exists.", "add_numbers_or_symbols_to_your_name": "Add numbers or symbols to your name.", "complete_the_paused_level_before": "Complete the paused level before continuing to play other levels.", "_Component_LeaderBoard": "LeaderBoard Child and Parent", "your_performance": "Your Performance", "you_are_doing_better_than_other_players_with": "You are doing better than other players with", "small_points": "points", "team_of": "Team of", "members": "Members", "add_member": "Add member", "are_you_sure_you_want_to_leave_from_the_team": "Are you sure you want to leave from the team", "are_you_sure_you _want_to_cancel_the_request_from_the_team": "Are you sure you want to cancel the request from the team", "_Component_friends": "Friends Child and Parent", "connect_with_others_to_see_your_friends_here": "Connect with others to see your friends here.", "_Component_progress": "Progress Child and Parent", "your_are_faster_than": "Your are faster than", "your_child_is_faster_than": "Your child is faster than", "_Component_FAQ": "FAQ Child and Parent", "frequently_asked_questions": "Frequently asked questions", "and": "and", "close_modal": "Close modal", "to_see_child_progress_first_you_need": "To see child progress first you need to add your child.", "your_child_performed_better_than": "Your child performed better than", "no_comments": "No  Comments", "start_the_conversation_by_adding": "Start the conversation by adding a comment below.", "select_file": "Select file", "no_schools_found": "No Schools found", "looks_like_there_is_no_any_schools_you_are_looking_for": "Looks like there is no any schools you are looking for.", "location": "Location", "search_state": "Search state...", "search_specialisation": "Search specialisation...", "copy_link": "Copy Link", "share_link": "Share Link", "your_kid_has_been_added_successfully": "Your kid has been added successfully.", "no_material": "No Material", "registration_is_necessary_to_see_training_materials": "Registration is necessary to see training materials.", "you_have_no_mails": "You have no mails", "send_a_mails_to_the_admin_to_get_started": "Send a mails to the admin to get started", "learning_can_take_various_form": "Learning can take various forms, but ultimately, consistent practice is the key to mastery.", "child": "Child", "parent": "Parent", "begin_your_journey_as_description": "Begin your journey as a parent or child with our easy-to-use interface.", "get_started": "Get Started", "continueText": "Continue", "you_are_performed_better_than": "Your are performed better than", "of_the_participants": "of the participants.", "choose_your_avtar": "Choose your avtar", "your_progress": "Your Progress", "on_one_text": "Test your child in German, Mathematics and English for the transition into high school.", "on_two_text": "Discover all high schools in Austria.", "on_third_text": "Connect with others Parents.", "skip": "<PERSON><PERSON>", "on_fourth_text": "Find promotions about interesting products & services.", "see_how_its_work": "See how it works", "watch_and_learn": "Watch and learn with our comprehensive video tutorial.", "register": "Register", "valid_until": "Valid until", "submit": "Submit", "next": "Next", "impressum": "Impressum", "verified": "Verified!", "verified_done": "You have successfully verified account.", "impressum_details": "GYMI Media OG Feldgasse 27131 half turn Austria", "you_have_not_any_child": "You haven't added your child yet.", "to_continue_monitoring_their_progress": "To continue monitoring their progress, add a child.", "registration_is_necessary": "Registration is necessary to add your kid.", "disable": "Disable", "join_the_knowledge_rally": "Join the knowledge rally", "are_you_sure_kids_disable": "Are you sure you want to disable your kid ?", "enable": "Enable", "add_child": "Add Child", "enter_child_name": "Enter child name ", "agree_with": "Agree with ", "andText": " and ", "your_child_has_been_added_successfully": "Your child has been added successfully.", "done": "Done", "download_the_app_on_the_child": "Download the app on the child's mobile phone using QR code or go to the website ", "click_on": "Click on", "start_the_knowledge_rally": "Start the knowledge rally", "enter_code": "Enter Code :", "save": "Save", "are_you_sure_you_want_to_enable_kids": "Are you sure you want to enable your kids ?", "gymi_website_link": "www.gymi.org", "enter_your_email": "Enter your email", "enter_your_password": "Enter your password", "password_must_be_eight_character": "Password must be of 8 characters long", "confirm_your_password": "Confirm your password", "by_signing_up_you_agree_to_the": "By Signing up, you agree to the ", "sign_up": "Sign Up", "sign_in": "Sign In", "already_have_an_account": "Already have an account? ", "password_dont_match": "Password don’t match ", "forgot_password": "Forgot password?", "dont_have_account": "Don’t have account ", "incorrect_password": "Incorrect password", "reset_password": "Reset Password", "enter_your_email_address_and_send": "Enter your email address and we’ll send you a link to reset your password", "send": "Send", "sent_successfully": "Sent Successfully", "back_to_login": "Back to login", "at_least_8_character": "At least 8 characters", "at_least_1_uppercase": "At least 1 uppercase letter", "at_least_1_number": "At least 1 number", "at_least_1_symbol": "At least 1 symbol", "please_enter_valid_email": "Please enter valid email", "level_1": "Level 1", "sample_level": "Sample level", "got_it": "Got it", "sample": "<PERSON><PERSON>", "exit": "Exit", "yes": "Yes", "no": "No", "do_you_want_to_exit": "Do you want to exit from the application?", "type_here": "Type here...", "type": "Type ...", "warm_up_questions": "Warm up Questions", "back_to_home": "Back to home", "are_your_sure_you_want_to_go_back_home": "Are you sure you want to go back? You need to pause your level.", "are_your_sure_you_want_to_play_level": "Are you sure you want to play Level", "level": "Level", "okay": "Okay", "pause": "Pause", "play": "Play", "reject": "Reject", "approve": "Approve", "cancel": "Cancel", "congratulations": "Congratulations!!!", "you_unlocked_a_new_avatar": "You unlocked a new avatars.", "avatar_hint": "Note : You can access this all unlocked avatars from edit profile screen.", "you_have_completed_level": "You have completed level", "successfully": "successfully!!!", "correct_answer": "Correct answer", "earned_points": "Earned Points", "view_leader_board": "View Leaderboard", "start_warm_up_questions": "Start your level with these warm-up questions to get into the right mindset.", "we_have_successfully_sent_the_link_to_your_account": "We have successfully sent the link to your account ", "please_verify_your_email": "Please click on the verification link sent\nto your email address \n", "to_have_your_child_to_be_a_part_of_knowledge": "To have your child to be a part of knowledge rally, Add your child.", "you_can_see_more_questions_on_your_child_app": "You can see more questions on your child app.", "please_enter_required_field": "Please enter required field", "registration_is_required_for_see_child_result": "Registration is necessary to show your kids progress.", "please_enter_your_email": "Please enter your email", "please_enter_password": "Please enter your password", "please_enter_current_password": "Please enter current password", "please_enter_new_password": "Please enter new password", "please_enter_confirm_password": "Please enter confirm password", "old_and_new_password_not_be_same": "New password must be different from old password", "please_remove_space_from_name": "Please remove space from name", "please_enter_valid_password": "Please enter valid password", "please_enter_valid__new_password": "Please enter valid new password", "please_enter_verification_code": "Please enter verification code", "please_enter_required_all_filed": "Please enter required fields", "please_accept_terms_and_conditions": "Please accept terms and conditions", "your_email": "Your email", "your": "Your", "quiz_unavailable": "Quiz Unavailable", "there_are_no_quizzes_available": "There are no quizzes available at the moment. Please check back later", "email_are_not_register": "are not register, please verify before 30 days.", "camera_permission_request": "Permission Request", "camera_permission_request_des": "Please give a camera permission for take your picture.", "camera_permission_give": "Give camera permission", "storage_permission_request_des": "Please give a storage permission for upload your picture.", "storage_permission_give": "Give storage permission", "account_are_created": "Account created successfully!", "are_you_sure_you_want_to_logout": "Are you sure you want to logout from Gym<PERSON>?", "progress": "Progress", "example_email": "<EMAIL>", "all_level_completed": "All Levels Completed!!", "knowledge_rally_registration_description": "To have your child to be a part of knowledge rally, Please signup and add your child.", "enter_verification_code": "Enter Verification Code", "you_will_receive_code_from_your_parent": "You can receive a code from your parent. They must download the app and activate a code for you.", "add_number_or_symbols_to_your_name": "Add numbers or symbols to your name", "warm_up": "Warm up", "change_name": "Change name", "website": "Website", "email": "Email", "avtar": "Avatar", "team_points": "Team Points", "currently_you_are_achieved": "Currently you have achieved", "points": "points", "point": "point", "manage_your_team_to_be_a_part_of_them": "Manage Your Team to Be a Part of Them.", "are_you_part_of_any_team": "Are You Part of Any Team?", "profile": "Profile", "direction": "Direction", "call": "Call", "created": "Created", "joined": "Joined", "create_team": "Create Team", "browse_team": "Browse Team", "pending_request": "Pending request", "about": "About", "managed_my_team": "Manage My Team", "verify_your_email": "Verify your email", "we_have_a_set_verification_link": "We have sent a verification link to your email address", "please_check_your_email_and_verify_it": "Please check your email and verify your account.", "your_account_was_disabled_by_your_parents": "Your account has been disabled by your parents. If you wish to have it reactivated, please get in touch with your parents.", "my_friends": "My Friends", "no_friends_yet": "No Friends Yet", "no_users_yet": "No Users Yet", "no_friends_yet_message": "Connect with others to see your friends here.", "no_any_users_find_at_this_moment": "No any users find at this moments", "are_you_sure_you_want_to_switch_to_child": "Are you sure you want to switch from parent to child mode?", "filters": "Filters", "switchToChild": "Switch", "switchToChildView": "Switch To Child", "state": "State", "view_child_report_card": "View Report Card", "boarding_school": "Boarding School", "supervision": "Supervision", "specialisation": "Specialisation", "school_type": "School Type", "postal_code": "Postal code", "reset": "Reset", "create_post": "Create Post", "apply_filters": "Apply Filters", "comments": "Comments", "comment": "Comment", "report_comments": "Report Comments", "report": "Report", "delete_message": "Are you sure you want to delete your comment?", "delete_community_post": "Are you sure you want to delete your community post ?", "post": "Post", "tap_to_import_from_gallery": "Tap to import from gallery", "file_name": "File name", "attachment": "Attachment", "description": "Description", "registration_is_necessary_to_create_post": "Registration is necessary to create post.", "remove": "Remove", "search_here": "Search here...", "search": "Search...", "are_you_sure_you_want_to_remove": "Are you sure you want to remove", "add": "Add", "added": "Added", "all_users": "All Users", "your_account_was_disabled_by_admin": "Your account has been disabled by admin. If you wish to have it reactivated, please contact to gymi admin.", "delete": "Delete", "settings": "Settings", "enter_your_full_name": "Enter your full name", "enter_your_name": "Enter your name", "current_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm Password", "account_created_done": "Account created successfully!", "your_password_has_been_changed": "Your password has been change successfully.", "back_to_profile": "Back to profile", "please_enter_your_password_to_confirm_deletion": "Please enter your password to confirm deletion.", "choose_your_option": "Choose Your option", "choose_from_library": "Choose from library", "take_photo": "Take photo", "reset_password_error": "Exception: The supplied auth credential is incorrect, malformed or has expired.", "reset_password_actual_error": "Please enter valid current password", "your_child_progress": "Your child Progress", "your_child_among_the": "Your child is among the", "best_overall": "best overall", "download": "Download", "questionsAndAnswers": "Questions\nAnswered", "qa": "Q/A", "german_as_a_subject": "German as a subject", "maths_as_a_subject": "Mathematics as a subject", "english_as_a_subject": "English as a subject", "level_wise_performance": "Level Wise Performance", "fastest": "fastest.", "maths": "Maths", "maths_capital": "MATHS", "german": "German", "german_capital": "GERMAN", "english": "English", "english_capital": "ENGLISH", "view_levels": "View Levels", "correct_answers": "Correct Answer", "durations": "Duration", "notification": "Notifications", "today": "Today", "yesterday": "Yesterday", "a_week_ago": "A Week Ago", "generalQuestions": "GENERAL INFORMATION", "featuresAndFunctions": "FEATURES AND FUNCTIONS", "costAndPrices": "COSTS AND PRICES", "security": "SECURITY", "technicalQuestions": "TECHNICAL QUESTIONS", "no_notification_found": "No Notification found", "friend_invitation": "Friend Invitations", "will_notify_you_as_soon_as_theres_something_new": "Will notify you as soon as there's something new!", "to_have_your_child": "Registration is necessary to create your profile.", "team": "Team", "not_started_any_level_yet": "Not started any level yet", "back": "Back", "admin": "Admin", "member": "Member", "join_request": "Join Requests", "add_team_name": "Add Team Name", "enter_team_name": "Enter team name ...", "no_internet_connection": "No internet connection", "connect_with_mobile_data": "Connect with mobile data", "connect_with_wifi": "Connect with Wi-Fi", "please_connect_internet_for_best_user_experience": "Please connect internet for best user experience.", "add_friend": "Add Friends", "available_to_join": "Available To Join", "full_team": "Full Team", "you_have_requested_to_join_the": "You have requested to join the", "join": "Join", "accept": "Accept", "leave": "Leave", "requested": "Requested", "invited_by": "Invited by", "created_by": "Created by", "edit_profile": "Edit Profile", "all_post": "All Post", "my_post": "My Post", "read_more": "Read more", "read_less": "Read less", "view_all": "View all", "add_comments": "Add comments....", "no_post_created_at": "No Post Created Yet", "looks_likes_you_havent_created_any_community": "Looks like you haven't created any community post yet", "looks_likes_no_any_comments_found": "Looks like not any comments found yet.", "no_any_comments_found": "No any comments found", "you_have_a_requested_to_join_the": "You have requested to join the", "you_can_not_join_more_than_ten_teams": "You cannot join more than ten teams.", "you_can_not_add_more_than_two_teams": "You cannot add more than two teams.", "you_can_not_add_more_than_six_members": "You cannot add more than six members.", "manage_my_team": "Manage My Team", "no_team_available": "No Team Available", "no_request_available": "No invitations found", "start_building_you_team_now": "Start building your team now and get things rolling", "no_team_found": "No Team Found", "complete_level": "Complete Level", "ofText": "of", "home": "Home", "you": "You", "have": "have", "files": "Files", "file": "File", "approved": "Approved", "rejected": "Rejected", "invitation": "Invitation", "request": "Request", "mail": "Mail", "upload_photo": "Upload picture from library", "upload_video": "Upload video from library", "add_description_for_the_post": "Add description for the post", "add_category_for_the_post": "Add category for the post", "register_is_required_for_access_all_features": "Registration is necessary for access all community features", "you_need_to_add_friend_in_the_add_friends_here": "You first need to add friends in the add friends area", "rd_grade_before_proceeding_to_other_levels": "rd grade before proceeding to other levels", "no_team_found_description": "Start building your team now or join other teams and get things rolling", "you_download_training_materials_after_your_child": "You can download training materials after your child completes the 4th grade.", "register_is_required_for_mails": "Registration is necessary to connect with the admin.", "afternoon_school": "Afternoon school", "all_day_school": "All-day school", "private_in_german": "privat", "private": "Private", "public_in_german": "<PERSON><PERSON><PERSON><PERSON>", "public": "Public", "no_school_added_yet": "No Schools Added Yet", "no_search_found": "No Search Found.", "copied_to_clipboard": "Copied to clipboard", "school_empty_des": "We’ll notify you once the admin adds a school to the system. Stay tuned!", "no_promotion_added_yet": "No Promotions Available", "promotion_empty_des": "We'll let you know as soon as new promotions are added.", "uid_number": "UID - Number", "company_register_no": "Company register number", "member_of_wko": "Member of WKO", "mag_dr_michaela": "MMag. Dr. <PERSON><PERSON>", "education": "Education", "parenting": "Parenting", "leisure_and_activities": "Leisure & Activities", "others": "Others", "education_capital": "EDUCATION", "parenting_capital": "PARENTING", "leisure_and_activities_capital": "LEISURE_AND_ACTIVITIES", "others_capital": "OTHERS", "product_capital": "PRODUCT", "services_capital": "SERVICES", "product": "Product", "services": "Services", "all": "All"}
"use client";
import React, { useState, useEffect } from "react";
import Navbar from "../Navbar/navbar";
import { videoTutorialType } from "@/src/libs/constants";
import Image from "next/image";
import {
    Dropdown,
    Radio,
    Button,
    MenuProps,
    Spin,
    message,
    Menu,
    Modal,
} from "antd";
import { QueryParams, uploadFile } from "@/src/services/upload.api";
import {
    addTutorialQueryParams,
    tutorialData,
    getUserTutorial,
    addNewTutorialVideo,
    updateTutorialVideo,
    deleteTutorialVideo,
} from "@/src/services/tutorial.api";
import { useRouter } from "next/navigation";
import { Roboto } from "next/font/google";
import { useTranslation } from "react-i18next";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const VideoTutorial = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [selectedVideoType, setSelectedVideoType] = useState("PARENT");
    const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
    const [video, setVideo] = useState<string | null>(null);
    const [isRemoveModalVisible, setIsRemoveModalVisible] = useState(false);
    const [tutorialId, setTutorialId] = useState("");
    const [isUploading, setIsUploading] = useState(false);
    const [isDelete, setIsDelete] = useState(false);
    const { t } = useTranslation();

    const router = useRouter();

    const handleRadioChange = (e: any) => {
        setSelectedVideoType(e);
        setDropdownVisible(false);
    };

    const handleItemClick = (key: any) => {
        setSelectedVideoType(key);
        setDropdownVisible(false);
    };

    useEffect(() => {
        const sessionId = localStorage.getItem("session");
        if (sessionId) {
            getTutorialVideo();
        } else {
            router.push("/");
        }
    }, [selectedVideoType, isDelete]);

    const items: MenuProps["items"] = videoTutorialType.map((item) => ({
        key: item.value,
        label: (
            <div
                className={`flex justify-between items-center w-[140px] ${roboto.className}`}
                onClick={() => handleItemClick(item.value)}
            >
                <span
                    className={`text-[14px] font-roboto font-[400] ${
                        selectedVideoType === item.value
                            ? "text-black"
                            : "text-gray-500"
                    }`}
                >
                    {item.label}
                </span>
                <Radio
                    value={item.value}
                    checked={selectedVideoType === item.value}
                    className={`custom-radio mr-0 ${
                        selectedVideoType === item.value
                            ? "accent-[#67A1A3]"
                            : ""
                    }`}
                    onChange={() => handleRadioChange(item.value)}
                />
            </div>
        ),
    }));

    const getTutorialVideo = async () => {
        try {
            setIsLoading(true);
            const authorization = localStorage.getItem("idToken");
            const queryParams: addTutorialQueryParams = {
                userType: selectedVideoType,
            };
            const tutorialResponse = await getUserTutorial(
                queryParams,
                authorization
            );
            if (tutorialResponse) {
                setVideo(tutorialResponse?.value);
                setTutorialId(tutorialResponse?.id);
                setIsLoading(false);
            }
        } catch (error: any) {
            if (error.message.includes("Anleitung nicht gefunden")) {
                setVideo(null);
                setIsLoading(false);
            } else {
                message.error(
                    error.message || "Tutorial konnte nicht abgerufen werden"
                );
                setIsLoading(false);
            }
        }
    };

    const handleTutorialChange = async (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        if (e.target.files && e.target.files.length) {
            const file = e.target.files[0];
            const reader = new FileReader();

            reader.onload = async (event) => {
                if (event.target?.result && file) {
                    if (!file.type.startsWith("video/")) {
                        message.error("Es sind nur Videodateien erlaubt");
                        return;
                    }

                    const trimmedFileName = file.name.replace(/\s+/g, "_");
                    const queryParams: QueryParams = {
                        fileName: trimmedFileName,
                        fileType: "VIDEO",
                        resourceType: "TUTORIAL",
                    };
                    const authorization = localStorage.getItem("idToken");

                    try {
                        setIsUploading(true);
                        const response = await uploadFile(
                            queryParams,
                            authorization
                        );

                        if (response && response.preSignedUrl) {
                            const url = response.preSignedUrl;
                            try {
                                await fetch(url, {
                                    method: "PUT",
                                    body: file,
                                    headers: {
                                        "Content-Type": file.type,
                                    },
                                });

                                setVideo(response.outPutUrl);

                                const payload: tutorialData = {
                                    type: selectedVideoType,
                                    value: response.outPutUrl,
                                };

                                const tutorialRes = await addNewTutorialVideo(
                                    payload,
                                    authorization
                                );
                                if (tutorialRes) {
                                    setTutorialId(tutorialRes?.id);
                                    setIsUploading(false);
                                }
                            } catch (uploadError) {
                                message.error(
                                    "Das Hochladen des Video-Tutorials ist fehlgeschlagen"
                                );
                                setIsUploading(false);
                            }
                        } else {
                            message.error(
                                "Das Hochladen des Video-Tutorials ist fehlgeschlagen"
                            );
                            setIsUploading(false);
                        }
                    } catch (apiError) {
                        message.error(
                            "Das Hochladen des Video-Tutorials ist fehlgeschlagen"
                        );
                        setIsUploading(false);
                    }
                }
            };
            reader.readAsDataURL(file);
        }
    };

    const handleMenuClick = async (e: any) => {
        e.domEvent.stopPropagation();
        const { key } = e;
        if (key === "delete") {
            setIsRemoveModalVisible(true);
        }
    };

    const menu = () => (
        <Menu onClick={(e) => handleMenuClick(e)}>
            <Menu.Item key="delete">
                <div
                    className={`flex text-[16px] font-[500] justify-center ${roboto.className}`}
                >
                    <Image
                        src="/images/trash.svg"
                        alt="visibility"
                        width={22}
                        height={22}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    <p className="pb-1 text-[16px] text-[#F1676D]">
                        {t("delete")}
                    </p>
                </div>
            </Menu.Item>
            {/* <Menu.Item key="update">
                <div
                    className={`flex text-[16px] font-[500] justify-center ${roboto.className}`}
                >
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={22}
                        height={22}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    <p className="pb-1 text-[16px]">Update</p>
                </div>
            </Menu.Item> */}
        </Menu>
    );

    const handleRemoveCancel = () => {
        setIsRemoveModalVisible(false);
    };

    const handleDeleteVideoTutorial = async () => {
        try {
            setIsDelete(true);
            const authorization = localStorage.getItem("idToken");
            const tutorialResponse = await deleteTutorialVideo(
                tutorialId,
                authorization
            );
            if (tutorialResponse) {
                message.success("Tutorial erfolgreich gelöscht");
                setIsRemoveModalVisible(false);
                setIsDelete(false);
            }
        } catch (error) {
            message.error("Tutorial konnte nicht gelöscht werden");
            setIsDelete(false);
        }
    };

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`${roboto.className} bg-white text-[24px] text-black items-center px-4 mt-4 h-full`}
            >
                <div className="flex items-center">
                    <div className="flex items-center font-[600] text-[23px]">
                        {t("video_tutorial")}
                    </div>
                    <Dropdown
                        menu={{ items }}
                        trigger={["click"]}
                        placement="bottomLeft"
                        open={dropdownVisible}
                        onOpenChange={setDropdownVisible}
                    >
                        <Image
                            src={
                                dropdownVisible
                                    ? "/images/arrow-up.svg"
                                    : "/images/arrow-bottom.svg"
                            }
                            alt="toggle dropdown"
                            width={30}
                            height={30}
                            className="cursor-pointer pl-[9px] mt-[5px]"
                        />
                    </Dropdown>
                </div>
                <div className="text-black text-[16px] font-normal w-full">
                    {videoTutorialType.find(
                        (item) => item.value === selectedVideoType
                    )?.label || "-"}
                </div>
                {isLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <div
                        className="flex justify-center items-center h-[calc(100vh-200px)] w-full"
                        // style={{ height: "calc(100vh - 70px)" }}
                    >
                        {video ? (
                            <div className="relative h-[50vh] w-[45vw]">
                                <video
                                    src={video}
                                    controls
                                    width="490"
                                    height="380"
                                    className="rounded-lg w-full"
                                />
                                <Dropdown
                                    overlay={menu}
                                    trigger={["click"]}
                                    placement="bottomRight"
                                >
                                    <div
                                        className="absolute top-4 right-5 cursor-pointer bg-white rounded-md bg-opacity-80 border-2"
                                        onClick={(e) => e.stopPropagation()}
                                    >
                                        <Image
                                            src="/images/more.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                            className="cursor-pointer"
                                        />
                                    </div>
                                </Dropdown>
                            </div>
                        ) : (
                            <label>
                                <div className="bg-[#67A1A3] bg-opacity-40 border-2 border-dashed border-[#67A1A3] border-opacity-65 h-[50vh] w-[45vw] rounded-lg">
                                    {isUploading ? (
                                        <div className="flex justify-center items-center h-full">
                                            <Spin size="default" />
                                        </div>
                                    ) : (
                                        <div className="flex flex-col justify-center items-center h-full cursor-pointer">
                                            <Image
                                                src="/images/upload.svg"
                                                alt="More"
                                                width={108}
                                                height={108}
                                                className="cursor-pointer"
                                            />
                                            <p
                                                className={`text-[20px] font-[500] ${roboto.className}`}
                                            >
                                                {t("Upload Video")}
                                            </p>
                                        </div>
                                    )}
                                </div>
                                <input
                                    type="file"
                                    accept="video/*"
                                    onChange={handleTutorialChange}
                                    style={{ display: "none" }}
                                />
                            </label>
                        )}
                    </div>
                )}
            </div>
            <Modal
                open={isRemoveModalVisible}
                onCancel={handleRemoveCancel}
                footer={null}
                centered
                width={300}
                style={{ textAlign: "center" }}
                closable={false}
            >
                <Image
                    src="/images/modalDelete.svg"
                    alt="Delete"
                    width={100}
                    height={100}
                    className="mx-auto bg-white -mt-3"
                />

                {/* <h2
                    className={`text-[20px] mb-5 font-[400] ${roboto.className}`}
                >
                    Are you sure you want to delete this video?
                </h2> */}
                <div className=" flex justify-center items-center w-full">
                    <h2 className="text-[16px] font-roboto font-[400] mb-8 w-[80%] flex justify-center items-center">
                        {t("Are you sure you want to delete this video?")}
                    </h2>
                </div>
                <div className="flex justify-center space-x-4">
                    <Button
                        onClick={handleRemoveCancel}
                        className="bg-transparent cancelbutton font-roboto text-[#F1676D] text-[18px] h-[46px] border-[1px] border-[#F1676D] w-[116px] font-medium rounded-xl"
                    >
                        {t("Cancel")}
                    </Button>
                    <Button
                        type="primary"
                        className="bg-[#F1676D] shadow-inner deleteButton font-roboto text-white text-[18px] w-[116px] h-[46px] font-medium rounded-xl"
                        onClick={handleDeleteVideoTutorial}
                        loading={isDelete}
                    >
                        {t("delete")}
                    </Button>
                </div>
            </Modal>
        </div>
    );
};

export default VideoTutorial;

import React, { useEffect } from "react";
import { <PERSON><PERSON> } from "next/font/google";
import { But<PERSON> } from "antd";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import {handleChangeLanguage} from  "@/components/context/languageContext"

const roboto = Roboto({
  weight: ["300", "400", "500", "700"],
  subsets: ["latin"],
});

const RegisterModal = ({setIsModalOpen, setMessage, deleteMsg, loading}: any) => {
  const { t } = useTranslation();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40 animate-fadeIn">
      <div
        className={`text-center bg-white px-10 pt-4 w-[383px] pb-4 rounded-xl flex justify-center items-center text-black ${roboto.className} animate-slideIn relative`}
      >
        <button
          type="button"
          className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center absolute top-2 right-2"
          onClick={() => {
            setMessage(null)
            setIsModalOpen(false)
          }}
        >
          <svg
            className="w-3 h-3"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 14 14"
          >
            <path
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
            />
          </svg>
          <span className="sr-only">{t("close_modal")}</span>
        </button>
        <div>
          <div className="flex justify-center">
            <Image
              src="/images/mails/deleteModalLogo.svg"
              width={80}
              height={80}
              alt="delete"
            />
          </div>
          <p className="text-[16px] text-[#1E1E1E] leading-[18.75px] font-[400] text-center mt-1">
          {t("delete_message_popup")}
          </p>
          <div>
            <div className="text-center flex justify-between gap-4 mt-10">
              <Button
                type="primary"
                size="large"
                onClick={deleteMsg}
                style={{ backgroundColor: "#F1676D", boxShadow: "0px 0px 12px 0px  inset #00000040" }}
                className="w-full h-[42px] font-medium font-roboto bg-[#F1676D] text-white"
                loading={loading}
              >
                {t("delete")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterModal;

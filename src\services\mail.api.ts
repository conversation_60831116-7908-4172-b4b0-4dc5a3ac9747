import { fetch } from "@/src/libs/helpers";
import axios from "axios";

export interface QueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    search_column?: string;
    type?: string;
    chatId?: string;
    childId?: string;
    id?: string;
    userType?: string;
    messageId?: string;
}

export interface media {
    content?: string;
    type?: string;
}

export interface messageDetails {
    value?: string;
    media?: media[] | any;
}

export const getAllCurrentUserChats = async (
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/chat/current-user-chats",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getFullChatsByChatId = async (
    chatId: string,
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/chat/${chatId}/chat-messages`,
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const readNotificationByChatId = async (
    chatId: string,
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/chat/${chatId}/read-notification`,
        method: "PUT",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const sendMessageByChatId = async (
    chatId: string,
    payload: messageDetails,
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/chat/${chatId}/send-message`,
        method: "POST",
        params: queryParams,
        data: payload,
        headers: {
            Authorization: authorization,
        },
    });
};

export const deleteMessageById = async (
    messageId: string,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/chat/${messageId}/delete-message`,
        method: "DELETE",
        
        headers: {
            Authorization: authorization,
        },
    });
};

export const getPresingedUrl = async (
    type: string,
    authorization: string,
): Promise<any> => {
    const timestamp = Date.now();
    const fileName = `${type}_${timestamp}`;
    const URLs = await fetch({
        url: `/upload/presinged-url?fileName=${fileName}&fileType=${type}&resourceType=USER`,
        method: "GET",
        headers: {
            Authorization: authorization,
        }
    });
    return URLs;
};

export const setFileToPresingedUrl = async (
    preSignedUrl: string,
    file: any
): Promise<any> => {
    const res = await axios.put(preSignedUrl, file, {
        headers: {
            "Content-Type": file.type,
        }
    });
    return res;
};

export const updateMessageById = async (
    messageId: string,
    payload: messageDetails,
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/chat/${messageId}/edit-message`,
        method: "PUT",
        data: payload,
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};
"use client";
import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import {
    Dropdown,
    Radio,
    Table,
    Menu,
    MenuProps,
    Avatar,
    Spin,
    Input,
    message,
    Modal,
    Button,
    Space,
    Select,
    Col,
} from "antd";
import { <PERSON><PERSON>s, Roboto, Inter } from "next/font/google";
import DeleteModal from "@/components/mails/deleteMsgModal";
import { useRouter } from "next/navigation";
import { userType } from "@/src/libs/constants";
import {
    getAllCurrentUserChats,
    QueryParams,
    getFullChatsByChatId,
    getPresingedUrl,
    setFileToPresingedUrl,
    readNotificationByChatId,
    sendMessageByChatId,
    deleteMessageById,
    updateMessageById,
} from "@/src/services/mail.api";
import { UserOutlined } from "@ant-design/icons";
import moment from "moment";
import Recorder from "@/components/mails/recorder";
import { io } from "socket.io-client";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";

const SOCKET_EVENT = "new-message";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const inter = Inter({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface allUsersData {
    message: string;
    id: string;
    picture?: string;
    chatName?: string;
    latestMessage?: string;
    lastMessageId?: string;
    status?: string;
    isRead?: boolean;
    isDeleted?: boolean;
    updatedAt?: string;
    createdAt?: string;
    senderId?: string;
}

const Mails = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [selectedUser, setSelectedUser] = useState(
        localStorage.getItem("chatUserType") || "PARENT"
    );
    const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [isGetAllChatsLoading, setIsGetAllChatsLoading] = useState(false);
    const [allUsersChatsRes, setAllUsersChatsRes] = useState<any>([]);
    const [userChatsRes, setUserChatsRes] = useState<allUsersData[]>([]);
    const [isUserChatLoading, setIsUserChatLoading] = useState(false);
    const [selectedChatId, setSelectedChatId] = useState<string | null>(
        localStorage.getItem("chatId") || null
    );
    const [chatIndex, setChatIndex] = useState<any>();
    const inputRef = useRef<any>(null);
    const [messageText, setMessageText] = useState<any>("");
    const [mediaFile, setMediaFile] = useState<any>(null);
    const [binaryMedia, setBinaryMedia] = useState<any>(null);
    const [mediaType, setMediaType] = useState<any>("");
    const [isRecordingOpen, setIsRecordingOpen] = useState(false);
    const [isRecording, setIsRecording] = useState(false);
    const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
        null
    );
    const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
    const [audioUrl, setAudioUrl] = useState<any>(null);
    const [refatch, setRefatch] = useState(false);
    const [total, setTotal] = useState();

    const { t } = useTranslation();

    const socketRef = useRef<any>(null);
    const router = useRouter();

    const handleItemClick = (key: any) => {
        setSelectedUser(key);
        setDropdownVisible(false);
    };

    const handleRadioChange = (e: any) => {
        setSelectedUser(e);
        setDropdownVisible(false);
    };

    const items: MenuProps["items"] = userType.map((item) => ({
        key: item.value,
        label: (
            <div
                className="flex justify-between items-center"
                onClick={() => handleItemClick(item.value)}
            >
                <span
                    className={`text-[14px] font-roboto font-medium mr-[50px] ${
                        selectedUser === item.value
                            ? "text-black"
                            : "text-gray-500"
                    }`}
                >
                    {item.label === "Child" ? t("child") : t("parent")}
                </span>
                <Radio
                    value={item.value}
                    checked={selectedUser === item.value}
                    className={`custom-radio ${
                        selectedUser === item.value ? "accent-[#67A1A3]" : ""
                    }`}
                    onChange={() => handleRadioChange(item.value)}
                />
            </div>
        ),
    }));

    useEffect(() => {
        const session = localStorage.getItem("session");
        if (session) {
            fetchAllUserChatData();
        } else {
            router.push("/");
        }
    }, [selectedUser, searchQuery]);

    useEffect(() => {
        const session = localStorage.getItem("session");
        const type = localStorage.getItem("chatUserType");
        if (session) {
            setSelectedChatId(null);
        }
    }, [selectedUser]);

    /**
     * Custom sorting function for chat list
     * Sorting priority:
     * 1. New chats (latestMessage is null) - shown first
     * 2. Unread chats with messages (isRead: false && unreadMessagesCount > 0) - shown second
     * 3. Read chats with messages - shown last
     * Within each category, sort by most recent updatedAt time
     */
    const sortChatList = (chats: any[]) => {
        return chats.sort((a, b) => {
            // Priority 1: New chats (latestMessage is null) should be at the top
            const aIsNewChat = a.latestMessage === null;
            const bIsNewChat = b.latestMessage === null;

            if (aIsNewChat && !bIsNewChat) return -1;
            if (!aIsNewChat && bIsNewChat) return 1;

            // Priority 2: Unread chats with messages should come next
            const aIsUnread = !a.isRead && (a.unreadMessagesCount || 0) > 0;
            const bIsUnread = !b.isRead && (b.unreadMessagesCount || 0) > 0;

            if (aIsUnread && !bIsUnread) return -1;
            if (!aIsUnread && bIsUnread) return 1;

            // Priority 3: Sort by updatedAt time (most recent first)
            const aTime = new Date(a.updatedAt || a.createdAt).getTime();
            const bTime = new Date(b.updatedAt || b.createdAt).getTime();

            return bTime - aTime;
        });
    };

    const fetchAllUserChatData = async () => {
        const queryParams: QueryParams = {
            userType: selectedUser,
            skip: 0,
            take: 100,
            search: searchQuery,
            search_column: "chatName",
            orderBy: "updatedAt|desc",
        };
        const authorization = localStorage.getItem("idToken");

        try {
            setIsGetAllChatsLoading(true);
            // Fetch initial chat data
            const allChatsRes = await getAllCurrentUserChats(
                queryParams,
                authorization
            );

            // If response is valid
            if (allChatsRes) {
                // Apply custom sorting to the initial list
                const sortedInitialChats = sortChatList([...allChatsRes.list]);
                setAllUsersChatsRes(sortedInitialChats);
                setTotal(allChatsRes.total);

                // Check if there are more data to load (hasMany is true)
                if (allChatsRes.hasMany) {
                    // Continue fetching more data until there is no more data to fetch
                    let skip = queryParams.take; // Start from the next page
                    let moreChats: any[] = [];

                    while (allChatsRes.hasMany) {
                        // Update the query to fetch the next page of data
                        const nextPageQuery = { ...queryParams, skip };

                        // Fetch the next page of data
                        const nextChatsRes = await getAllCurrentUserChats(
                            nextPageQuery,
                            authorization
                        );

                        if (nextChatsRes && nextChatsRes.list.length > 0) {
                            moreChats = moreChats.concat(nextChatsRes.list); // Append new data
                            allChatsRes.hasMany = nextChatsRes.hasMany; // Check if there are more pages
                            if (
                                skip !== undefined &&
                                queryParams.take !== undefined
                            ) {
                                skip += queryParams.take; // Increment skip if it's defined
                            } // Move to the next page
                        } else {
                            break;
                        }
                    }

                    // Once all data is fetched, apply sorting to the complete list
                    setAllUsersChatsRes((prevChats: any) => {
                        const allChats = [...prevChats, ...moreChats];
                        return sortChatList(allChats);
                    });
                }
                setIsGetAllChatsLoading(false);
            } else {
                message.error(t("Empty response data"));
            }
        } catch (error) {
            message.error(t("Data could not be retrieved"));
        } finally {
            setIsGetAllChatsLoading(false);
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (selectedChatId !== null) {
            fetchSelectedUserChat(selectedChatId);
        }
    }, [refatch]);

    useEffect(() => {
        const storedChatId = localStorage.getItem("chatId");
        if (storedChatId && allUsersChatsRes) {
            const index = allUsersChatsRes.findIndex(
                (chat: any) => chat.id === storedChatId
            );
            if (index !== -1) {
                setChatIndex(index);
            }
        }
    }, [allUsersChatsRes]);

    const fetchSelectedUserChat = async (chatId: any) => {
        const queryParams: QueryParams = {
            type: selectedUser,
            skip: 0,
            take: 100,
            search: searchQuery,
            orderBy: "createdAt|asc",
            include: "media",
        };
        const authorization = localStorage.getItem("idToken");
        const chatUserType = localStorage.getItem("chatUserType");
        try {
            if (chatUserType === selectedUser) {
                setIsUserChatLoading(true);
                const fullChatsRes = await getFullChatsByChatId(
                    chatId,
                    queryParams,
                    authorization
                );
                if (fullChatsRes) {
                    setUserChatsRes(fullChatsRes.list);
                    setIsUserChatLoading(false);
                    const queryParams: QueryParams = {
                        chatId: chatId,
                    };
                    if (
                        fullChatsRes?.list?.[fullChatsRes.total - 1]?.isRead ===
                        false
                    ) {
                        const readNotificationRes =
                            await readNotificationByChatId(
                                chatId,
                                queryParams,
                                authorization
                            );
                    }
                } else {
                    message.error(t("Empty response data"));
                }
            }
        } catch (error) {
            message.error(t("Data could not be retrieved"));
        }
        setIsUserChatLoading(false);
        setIsLoading(false);
    };

    const formatRecordingTime = (seconds: number) => {
        return [Math.floor(seconds / 60), seconds % 60]
            .map((v) => `0${v}`.slice(-2))
            .join(":");
    };

    const toggleRecording = () => {
        setIsRecording((prev) => !prev);
    };

    const timerRef = useRef<NodeJS.Timeout | null>(null);

    const startTimer = () => {
        timerRef.current = setInterval(() => {
            setRecordingTime((prevTime) => prevTime + 1);
        }, 1000);
    };

    const stopTimer = () => {
        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }
    };

    const stopRecording = () => {
        if (mediaRecorder) {
            mediaRecorder.stop();
            setIsRecordingStoped(true);
            setIsRecording(false);
            stopTimer(); // Stop the timer
        }
    };

    const startRecording = async () => {
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            message.error(t("Your browser does not support audio recording.")); //Your browser does not support audio recording.
            return;
        }

        const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
        });
        const recorder = new MediaRecorder(stream);
        setMediaRecorder(recorder);

        const audioChunks: BlobPart[] = [];

        recorder.ondataavailable = (event) => {
            audioChunks.push(event.data);
        };

        recorder.onstop = () => {
            const blob = new Blob(audioChunks, { type: "audio/wav" });
            const url = URL.createObjectURL(blob);
            setAudioBlob(blob);
            setAudioUrl(url);
            setAudioBlob(blob);
            setMediaType("AUDIO");
        };

        recorder.start();
        setIsRecording(true);
        setRecordingTime(0); // Reset the timer
        startTimer();
    };

    useEffect(() => {
        if (isRecording) {
            startRecording();
        } else {
            stopRecording();
        }
    }, [isRecording]);

    const handleImage = (e: any) => {
        const file = e.target.files[0];
        if (file) {
            if (file.type === "image/png") {
                setMediaType("IMAGE");
            }
            if (file.type === "video/mp4") {
                setMediaType("VIDEO");
            }
            const reader: any = new FileReader();
            reader.onloadend = () => {
                const binaryStr: any = reader.result;
                const base64String = arrayBufferToBase64(
                    reader.result,
                    file.type
                );
                setMediaFile(base64String);
                setBinaryMedia(new Blob([binaryStr], { type: file.type }));
            };
            reader.readAsArrayBuffer(file);
        }
    };

    const arrayBufferToBase64 = (buffer: any, type: string) => {
        let binary = "";
        const bytes = new Uint8Array(buffer);
        const len = bytes.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(bytes[i]);
        }

        const mimeType = type === "image" ? "image/jpeg" : "video/mp4";

        return `data:${mimeType};base64,${window.btoa(binary)}`;
    };

    const [sendLoading, setSendLoading] = useState(false);
    const [isRecordingStoped, setIsRecordingStoped] = useState(false);
    const [recordingTime, setRecordingTime] = useState(0);

    const handleSendMessage = async () => {
        try {
            if (
                messageText.trim() === "" &&
                mediaFile === null &&
                audioUrl === null
            ) {
                return;
            }

            setSendLoading(true);
            const idToken: any = localStorage.getItem("idToken");
            const chatId: any = allUsersChatsRes?.[chatIndex]?.id;
            let opUrl: any = "";

            if (mediaFile !== null || binaryMedia !== null) {
                const res = await getPresingedUrl(mediaType, idToken);
                if (res) {
                    const { preSignedUrl, outPutUrl } = res;
                    opUrl = outPutUrl;
                    const setFileToUrl: any = await setFileToPresingedUrl(
                        preSignedUrl,
                        binaryMedia
                    );
                    if (setFileToUrl) {
                        // console.log("media====>", opUrl);
                    }
                }
            }
            if (audioBlob !== null) {
                const res = await getPresingedUrl(mediaType, idToken);
                if (res) {
                    const { preSignedUrl, outPutUrl } = res;
                    opUrl = outPutUrl;
                    const setFileToUrl: any = await setFileToPresingedUrl(
                        preSignedUrl,
                        audioBlob
                    );
                    if (setFileToUrl) {
                        // console.log("audio====>", opUrl);
                    }
                }
            }

            const timeStamp = Date.now();

            const payload: any = {
                ...(messageText.trim() !== "" && { value: messageText }),
                ...(binaryMedia !== null &&
                    opUrl !== "" && {
                        media: [
                            {
                                content: opUrl,
                                type: mediaType,
                            },
                        ],
                    }),
                ...(audioBlob !== null &&
                    opUrl !== "" && {
                        media: [
                            {
                                content: opUrl,
                                type: mediaType,
                            },
                        ],
                    }),
            };

            const queryParams: QueryParams = {
                chatId: chatId,
            };

            const send = await sendMessageByChatId(
                chatId,
                payload,
                queryParams,
                idToken
            );

            if (send) {
                setRefatch(!refatch);

                const authorization = localStorage.getItem("idToken");
                const readNotificationRes = await readNotificationByChatId(
                    chatId,
                    queryParams,
                    authorization
                );
            }
        } catch (error) {
            message.error(t("Error sending message, try again later!")); //Error sending message, try again later!
        } finally {
            setSendLoading(false);
            setMessageText("");
            setMediaFile(null);
            setBinaryMedia(null);
            setAudioBlob(null);
            setAudioUrl(null);
            setIsRecordingOpen(false);
            setIsRecordingStoped(false);
            setRecordingTime(0);
        }
    };
    const handleChatClick = (chatId: string, chatType: string, index: any) => {
        setSelectedChatId(chatId); // Set the selected chat
        fetchSelectedUserChat(chatId); // Call your existing function to fetch chat data
        setChatIndex(index);
        localStorage.setItem("chatId", chatId);
        localStorage.setItem("chatUserType", chatType);
    };

    const [selectedMessage, setSelectedMessage] = useState<any>(null);
    const [selectedMessageIndex, setSelectedMessageIndex] = useState<any>(null);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

    const handleMessage = (msg: any, index: number) => {
        const userId = localStorage.getItem("userId");
        if (userId === msg.senderId) {
            setSelectedMessageIndex(index);
            setSelectedMessage(msg);
            setIsDeleteModalOpen(true);
        }
    };

    function formatTime(inputTime: any) {
        const time = moment(inputTime);
        const now = moment();

        // return time.format("h:mm A");

        if (time.isSame(now, "day")) {
            return time.format("h:mm A");
        } else if (time.isSame(now.subtract(1, "days"), "day")) {
            return `Yesterday ${time.format("h:mm A")}`;
        } else {
            return time.format("DD-MM-YY | h:mm A");
        }
    }

    const bottomRef = useRef<any>(null);

    useEffect(() => {
        if (bottomRef.current) {
            bottomRef.current.scrollIntoView({ behavior: "smooth" });
        }
    }, [userChatsRes]);

    const handleEnterKeyPress = (e: any) => {
        if (e.key === "Enter" && isEditing) {
            handleEditMsg();
        } else if (e.key === "Enter") {
            handleSendMessage();
        }
    };

    const [loading, setLoading] = useState(false);
    const handleDeleteMsg = async () => {
        try {
            setLoading(true);
            const idToken: any = localStorage.getItem("idToken");
            const deleteMsg = await deleteMessageById(
                selectedMessage.id,
                idToken
            );
            if (deleteMsg) {
                userChatsRes.splice(selectedMessageIndex, 1);
                setIsDeleteModalOpen(false);
                message.success(t("Message deleted successfully."));
            }
        } catch (error) {
            message.error(t("Error deleting message!"));
        } finally {
            setLoading(false);
        }
    };

    const handleEditMsg = async () => {
        try {
            if (messageText.trim() === "") {
                message.warning(t("Please enter message to update!"));
                return;
            }

            setLoading(true);
            const idToken: any = localStorage.getItem("idToken");
            const payload: any = {
                ...(messageText.trim() !== "" && { value: messageText }),
                ...(binaryMedia !== null && {
                    media: [
                        {
                            content: binaryMedia,
                            type: mediaType,
                        },
                    ],
                }),
            };

            const queryParams: QueryParams = {
                messageId: selectedMessage.id,
                id: selectedMessage.senderId,
                chatId: selectedChatId || "",
                userType: "ADMIN",
            };

            const updateMsg = await updateMessageById(
                selectedMessage.id,
                payload,
                queryParams,
                idToken
            );
            if (updateMsg) {
                message.success(t("Message Updated successfully."));
                setSelectedMessage(null);
                setIsEditing(false);
                setMessageText("");
                setMediaType("");
                setBinaryMedia(null);
                setSelectedMessageIndex("");
            }
        } catch (error) {
            message.error(t("Error in updating message!"));
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        const socketUrl = process.env.NEXT_API_ENDPOINT; // Update with your socket URL
        socketRef.current = io(socketUrl, {
            transports: ["websocket"],
            autoConnect: true,
        });

        socketRef.current.on("connect", () => {
            // console.log("Socket connected");
        });

        socketRef.current.on("disconnect", () => {
            // console.log("Socket disconnected");
        });

        // Listen for incoming messages
        socketRef.current.on(SOCKET_EVENT, (newMessage: any) => {
            const chatId: any = localStorage.getItem("chatId");

            if (chatId === newMessage?.content?.chatId) {
                if (newMessage?.messageType === "new") {
                    setUserChatsRes((prevMessages: any) => [
                        ...prevMessages,
                        newMessage?.content,
                    ]);
                }

                if (newMessage?.messageType === "update") {
                    setUserChatsRes((prevMessages: any) =>
                        prevMessages.map((message: any) =>
                            message.id === newMessage?.content?.id
                                ? { ...message, ...newMessage?.content }
                                : message
                        )
                    );
                }

                if (newMessage?.messageType === "delete") {
                    setUserChatsRes((prevMessages: any) =>
                        prevMessages.filter(
                            (message: any) =>
                                message.id !== newMessage?.content?.id
                        )
                    );
                }
            }

            if (allUsersChatsRes) {
                setAllUsersChatsRes((prevChats: any) => {
                    const updatedChatId = newMessage?.content?.chatId;

                    // Find the updated chat and create updated list
                    const updatedChats = prevChats.map((chat: any) => {
                        if (chat.id === updatedChatId && newMessage?.messageType === "new") {
                            return {
                                ...chat,
                                unreadMessagesCount:
                                    chatId !== updatedChatId
                                        ? (chat.unreadMessagesCount || 0) + 1
                                        : chat.unreadMessagesCount,
                                latestMessage: newMessage?.content?.message,
                                updatedAt: newMessage?.content?.updatedAt,
                                isRead:
                                    chatId !== updatedChatId
                                        ? newMessage?.content?.isRead
                                        : true,
                            };
                        }
                        return chat;
                    });

                    // Apply sorting to maintain proper order
                    return sortChatList(updatedChats);
                });
            }
        });

        return () => {
            socketRef.current.disconnect();
        };
    }, []);

    useEffect(() => {
        if (selectedChatId) {
            setAllUsersChatsRes((prevChats: any) => {
                const updatedChats = prevChats.map((chat: any) => {
                    if (chat.id === selectedChatId) {
                        return {
                            ...chat,
                            isRead: true,
                            unreadMessagesCount: 0,
                        };
                    }
                    return chat;
                });

                // Apply sorting after marking chat as read
                return sortChatList(updatedChats);
            });
        }
    }, [selectedChatId]);

    const [isEditing, setIsEditing] = useState(false);
    const menue = (record: any, index: any): MenuProps["items"] => [
        record?.message !== ""
            ? {
                  key: "edit",
                  label: (
                      <div
                          className="flex gap-5 w-auto cursor-pointer"
                          onClick={() => {
                              setSelectedMessage(record);
                              setIsEditing(true);
                              setMessageText(record.message);
                              if (record.media) {
                                  setMediaType(record.media.type);
                                  setBinaryMedia(record.media.content);
                              }
                              setSelectedMessageIndex(index);
                          }}
                      >
                          <img
                              src="/images/edit.svg"
                              alt="logout"
                              width={20}
                              height={20}
                              className="cursor-pointer"
                          />
                          <span
                              className={`text-[14px] text-black font-[400] ${roboto.className}`}
                          >
                              {t("Edit")}
                          </span>
                      </div>
                  ),
              }
            : null,
        {
            key: "delete",
            label: (
                <div
                    className="flex gap-5 w-auto cursor-pointer"
                    onClick={() => {
                        setSelectedMessage(record);
                        setSelectedMessageIndex(index);
                        setIsDeleteModalOpen(true);
                    }}
                >
                    <img
                        src="/images/trash.svg"
                        alt="logout"
                        width={20}
                        height={20}
                        className="cursor-pointer"
                    />
                    <span
                        className={`text-[14px] text-[#F1676D] font-[400] ${roboto.className}`}
                    >
                        {t("Delete")}
                    </span>
                </div>
            ),
        },
    ];

    const hendleCancelEditMessage = () => {
        setSelectedMessage(null);
        setIsEditing(false);
        setMessageText("");
        setMediaType("");
        setBinaryMedia(null);
        setSelectedMessageIndex(null);
    };

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`${roboto.className} w-full bg-white text-[24px] text-black items-center mt-4`}
            >
                {isLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <>
                        <div className="flex items-center px-4">
                            <div className="flex items-center font-[600] text-[23px]">
                                Mails
                                {!isGetAllChatsLoading && (
                                    <span className="text-gray-400 font-semibold text-[18px] font-roboto ml-2">
                                        ({total})
                                    </span>
                                )}
                            </div>
                            <Dropdown
                                menu={{ items }}
                                trigger={["click"]}
                                placement="bottomLeft"
                                open={dropdownVisible}
                                onOpenChange={setDropdownVisible}
                            >
                                <Image
                                    src={
                                        dropdownVisible
                                            ? "/images/arrow-up.svg"
                                            : "/images/arrow-bottom.svg"
                                    }
                                    alt="toggle dropdown"
                                    width={30}
                                    height={30}
                                    className="cursor-pointer pl-[9px]"
                                />
                            </Dropdown>
                        </div>
                        <div className={`text-black text-[16px] px-4`}>
                            {selectedUser === "CHILD"
                                ? t("child")
                                : t("parent")}
                        </div>
                        <hr className="custom-hr mt-2"></hr>
                        {isGetAllChatsLoading ? (
                            <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                                <Spin size="default" />
                            </div>
                        ) : (
                            <div className="pl-4 w-full flex h-[calc(100vh-150px)] overflow-y-auto">
                                <div className="w-[30%] border-r-[1px] border-solid border-[#D3E3E3] py-2">
                                    <div>
                                        <div className="text-[16px] font-normal text-black h-[60px] flex items-center relative">
                                            <input
                                                type="text"
                                                placeholder={t("search")}
                                                className="bg-[#D9D9D9] bg-opacity-[20%] pl-10 w-[95%] py-2 rounded-lg h-10"
                                                value={searchQuery}
                                                onChange={(e: any) =>
                                                    setSearchQuery(
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            <Image
                                                src="/images/search_1.svg"
                                                alt="search"
                                                width={22}
                                                height={22}
                                                className="absolute ml-2"
                                            />
                                        </div>
                                        <div className="w-full space-y-2 h-[69vh] overflow-y-scroll scrollbar">
                                            {allUsersChatsRes?.map(
                                                (chat: any, index: any) => (
                                                    <div
                                                        key={chat.id}
                                                        className={`flex w-[95%] cursor-pointer py-2 ${
                                                            selectedChatId ===
                                                            chat.id
                                                                ? "bg-[#004F530F] rounded-md border-[1px] border-[#004F530F]"
                                                                : "bg-gray-100 rounded-md border-[1px] border-gray-100"
                                                        }`}
                                                        onClick={() =>
                                                            handleChatClick(
                                                                chat.id,
                                                                chat.chatType,
                                                                index
                                                            )
                                                        }
                                                    >
                                                        <div className="flex px-3 w-full">
                                                            <div className="mr-2">
                                                                {chat?.picture ===
                                                                null ? (
                                                                    <Avatar
                                                                        size={
                                                                            48
                                                                        }
                                                                        className="shadow-lg custom-avatar-size"
                                                                        icon={
                                                                            <UserOutlined />
                                                                        }
                                                                    />
                                                                ) : chat?.picture ===
                                                                  "FEMALE" ? (
                                                                    <Avatar
                                                                        size="large"
                                                                        src="/images/Avatar_female.svg"
                                                                        alt="Male Profile"
                                                                        className="border-none shadow-lg custom-avatar-size"
                                                                    />
                                                                ) : chat?.picture ===
                                                                  "MALE" ? (
                                                                    <Avatar
                                                                        size="large"
                                                                        src="/images/Avatar_male.svg"
                                                                        alt="Male Profile"
                                                                        className="border-none shadow-lg custom-avatar-size"
                                                                    />
                                                                ) : (
                                                                    <Avatar
                                                                        size={
                                                                            48
                                                                        }
                                                                        src={
                                                                            chat?.picture
                                                                        }
                                                                        alt="Profile"
                                                                        className="border-none shadow-lg custom-avatar-size"
                                                                    />
                                                                )}
                                                            </div>
                                                            <div className="flex flex-col w-full">
                                                                <div className="flex justify-between items-center">
                                                                    <div
                                                                        className={`${
                                                                            inter.className
                                                                        } ${
                                                                            chat?.isRead
                                                                                ? "font-[400]"
                                                                                : "font-[500]"
                                                                        } text-[16px]`}
                                                                    >
                                                                        {chat?.chatName
                                                                            ? chat.chatName.includes(
                                                                                  "@"
                                                                              )
                                                                                ? chat.chatName.split(
                                                                                      "@"
                                                                                  )[0]
                                                                                : chat.chatName
                                                                            : "Unknown"}
                                                                    </div>
                                                                    <div
                                                                        className={`${
                                                                            roboto.className
                                                                        } ${
                                                                            chat?.isRead
                                                                                ? "font-[400] text-[#AAAAAA]"
                                                                                : "font-[500] text-[#000000]"
                                                                        } text-[12px] flex justify-center items-center mt-1`}
                                                                    >
                                                                        {chat?.updatedAt
                                                                            ? moment(
                                                                                  chat?.updatedAt
                                                                              ).isSame(
                                                                                  moment(),
                                                                                  "day"
                                                                              )
                                                                                ? moment(
                                                                                      chat?.updatedAt
                                                                                  ).format(
                                                                                      "hh:mm A"
                                                                                  )
                                                                                : moment(
                                                                                      chat?.updatedAt
                                                                                  ).isSame(
                                                                                      moment().subtract(
                                                                                          1,
                                                                                          "days"
                                                                                      ),
                                                                                      "day"
                                                                                  )
                                                                                ? "Yesterday"
                                                                                : moment(
                                                                                      chat?.updatedAt
                                                                                  ).fromNow(
                                                                                      true
                                                                                  )
                                                                            : ""}
                                                                    </div>
                                                                </div>
                                                                <div className="flex justify-between items-center">
                                                                    <div
                                                                        className={`font-[400] text-[14px] text-[#AAAAAA] ${inter.className}`}
                                                                    >
                                                                        {chat?.latestMessage
                                                                            ? chat
                                                                                  ?.latestMessage
                                                                                  .length >
                                                                              30
                                                                                ? `${chat?.latestMessage.substring(
                                                                                      0,
                                                                                      30
                                                                                  )}...`
                                                                                : chat?.latestMessage
                                                                            : ""}
                                                                    </div>
                                                                    <div
                                                                        className={`${
                                                                            inter.className
                                                                        } ${
                                                                            chat?.isRead
                                                                                ? ""
                                                                                : "font-[600] text-[12px] text-[#FFFFFF] bg-[#67A1A3] rounded-full px-[7px]"
                                                                        } `}
                                                                    >
                                                                        {chat?.isRead
                                                                            ? ""
                                                                            : chat?.unreadMessagesCount}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )
                                            )}
                                        </div>
                                    </div>
                                </div>
                                {selectedChatId === null && (
                                    <div className="w-[70%] h-[74vh] flex flex-col justify-center items-center">
                                        <Image
                                            src="/images/mails/emptyMail.svg"
                                            alt="search"
                                            width={56}
                                            height={56}
                                            className="ml-2 w-[152px] h-[80px]"
                                        />
                                        <div
                                            className={`text-[16px] font-[500] text-[#1E1E1E] ${roboto.className}`}
                                        >
                                            {t(
                                                "You have not selected any mails"
                                            )}
                                        </div>
                                        <div
                                            className={`text-[14px] font-[400] text-[#8E9292] ${roboto.className}`}
                                        >
                                            {t(
                                                "Send a mails to the user by select any user"
                                            )}
                                        </div>
                                    </div>
                                )}
                                {selectedChatId !== null && (
                                    <div className="w-[70%]">
                                        <div className="flex items-center space-x-2 py-3 px-5">
                                            <div className="">
                                                {/* {allUsersChatsRes?.[chatIndex]
                                                ?.picture === null ? (
                                                <Avatar
                                                    size={42}
                                                    className="shadow-lg custom-avatar-size"
                                                    icon={<UserOutlined />}
                                                />
                                            ) : (
                                                <Avatar
                                                    size={42}
                                                    src={
                                                        allUsersChatsRes?.[
                                                            chatIndex
                                                        ]?.picture
                                                    }
                                                    alt="Profile"
                                                    className="border-none shadow-lg custom-avatar-size"
                                                />
                                            )} */}
                                                {allUsersChatsRes?.[chatIndex]
                                                    ?.picture === null ? (
                                                    <Avatar
                                                        size={48}
                                                        className="shadow-lg custom-avatar-size"
                                                        icon={<UserOutlined />}
                                                    />
                                                ) : allUsersChatsRes?.[
                                                      chatIndex
                                                  ]?.picture === "FEMALE" ? (
                                                    <Avatar
                                                        size="large"
                                                        src="/images/Avatar_female.svg"
                                                        alt="Male Profile"
                                                        className="border-none shadow-lg custom-avatar-size"
                                                    />
                                                ) : allUsersChatsRes?.[
                                                      chatIndex
                                                  ]?.picture === "MALE" ? (
                                                    <Avatar
                                                        size="large"
                                                        src="/images/Avatar_male.svg"
                                                        alt="Male Profile"
                                                        className="border-none shadow-lg custom-avatar-size"
                                                    />
                                                ) : (
                                                    <Avatar
                                                        size={48}
                                                        src={
                                                            allUsersChatsRes?.[
                                                                chatIndex
                                                            ]?.picture
                                                        }
                                                        alt="Profile"
                                                        className="border-none shadow-lg custom-avatar-size"
                                                    />
                                                )}
                                            </div>
                                            {/* <div
                                            className={`${roboto.className} text-[16px] font-[400] text-[#000000]`}
                                        >
                                            {allUsersChatsRes?.[chatIndex]
                                                ?.chatName
                                                ? allUsersChatsRes?.[
                                                      chatIndex
                                                  ].chatName.includes("@")
                                                    ? allUsersChatsRes?.[
                                                          chatIndex
                                                      ].chatName.split("@")[0]
                                                    : allUsersChatsRes?.[
                                                          chatIndex
                                                      ].chatName
                                                : "-"}
                                        </div> */}
                                            <div
                                                className={`${roboto.className} text-[16px] font-[400] text-[#000000]`}
                                            >
                                                {allUsersChatsRes &&
                                                allUsersChatsRes[chatIndex] &&
                                                allUsersChatsRes[chatIndex]
                                                    .chatName
                                                    ? allUsersChatsRes[
                                                          chatIndex
                                                      ].chatName.includes("@")
                                                        ? allUsersChatsRes[
                                                              chatIndex
                                                          ].chatName.split(
                                                              "@"
                                                          )[0]
                                                        : allUsersChatsRes[
                                                              chatIndex
                                                          ].chatName
                                                    : "-"}
                                            </div>
                                        </div>
                                        <hr className="custom-hr"></hr>
                                        <div className="overflow-y-scroll h-[61vh] scrollbar">
                                            <div className="flex flex-col space-y-2 py-3 px-5">
                                                {userChatsRes &&
                                                    userChatsRes?.length >
                                                        0 && (
                                                        <div className="w-full h-[calc(100%-80px)] pt-4 flex flex-col gap-2 overflow-y-auto scrollbar">
                                                            {userChatsRes.map(
                                                                (
                                                                    msg: any,
                                                                    index: any
                                                                ) => {
                                                                    return (
                                                                        <div
                                                                            key={
                                                                                index
                                                                            }
                                                                            className={`w-full flex ${
                                                                                msg.senderType !==
                                                                                "ADMIN"
                                                                                    ? "justify-start"
                                                                                    : "justify-end"
                                                                            } relative group`}
                                                                        >
                                                                            {selectedMessage &&
                                                                                msg.senderType ===
                                                                                    "ADMIN" &&
                                                                                selectedMessage.id ===
                                                                                    msg.id && (
                                                                                    <div className="absolute w-full h-full rounded-[14px] rounded-tr-none bg-[#67a1a375]"></div>
                                                                                )}
                                                                            <div
                                                                                className={`${
                                                                                    msg?.media &&
                                                                                    msg
                                                                                        .media
                                                                                        .type ===
                                                                                        "AUDIO"
                                                                                        ? "w-[90%] sm:w-[80%] md:w-[60%] lg:w-[30%]"
                                                                                        : "max-w-[90%] sm:max-w-[80%] md:max-w-[60%] lg:max-w-[40%] p-3"
                                                                                } ${
                                                                                    selectedMessage &&
                                                                                    msg.senderType ===
                                                                                        "ADMIN" &&
                                                                                    selectedMessage.id ===
                                                                                        msg.id &&
                                                                                    "my-1"
                                                                                } mx-1 rounded-[10px] flex flex-col gap-2 ${
                                                                                    msg.senderType !==
                                                                                    "ADMIN"
                                                                                        ? "bg-[#F4F4F4] text-[#000000] rounded-tl-none"
                                                                                        : "bg-[#67A1A3] text-[#FFFFFF] rounded-tr-none"
                                                                                } ${
                                                                                    msg.senderType ===
                                                                                        "ADMIN" &&
                                                                                    "cursor-pointer"
                                                                                } relative mr-5`}
                                                                            >
                                                                                {msg?.media && (
                                                                                    <>
                                                                                        {msg
                                                                                            .media
                                                                                            .type ===
                                                                                            "IMAGE" && (
                                                                                            <img
                                                                                                src={
                                                                                                    msg
                                                                                                        ?.media
                                                                                                        ?.content !==
                                                                                                    "string"
                                                                                                        ? msg
                                                                                                              ?.media
                                                                                                              ?.content
                                                                                                        : "/images/Logo_2.svg"
                                                                                                }
                                                                                                width={
                                                                                                    500
                                                                                                }
                                                                                                height={
                                                                                                    238
                                                                                                }
                                                                                                alt="school image"
                                                                                                className={`max-w-full h-[200px] object-contain rounded-[8px] ${
                                                                                                    msg.senderType !==
                                                                                                    "ADMIN"
                                                                                                        ? "bg-white"
                                                                                                        : "bg-[#90bec0]"
                                                                                                }`}
                                                                                            />
                                                                                        )}
                                                                                        {msg
                                                                                            .media
                                                                                            .type ===
                                                                                            "VIDEO" && (
                                                                                            <video
                                                                                                src={
                                                                                                    msg
                                                                                                        .media
                                                                                                        .content
                                                                                                }
                                                                                                width={
                                                                                                    500
                                                                                                }
                                                                                                height={
                                                                                                    238
                                                                                                }
                                                                                                controls
                                                                                                className="max-w-full h-[200px] object-cover rounded-[8px]"
                                                                                            />
                                                                                        )}
                                                                                    </>
                                                                                )}
                                                                                {msg?.message && (
                                                                                    <p className="text-[14px] leading-[19.6px] font-[400]">
                                                                                        {
                                                                                            msg.message
                                                                                        }
                                                                                    </p>
                                                                                )}
                                                                                {msg?.media &&
                                                                                    msg
                                                                                        .media
                                                                                        .type ===
                                                                                        "AUDIO" && (
                                                                                        <div className="w-full text-[14px] leading-[19.6px] font-[400]">
                                                                                            <Recorder
                                                                                                url={
                                                                                                    msg
                                                                                                        ?.media
                                                                                                        ?.content
                                                                                                }
                                                                                                bgColor={
                                                                                                    msg.senderType !==
                                                                                                    "ADMIN"
                                                                                                        ? "#F4F4F4"
                                                                                                        : "#67A1A3"
                                                                                                }
                                                                                            />
                                                                                        </div>
                                                                                    )}
                                                                                <p
                                                                                    className={`w-full flex text-[10px] leading-[11.72px] font-[400] justify-end ${
                                                                                        msg?.media &&
                                                                                        msg
                                                                                            .media
                                                                                            .type ===
                                                                                            "AUDIO" &&
                                                                                        "pb-2.5 pr-3"
                                                                                    }`}
                                                                                >
                                                                                    {formatTime(
                                                                                        msg.createdAt
                                                                                    )}
                                                                                </p>
                                                                            </div>
                                                                            {msg.senderType ===
                                                                                "ADMIN" && (
                                                                                <div
                                                                                    className="absolute right-0 hidden group-hover:flex cursor-pointer"
                                                                                    // onClick={() =>
                                                                                    //     handleMessage(
                                                                                    //         msg,
                                                                                    //         index
                                                                                    //     )
                                                                                    // }
                                                                                >
                                                                                    <Dropdown
                                                                                        menu={{
                                                                                            items: menue(
                                                                                                msg,
                                                                                                index
                                                                                            ),
                                                                                        }}
                                                                                        trigger={[
                                                                                            "click",
                                                                                        ]}
                                                                                        placement="bottomRight"
                                                                                    >
                                                                                        <div className="w-[16px] h-[16px] flex justify-center items-center rounded-full hover:bg-gray-100 opacity-70">
                                                                                            <svg
                                                                                                width="21"
                                                                                                height="20"
                                                                                                viewBox="0 0 21 20"
                                                                                                fill="none"
                                                                                                xmlns="http://www.w3.org/2000/svg"
                                                                                            >
                                                                                                <path
                                                                                                    d="M11.3346 9.99967C11.3346 9.53944 10.9615 9.16634 10.5013 9.16634C10.0411 9.16634 9.66797 9.53944 9.66797 9.99967C9.66797 10.4599 10.0411 10.833 10.5013 10.833C10.9615 10.833 11.3346 10.4599 11.3346 9.99967Z"
                                                                                                    stroke="black"
                                                                                                    stroke-width="2"
                                                                                                    stroke-linecap="round"
                                                                                                    stroke-linejoin="round"
                                                                                                />
                                                                                                <path
                                                                                                    d="M11.3346 4.16667C11.3346 3.70643 10.9615 3.33333 10.5013 3.33333C10.0411 3.33333 9.66797 3.70643 9.66797 4.16667C9.66797 4.6269 10.0411 5 10.5013 5C10.9615 5 11.3346 4.6269 11.3346 4.16667Z"
                                                                                                    stroke="black"
                                                                                                    stroke-width="2"
                                                                                                    stroke-linecap="round"
                                                                                                    stroke-linejoin="round"
                                                                                                />
                                                                                                <path
                                                                                                    d="M11.3346 15.8337C11.3346 15.3734 10.9615 15.0003 10.5013 15.0003C10.0411 15.0003 9.66797 15.3734 9.66797 15.8337C9.66797 16.2939 10.0411 16.667 10.5013 16.667C10.9615 16.667 11.3346 16.2939 11.3346 15.8337Z"
                                                                                                    stroke="black"
                                                                                                    stroke-width="2"
                                                                                                    stroke-linecap="round"
                                                                                                    stroke-linejoin="round"
                                                                                                />
                                                                                            </svg>
                                                                                        </div>
                                                                                    </Dropdown>
                                                                                </div>
                                                                            )}
                                                                        </div>
                                                                    );
                                                                }
                                                            )}
                                                            <div
                                                                ref={bottomRef}
                                                            ></div>
                                                        </div>
                                                    )}
                                                {userChatsRes?.length === 0 && (
                                                    <div className="w-full h-[60vh] flex flex-col justify-center items-center">
                                                        <Image
                                                            src="/images/mails/emptyMail.svg"
                                                            alt="search"
                                                            width={56}
                                                            height={56}
                                                            className="ml-2 w-[152px] h-[80px]"
                                                        />
                                                        <div
                                                            className={`text-[16px] font-[500] text-[#1E1E1E] ${roboto.className}`}
                                                        >
                                                            {t(
                                                                "You have no chat with this user"
                                                            )}
                                                        </div>
                                                        <div
                                                            className={`text-[14px] font-[400] text-[#8E9292] ${roboto.className}`}
                                                        >
                                                            {t(
                                                                "Send a mails to the user"
                                                            )}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        {/* Bottom send message fields */}
                                        <div className="absolute bottom-0 w-[57%] px-5 py-4">
                                            <div className="w-full">
                                                {!isRecordingOpen &&
                                                    mediaFile !== null && (
                                                        <div className="w-full h-[200px] pt-3 px-3 bg-[#F4F4F4] rounded-t-[10px]">
                                                            <div className="h-full w-[100%] sm:w-[60%] md:lg-[50%] lg:w-[40%] rounded-[6px] relative">
                                                                {mediaType ===
                                                                    "IMAGE" && (
                                                                    <Image
                                                                        src={
                                                                            mediaFile
                                                                        }
                                                                        width={
                                                                            342
                                                                        }
                                                                        height={
                                                                            238
                                                                        }
                                                                        alt="school image"
                                                                        className="w-full h-full object-cover rounded-[6px] bg-white"
                                                                    />
                                                                )}
                                                                {mediaType ===
                                                                    "VIDEO" && (
                                                                    <video
                                                                        src={
                                                                            mediaFile
                                                                        }
                                                                        width={
                                                                            342
                                                                        }
                                                                        height={
                                                                            238
                                                                        }
                                                                        className="w-full h-full object-cover rounded-[6px] bg-white"
                                                                    />
                                                                )}
                                                                <button
                                                                    className="bg-[#87c4c6] text-gray-900 hover:bg-[#67A1A3] transition-all rounded-lg text-sm w-6 h-6 ms-auto inline-flex justify-center items-center absolute -top-2 -right-2"
                                                                    onClick={() => {
                                                                        setMediaFile(
                                                                            null
                                                                        );
                                                                        setBinaryMedia(
                                                                            null
                                                                        );
                                                                    }}
                                                                    aria-label="Close"
                                                                    title="Close"
                                                                >
                                                                    <svg
                                                                        className="w-2 h-2"
                                                                        aria-hidden="true"
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        fill="none"
                                                                        viewBox="0 0 14 14"
                                                                    >
                                                                        <path
                                                                            stroke="currentColor"
                                                                            strokeLinecap="round"
                                                                            strokeLinejoin="round"
                                                                            strokeWidth="2"
                                                                            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                                                                        />
                                                                    </svg>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    )}
                                                {!isRecordingOpen ? (
                                                    <Input
                                                        size="large"
                                                        placeholder={t("type")}
                                                        style={{
                                                            backgroundColor:
                                                                "#F4F4F4",
                                                        }}
                                                        value={messageText}
                                                        onChange={(e) =>
                                                            setMessageText(
                                                                e.target.value
                                                            )
                                                        }
                                                        onKeyDown={
                                                            handleEnterKeyPress
                                                        }
                                                        className={`bg-[#F4F4F4] py-3 border-none hover:bg-[#F4F4F4] focus:bg-[#F4F4F4] rounded-[10px] ${
                                                            mediaFile !==
                                                                null &&
                                                            "rounded-t-none"
                                                        }`}
                                                        disabled={sendLoading}
                                                        suffix={
                                                            <div className="flex gap-3 mr-3">
                                                                {!isEditing ? (
                                                                    <>
                                                                        <Button
                                                                            style={{
                                                                                backgroundColor:
                                                                                    "transparent",
                                                                            }}
                                                                            className="cursor-pointer border-none p-0"
                                                                            onClick={() =>
                                                                                inputRef.current.click()
                                                                            }
                                                                        >
                                                                            <Image
                                                                                src="/images/mails/camera.svg"
                                                                                width={
                                                                                    24
                                                                                }
                                                                                height={
                                                                                    24
                                                                                }
                                                                                alt=""
                                                                            />
                                                                            <input
                                                                                ref={
                                                                                    inputRef
                                                                                }
                                                                                type="file"
                                                                                className="hidden"
                                                                                onChange={(
                                                                                    e
                                                                                ) =>
                                                                                    handleImage(
                                                                                        e
                                                                                    )
                                                                                }
                                                                            />
                                                                        </Button>
                                                                        <Button
                                                                            style={{
                                                                                backgroundColor:
                                                                                    "transparent",
                                                                            }}
                                                                            className="cursor-pointer border-none p-0"
                                                                            onClick={() => {
                                                                                setMediaFile(
                                                                                    null
                                                                                );
                                                                                setBinaryMedia(
                                                                                    null
                                                                                );
                                                                                setMessageText(
                                                                                    ""
                                                                                );
                                                                                setIsRecordingOpen(
                                                                                    true
                                                                                );
                                                                            }}
                                                                        >
                                                                            <Image
                                                                                src="/images/mails/microphone.svg"
                                                                                width={
                                                                                    24
                                                                                }
                                                                                height={
                                                                                    24
                                                                                }
                                                                                alt=""
                                                                            />
                                                                        </Button>
                                                                    </>
                                                                ) : (
                                                                    <Button
                                                                        style={{
                                                                            backgroundColor:
                                                                                "transparent",
                                                                        }}
                                                                        className="cursor-pointer border-none p-0"
                                                                        onClick={() => {
                                                                            hendleCancelEditMessage();
                                                                        }}
                                                                    >
                                                                        <Image
                                                                            src="/images/cross.svg"
                                                                            width={
                                                                                24
                                                                            }
                                                                            height={
                                                                                24
                                                                            }
                                                                            alt=""
                                                                        />
                                                                    </Button>
                                                                )}
                                                                <Button
                                                                    style={{
                                                                        backgroundColor:
                                                                            "transparent",
                                                                    }}
                                                                    className="cursor-pointer border-none p-0"
                                                                    loading={
                                                                        sendLoading
                                                                    }
                                                                    onClick={() => {
                                                                        if (
                                                                            isEditing
                                                                        ) {
                                                                            handleEditMsg();
                                                                        } else {
                                                                            handleSendMessage();
                                                                        }
                                                                    }}
                                                                >
                                                                    <Image
                                                                        src="/images/mails/send.svg"
                                                                        width={
                                                                            24
                                                                        }
                                                                        height={
                                                                            24
                                                                        }
                                                                        alt=""
                                                                    />
                                                                </Button>
                                                            </div>
                                                        }
                                                    />
                                                ) : (
                                                    <div className="w-full bg-[#F4F4F4] py-3 px-4 rounded-[10px] flex flex-col gap-2">
                                                        <div className="w-full">
                                                            {audioBlob && (
                                                                <Recorder
                                                                    url={
                                                                        audioUrl
                                                                    }
                                                                />
                                                            )}
                                                        </div>
                                                        <div className="flex justify-between items-end">
                                                            <Button
                                                                style={{
                                                                    backgroundColor:
                                                                        "transparent",
                                                                }}
                                                                className="cursor-pointer border-none p-0"
                                                                disabled={
                                                                    sendLoading
                                                                }
                                                                onClick={() => {
                                                                    setAudioBlob(
                                                                        null
                                                                    );
                                                                    setAudioUrl(
                                                                        null
                                                                    );
                                                                    setIsRecordingOpen(
                                                                        false
                                                                    );
                                                                    setIsRecordingStoped(
                                                                        false
                                                                    );
                                                                    setRecordingTime(
                                                                        0
                                                                    );
                                                                    if (
                                                                        isRecording
                                                                    )
                                                                        toggleRecording();
                                                                }}
                                                            >
                                                                <Image
                                                                    src="/images/mails/trash.svg"
                                                                    width={24}
                                                                    height={24}
                                                                    alt=""
                                                                />
                                                            </Button>
                                                            {!isRecordingStoped && (
                                                                <div
                                                                    title="Start recording"
                                                                    className="cursor-pointer flex flex-col items-center gap-3"
                                                                    onClick={
                                                                        toggleRecording
                                                                    }
                                                                >
                                                                    <p className="text-[14px] leading-[11.72px] font-[400]">
                                                                        {formatRecordingTime(
                                                                            recordingTime
                                                                        )}
                                                                    </p>
                                                                    <Image
                                                                        src={
                                                                            isRecording
                                                                                ? "/images/mails/pause.svg"
                                                                                : "/images/mails/microphone.svg"
                                                                        }
                                                                        width={
                                                                            24
                                                                        }
                                                                        height={
                                                                            24
                                                                        }
                                                                        alt=""
                                                                    />
                                                                </div>
                                                            )}
                                                            <Button
                                                                style={{
                                                                    backgroundColor:
                                                                        "transparent",
                                                                }}
                                                                className="cursor-pointer border-none p-0"
                                                                loading={
                                                                    sendLoading
                                                                }
                                                                onClick={
                                                                    handleSendMessage
                                                                }
                                                            >
                                                                <Image
                                                                    src="/images/mails/send.svg"
                                                                    width={24}
                                                                    height={24}
                                                                    alt=""
                                                                />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        )}
                    </>
                )}
            </div>
            {isDeleteModalOpen && (
                <DeleteModal
                    setIsModalOpen={setIsDeleteModalOpen}
                    setMessage={setSelectedMessage}
                    deleteMsg={handleDeleteMsg}
                    loading={loading}
                />
            )}
        </div>
    );
};

export default Mails;

import { getApps, initializeApp } from "firebase/app";
import { getAuth, onAuthStateChanged, User } from "firebase/auth";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import { getAnalytics, isSupported } from "firebase/analytics";

const firebaseConfig = {
    apiKey: "AIzaSyD51wSkS8IOe8uuYaa2loHlIJk6tUa1u7A",
    authDomain: "gymi-a1511.firebaseapp.com",
    projectId: "gymi-a1511",
    storageBucket: "gymi-a1511.appspot.com",
    messagingSenderId: "910525138413",
    appId: "1:910525138413:web:5e31a1fdde6ed7476a93e4",
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

const refreshToken = async (): Promise<string> => {
    return new Promise((resolve, reject) => {
        const user = auth.currentUser;
        if (user) {
            user.getIdToken(true)
                .then((token) => {
                    resolve(token);
                })
                .catch((error) => {
                    reject(error);
                });
        } else {
            reject(new Error("No user is currently signed in."));
        }
    });
};

let messaging: any = null;
if (typeof window !== "undefined") {
    if ("Notification" in window && "serviceWorker" in navigator) {
        try {
            messaging = getMessaging(app);
        } catch (error) {
            console.error("Error initializing Firebase Messaging:", error);
        }
    } else {
        console.warn(
            "Notifications or Service Workers are not supported in this browser."
        );
    }
}

const getFCMToken = async (): Promise<string> => {
    return new Promise(async (resolve, reject) => {
        try {
            if (typeof window !== "undefined" && "Notification" in window) {
                let permission = Notification.permission;

                while (permission !== "granted") {
                    permission = await Notification.requestPermission();

                    if (permission === "granted") {
                        break;
                    } else if (permission === "denied") {
                        reject(
                            new Error(
                                "Notification permission denied by the user"
                            )
                        );
                        return;
                    }
                }

                const currentToken = await getToken(messaging, {
                    vapidKey:
                        "BDKWH47oaDR3iXaPk0QuzRF-1TKUsJxj-HHIalh3rEXaPQKxW-O-V8ZRPTUS6VRaAyXQZ7-ltU_l2T-c9zRhcpw",
                });

                if (currentToken) {
                    resolve(currentToken);
                } else {
                    reject(new Error("No registration token available."));
                }
            }
        } catch (error) {
            console.error("An error occurred while retrieving token:", error);
            reject(error);
        }
    });
};

if (typeof window !== "undefined") {
    onMessage(messaging, (payload) => {
        const notificationTitle = payload.notification?.title || "Notification";
        const notificationOptions = {
            body: payload.notification?.body,
            icon: payload.notification?.icon,
        };
        if (Notification.permission === "granted") {
            new Notification(notificationTitle, notificationOptions);
        } else {
            console.warn("Notification permission is not granted.");
        }

        // Send message to React component (if using a shared state)
        window.dispatchEvent(
            new CustomEvent("new-notification", { detail: payload })
        );
    });
}

let analytics;
const initFirebase = async () => {
    if (!getApps().length) {
        const app = initializeApp(firebaseConfig);

        if (typeof window !== "undefined") {
            const analyticsSupported = await isSupported();
            if (analyticsSupported) {
                analytics = getAnalytics(app);
            }
        }
    }
};

export { auth, refreshToken, app, getFCMToken, messaging, initFirebase };

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { auth, refreshToken } from "@/firebase.config";
import { onAuthStateChanged } from "firebase/auth";

let axiosInstance: AxiosInstance | null = null;
let authReadyPromise: Promise<void> | null = null;
let tokenRefreshPromise: Promise<string> | null = null;

// Wait for Firebase to restore the authentication state
const waitForAuthState = (): Promise<void> => {
    if (!authReadyPromise) {
        authReadyPromise = new Promise((resolve) => {
            onAuthStateChanged(auth, () => {
                resolve(); // Firebase has finished restoring the session
            });
        });
    }
    return authReadyPromise;
};

// Redirect to login page and clear storage
const redirectToLogin = () => {
    localStorage.clear();
    sessionStorage.clear();
    window.location.href = '/'; // Redirect to login page
};

// Get a fresh token, with deduplication of simultaneous requests
const getIdToken = async (forceRefresh = false): Promise<string> => {
    if (!auth.currentUser) {
        redirectToLogin();
        throw new Error("User is not authenticated");
    }
    
    // If we're already refreshing a token, return that promise
    if (forceRefresh && tokenRefreshPromise) {
        return tokenRefreshPromise;
    }
    
    try {
        if (forceRefresh) {
            // Create a new promise for token refresh
            tokenRefreshPromise = auth.currentUser.getIdToken(true);
            const token = await tokenRefreshPromise;
            return token;
        } else {
            return await auth.currentUser.getIdToken(false);
        }
    } finally {
        if (forceRefresh) {
            tokenRefreshPromise = null;
        }
    }
};

const API = async (forceNew = false): Promise<AxiosInstance> => {
    if (axiosInstance && !forceNew) {
        return axiosInstance;
    }
    
    // Wait for Firebase to restore auth state
    await waitForAuthState();
    
    axiosInstance = axios.create({
        baseURL: `${process.env.NEXT_API_ENDPOINT}`,
        headers: {
            Authorization: `${localStorage.getItem("idToken") || ""}`,
            userplatform: "WEB",
        },
        timeout: 30000, // 30 second timeout
    });
    
    return axiosInstance;
};

const fetch = async <T>(config: AxiosRequestConfig): Promise<T> => {
    // Wait for auth to be ready
    await waitForAuthState();
    
    try {
        // Get API instance after auth is ready
        const apiInstance = await API();
        
        // Ensure we have the latest token in the request
        if (auth.currentUser) {
            try {
                const currentToken = await getIdToken(false);
                config.headers = {
                    ...config.headers,
                    Authorization: `Bearer ${currentToken}`,
                    userplatform: "WEB",
                };
                // Update localStorage with the current token
                localStorage.setItem("idToken", `Bearer ${currentToken}`);
            } catch (tokenError) {
                console.warn("Could not get current token:", tokenError);
                redirectToLogin();
                throw tokenError;
            }
        }
        
        const response: AxiosResponse<T> = await apiInstance.request<T>(config);
        return response.data;
    } catch (error: any) {
        if (error.response?.status === 401) {
            try {
                // Force refresh the token
                const newToken = await getIdToken(true);
                localStorage.setItem("idToken", `Bearer ${newToken}`);
                
                // Force create a new axios instance
                axiosInstance = null;
                
                config.headers = {
                    ...config.headers,
                    Authorization: `Bearer ${newToken}`,
                    userplatform: "WEB",
                };
                
                // Get a fresh API instance and retry the request
                const freshApiInstance = await API(true);
                const response: AxiosResponse<T> = await freshApiInstance.request<T>(config);
                return response.data;
            } catch (refreshError: any) {
                // If token refresh fails, clear the instance to force a new one on next try
                axiosInstance = null;
                // Clear storage and redirect to login page
                redirectToLogin();
                throw new Error(
                    refreshError?.response?.data?.message ||
                    "Token refresh failed. Please log in again."
                );
            }
        } else {
            throw new Error(
                error?.response?.data?.message || "Bad response from server"
            );
        }
    }
};

export { API, fetch, waitForAuthState };


on:
    push:
      branches:
        - master
concurrency:
    group: master
    cancel-in-progress: true
name: 🚀 Deploy master to DigitalOcean Server
jobs:
    deploy:
      name: 🎉 Deploy
      runs-on: ubuntu-latest
      steps:
      - name: 🚚 SSH Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PRODUCTION_SERVER_HOST }}
          username: ${{ secrets.PRODUCTION_SERVER_USER }}
          key: ${{ secrets.PRODUCTION_SERVER_KEY }}
          port: ${{ secrets.PRODUCTION_SERVER_PORT }}
          script: |
              cd /var/www/gymi-admin-react-web/
              git reset --hard
              git checkout master
              git pull origin master
              yarn
              yarn build
              pm2 restart "gymi-admin"
              pm2 save
#start new proccess: sudo pm2 start npx --name "gymi-admin"

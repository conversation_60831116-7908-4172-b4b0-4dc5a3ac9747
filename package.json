{"name": "gymi-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 1465", "build": "next build", "start": "next start -p 1465", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/material": "^5.16.4", "@wavesurfer/react": "^1.0.7", "antd": "^5.18.2", "axios": "^1.7.2", "firebase": "^10.12.2", "i18next": "^23.16.2", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "14.2.3", "next-i18next": "^15.3.1", "react": "^18", "react-dom": "^18", "react-i18next": "^15.0.3", "react-icons": "^5.2.1", "react-quill": "^2.0.0", "socket.io-client": "^4.8.0", "wavesurfer.js": "^7.8.6"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}
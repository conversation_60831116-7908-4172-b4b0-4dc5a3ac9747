import React from "react";
import Image from "next/image";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import { useRouter } from "next/navigation";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const Footer = () => {
    const router = useRouter();

    const handleClickForLandingPage = () => {
        router.push("/");
    };

    return (
        <div className="bg-[url('/images/landingPage/curvedBg.svg')] bg-cover flex flex-col md:flex-row justify-between items-center h-auto md:h-[365px] w-full p-4 pb-10">
            <div className="w-full md:w-1/2 flex flex-col justify-center items-center mb-10 md:mb-0">
                <div className="flex flex-col items-center pt-14 md:items-start">
                    <div
                        className="flex gap-3 items-center justify-center cursor-pointer hover:scale-[1.1] transition-all duration-500"
                        // onClick={handleClickForLandingPage}
                    >
                        <Image
                            src="/images/Logo_2.svg"
                            alt="login"
                            width={60}
                            height={60}
                            className="shadowed-element rounded-full"
                        />
                        <h1 className="text-[#004F53] text-opacity-80 text-[34px] font-bold">
                            Gymi
                        </h1>
                    </div>
                    <div className="flex gap-3 items-center pt-10">
                        <Image
                            src="/images/landingPage/buildings.svg"
                            alt="login"
                            width={25}
                            height={25}
                        />
                        <h1
                            className={`text-[#004F53] text-opacity-80 text-[16px] font-[400] ${roboto.className}`}
                        >
                            GYMi Media OG
                        </h1>
                    </div>
                    <div className="flex gap-3 items-center pt-1">
                        <Image
                            src="/images/landingPage/mapIcon.svg"
                            alt="login"
                            width={25}
                            height={25}
                        />
                        <h1
                            className={`text-[#004F53] text-opacity-80 text-[16px] font-[400] ${roboto.className}`}
                        >
                            Feldgasse 2, 7131 Halbturn
                        </h1>
                    </div>
                    <div className="flex gap-4 items-center pt-1">
                        <Image
                            src="/images/landingPage/Message.svg"
                            alt="login"
                            width={20}
                            height={20}
                            className="ml-[2px]"
                        />
                        <h1
                            className={`text-[#004F53] text-opacity-80 text-[16px] font-[400] ${roboto.className}`}
                        >
                            <EMAIL>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Footer;

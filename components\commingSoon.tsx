"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";

const CommingSoon = () => {
    const [dots, setDots] = useState("");

    useEffect(() => {
        const interval = setInterval(() => {
            setDots((prevDots) => (prevDots.length >= 4 ? "" : prevDots + "."));
        }, 500);

        return () => clearInterval(interval);
    }, []);
    return (
        <>
            <div className="">
                <div className="h-screen bg-black">
                    <div className="opacity-20 ">
                        <Image
                            src="/images/image.jpg"
                            alt="gymi logo"
                            layout="fill"
                            objectFit="cover"
                        />
                    </div>
                    <div className="flex pt-[50px] pl-[50px] cursor-pointer">
                        <Image
                            src="/images/Gymi logo.svg"
                            alt="gymi logo"
                            width={105.45}
                            height={105.45}
                            className="cursor-pointer"
                        />
                        <p className="text-[75.21px] font-roboto font-semibold ml-[10px] cursor-pointer">
                            <link
                                href="https://fonts.googleapis.com/css2?family=Roboto&display=swap"
                                rel="stylesheet"
                            />{" "}
                            Gymi{" "}
                        </p>
                    </div>
                    <div className="flex justify-center">
                        <div className="mt-[20px] ml-[50px]">
                            <p className="text-[100px] font-semibold">
                                {" "}
                                Coming Soon{dots}{" "}
                            </p>
                            <p className="text-[30px]">
                                We are excited to announce that our latest
                                project is on
                            </p>
                            <p className="text-[30px]">
                                the horizon. Stay tuned for the official
                                release!
                            </p>
                            {/* <Link href="/login"> */}
                            <div className="relative flex mt-[70px] text-[20px] items-center justify-center">
                                <Image
                                    src="/images/Rectangle 50.svg"
                                    alt="Contact_Us"
                                    width={241}
                                    height={50}
                                    className="cursor-pointer"
                                />
                                <div className="absolute font-roboto">
                                    <link
                                        href="https://fonts.googleapis.com/css2?family=Roboto&display=swap"
                                        rel="stylesheet"
                                    />
                                    <p className="text-center text-[20px] cursor-pointer">
                                        Contact Us
                                    </p>
                                </div>
                            </div>
                            {/* </Link> */}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default CommingSoon;

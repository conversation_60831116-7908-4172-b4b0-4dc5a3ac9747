"use client";
import React, { useState, useEffect, useRef } from "react";
import { Select, Input, Button, Spin, message, Modal } from "antd";
import SideBar from "@/components/sideBar/sideBar";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import {
    Grade3rdLevel,
    Grade4thLevel,
    grades,
    forthGradeSubjects,
    thirdGradeSubjects,
    practiceTypes,
    questionTypes,
    englishCategories,
    germanCategories,
    mathsCategories,
    answerTypes,
    gradeFirstEnglishCategories,
    gradeFirstMathsCategories,
    gradeFirstGermanCategories,
} from "@/src/libs/constants";
import TextArea from "antd/es/input/TextArea";
import { DeleteOutlined } from "@ant-design/icons";
import {
    quetionsDetail,
    editQuestion,
    addQuestion,
    getQuestion,
} from "@/src/services/knowledgeRally.api";
import {
    QueryParams,
    deleteUploadedFile,
    uploadFile,
} from "@/src/services/upload.api";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";
import { Roboto } from "next/font/google";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import {
    getAllGrade,
    getAllForTecherGrade,
} from "@/src/services/manage-grade.api";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const { Option } = Select;

const modules = {
    toolbar: [
        [{ header: "1" }, { header: "2" } /*{ 'font': [] }*/],
        [{ size: ["small", false, "large", "huge"] }],
        ["bold", "italic", "underline", "strike", "blockquote"],
        [
            { list: "ordered" },
            { list: "bullet" },
            { indent: "-1" },
            { indent: "+1" },
        ],
        ["link"],
        ["clean"],
    ],
};

const AddQuestion = () => {
    const [level, setLevel] = useState(
        localStorage.getItem("selectedLevel") || "LEVEL_1"
    );
    const [subject, setSubject] = useState(
        localStorage.getItem("selectedSubject") || "GERMAN"
    );
    const [grade, setGrade] = useState(
        localStorage.getItem("selectedGrade") || "THIRD"
    );
    const [practiceType, setPracticeType] = useState("NORMAL");
    const [questionType, setQuestionType] = useState("MCQ");
    const [category, setCategory] = useState("");
    const [serialNo, setSerialNo] = useState("");
    const [question, setQuestion] = useState<string | null>(null);
    const [answerType, setAnswerType] = useState("TEXT");
    const [correctAnswer, setCorrectAnswer] = useState<number | null>(null);
    const [levelOptions, setLevelOptions] = useState<
        { value: string; label: string }[]
    >([]);
    const [categoryOptions, setCategoryOptions] = useState<
        { value: string; label: string }[]
    >([]);
    const router = useRouter();
    const searchParams = useSearchParams();
    const [blanksCount, setBlanksCount] = useState<number>(0);
    const [answers, setAnswers] = useState<string[] | null>(null);
    // const [numAnswerFields, setNumAnswerFields] = useState(1);
    const [isloading, setIsLoading] = useState(true);
    const [loading, setLoading] = useState(false);
    const [editResponse, setEditResponse] = useState<any>();
    const [userType, setUserType] = useState(
        localStorage.getItem("userType") || "ADMIN"
    );
    const [isTableDataLoading, setIsTableDataLoading] = useState(false);
    const [gradeData, setGradeData] = useState<any>([]);
    const [disabledGrades, setDisabledGrades] = useState<string[]>([]);
    const [images, setImages] = useState<Array<string | undefined>>([
        undefined,
    ]);
    const [imageOptions, setImageOptions] = useState<
        {
            label: string;
            id: number;
            value: string | null;
            isCorrect?: boolean;
        }[]
    >([
        { label: "Option A", id: 1, value: null },
        { label: "Option B", id: 2, value: null },
        // { label: "Option c", id: 3, value: null },
        // { label: "Option d", id: 4, value: null },
    ]);
    const [correctImageAnswer, setCorrectImageAnswer] = useState<any>(null);
    const [questionMode, setQuestionMode] = useState<any>(null);
    const { t } = useTranslation();
    const audioRef = useRef<HTMLAudioElement>(null);

    useEffect(() => {
        setTimeout(() => {
            setIsLoading(false);
        }, 2000);
        fetchGradeStatusData();
    }, []);

    useEffect(() => {
        const mode: any = localStorage.getItem("mode");
        if (mode || searchParams.get("mode")) {
            const operationMode = mode ? mode : searchParams.get("mode");
            setQuestionMode(operationMode);
        }
    }, []);

    useEffect(() => {
        if (typeof window !== "undefined") {
            const session = localStorage.getItem("session");
            const userType = localStorage.getItem("userType");
            if (!session) {
                router.push("/");
            }
            if (userType) {
                setUserType(userType);
            }
        }
    }, [router]);

    const handleOptionChange = (index: any, value: any) => {
        const newOptions = [...options];
        newOptions[index].value = value;
        setOptions(newOptions);
    };

    const addOption = () => {
        if (options.length < 10) {
            const newOptionLabel = `Option ${String.fromCharCode(
                65 + options.length
            )}`;
            setOptions([...options, { label: newOptionLabel, value: "" }]);
        }
    };

    const deleteOption = (index: any) => {
        const newOptions = options.filter((_, i) => i !== index);
        const updatedOptions = newOptions.map((option, i) => ({
            ...option,
            label: `Option ${String.fromCharCode(65 + i)}`,
        }));
        setOptions(updatedOptions);
    };

    useEffect(() => {
        if (grade === "THIRD" || grade === "FIRST" || grade === "SECOND") {
            setLevelOptions(Grade3rdLevel);
            // setSubject("GERMAN");
            if (
                level === "LEVEL_7" ||
                level === "LEVEL_8" ||
                level === "LEVEL_9"
            )
                setLevel("LEVEL_1");
            setPracticeType("NORMAL");
        } else if (grade === "FOURTH") {
            setLevelOptions(Grade4thLevel);
            // setSubject("GERMAN");
            // setLevel("LEVEL_1");
        } else {
            setLevelOptions([]);
        }
    }, [grade]);

    useEffect(() => {
        if (grade === "FIRST" || grade === "SECOND") {
            if (subject === "ENGLISH") {
                setCategoryOptions(gradeFirstEnglishCategories);
                setCategory("GRAMMAR");
            } else if (subject === "MATHS") {
                setCategoryOptions(gradeFirstMathsCategories);
                setCategory("GEOMETRY");
            } else if (subject === "GERMAN") {
                setCategoryOptions(gradeFirstGermanCategories);
                setCategory("GRAMMAR");
            } else {
                setCategoryOptions([]);
            }
            setPracticeType("NORMAL");
        } else {
            if (subject === "ENGLISH") {
                setCategoryOptions(englishCategories);
                setCategory("GRAMMAR");
            } else if (subject === "MATHS") {
                setCategoryOptions(mathsCategories);
                setCategory("GEOMETRY");
            } else if (subject === "GERMAN") {
                setCategoryOptions(germanCategories);
                setCategory("GRAMMAR");
            } else {
                setCategoryOptions([]);
            }
        }
    }, [subject, grade]);

    useEffect(() => {
        if (editResponse) {
            if (grade === "FIRST" || grade === "SECOND") {
                if (subject === "ENGLISH") {
                    setCategoryOptions(gradeFirstEnglishCategories);
                    setCategory(editResponse.category);
                } else if (subject === "MATHS") {
                    setCategoryOptions(gradeFirstMathsCategories);
                    setCategory(editResponse.category);
                } else if (subject === "GERMAN") {
                    setCategoryOptions(gradeFirstGermanCategories);
                    setCategory(editResponse.category);
                } else {
                    setCategoryOptions([]);
                }
                setPracticeType("NORMAL");
            } else {
                if (subject === "ENGLISH") {
                    setCategoryOptions(englishCategories);
                    setCategory(editResponse.category);
                } else if (subject === "MATHS") {
                    setCategoryOptions(mathsCategories);
                    setCategory(editResponse.category);
                } else if (subject === "GERMAN") {
                    setCategoryOptions(germanCategories);
                    setCategory(editResponse.category);
                } else {
                    setCategoryOptions([]);
                }
            }
        }
    }, [editResponse, grade]);

    useEffect(() => {
        const countBlanks = (text: string) => {
            const underscoreBlanks = (text.match(/_{3,}/g) || []).length;
            const bracketBlanks = (text.match(/\[BLANK\]/g) || []).length;
            return underscoreBlanks + bracketBlanks;
        };

        if (question !== null) {
            const count = countBlanks(question);
            setBlanksCount(count);
            if (answers === null) {
                setAnswers(Array(count).fill(""));
            }
        }
    }, [question]);

    const handleAnswerChange = (index: number, value: string) => {
        if (answers !== null) {
            const newAnswers = [...answers];
            newAnswers[index] = value;
            setAnswers(newAnswers);
        }
    };

    const [isUploading, setIsUploading] = useState<boolean[]>(
        new Array(images.length).fill(false)
    );

    const handleMediaChange = async (
        e: React.ChangeEvent<HTMLInputElement>,
        index: number
    ) => {
        if (e.target.files && e.target.files.length) {
            const file = e.target.files[0];
            const reader = new FileReader();

            const newUploadingState = [...isUploading];
            newUploadingState[index] = true;
            setIsUploading(newUploadingState);

            reader.onload = async (event) => {
                if (event.target?.result) {
                    const trimmedFileName = file.name.replace(/\s+/g, "_");

                    // Determine file type based on MIME type
                    let fileType: "IMAGE" | "AUDIO" | "FILE";
                    if (file.type.startsWith("image/")) {
                        fileType = "IMAGE";
                    } else if (file.type.startsWith("audio/")) {
                        fileType = "AUDIO";
                    } else if (file.type === "application/pdf") {
                        fileType = "FILE";
                    } else {
                        // newUploadingState[index] = false;
                        // setIsUploading(newUploadingState);
                        setIsUploading((prev) => {
                            const updatedState = [...prev];
                            updatedState[index] = false;
                            return updatedState;
                        });
                        message.warning(
                            t("Only audio, PDF, and image files are supported")
                        );
                        return;
                    }

                    const queryParams: QueryParams = {
                        fileName: trimmedFileName,
                        fileType,
                        resourceType: "QUESTION",
                    };
                    const authorization = localStorage.getItem("idToken");

                    try {
                        const response = await uploadFile(
                            queryParams,
                            authorization
                        );

                        if (response && response.preSignedUrl) {
                            const url = response.preSignedUrl;
                            try {
                                await fetch(url, {
                                    method: "PUT",
                                    body: file,
                                    headers: {
                                        "Content-Type": file.type,
                                    },
                                });

                                const newMedia = [...images];
                                newMedia[index] = response.outPutUrl;
                                setImages(newMedia);

                                // Set loading state to false after upload
                                newUploadingState[index] = false;
                                setIsUploading(newUploadingState);
                            } catch (uploadError) {
                                message.error(t("Media upload failed"));
                                newUploadingState[index] = false;
                                setIsUploading(newUploadingState);
                            }
                        } else {
                            message.error(t("Invalid presigned URL response"));
                            newUploadingState[index] = false;
                            setIsUploading(newUploadingState);
                        }
                    } catch (apiError) {
                        message.error(t("Failed to get presigned URL"));
                        newUploadingState[index] = false;
                        setIsUploading(newUploadingState);
                    }
                }
            };
            reader.readAsDataURL(file);
        }
    };

    const addMore = () => {
        if (images.length < 4) {
            setImages([...images, undefined]);
        }
    };

    const deleteQuestionMedia = async (index: number) => {
        try {
            const mediaUrlToDelete = images[index];
            if (mediaUrlToDelete) {
                const fileName = mediaUrlToDelete.split("/").pop();

                let fileType: "IMAGE" | "AUDIO" | "FILE" = "IMAGE";
                if (mediaUrlToDelete.includes(".pdf")) {
                    fileType = "FILE";
                } else if (
                    mediaUrlToDelete.includes(".mp3") ||
                    mediaUrlToDelete.includes(".wav")
                ) {
                    fileType = "AUDIO";
                }

                const queryParams: QueryParams = {
                    fileName: fileName,
                    fileType,
                    resourceType: "QUESTION",
                };
                const authorization = localStorage.getItem("idToken");
                await deleteUploadedFile(queryParams, authorization);

                const newImages = [...images];
                newImages.splice(index, 1);
                setImages(newImages);
            } else if (images[index] === undefined) {
                const newImages = [...images];
                newImages.splice(index, 1);
                setImages(newImages);
            }
        } catch (error) {
            message.error(t("Error deleting media"));
        }
    };

    const handleImageOptionChange = async (
        e: React.ChangeEvent<HTMLInputElement>,
        index: number
    ) => {
        const file = e.target.files?.[0];
        if (file) {
            const trimmedFileName = file.name.replace(/\s+/g, "_");
            const queryParams: QueryParams = {
                fileName: trimmedFileName,
                fileType: "IMAGE",
                resourceType: "OPTION",
            };
            const authorization = localStorage.getItem("idToken");

            try {
                const response = await uploadFile(queryParams, authorization);

                if (response && response.preSignedUrl) {
                    const url = response.preSignedUrl;
                    try {
                        await fetch(url, {
                            method: "PUT",
                            body: file,
                            headers: {
                                "Content-Type": file.type,
                            },
                        });

                        const newImageOptions = [...imageOptions];
                        newImageOptions[index].value = response.outPutUrl;
                        setImageOptions(newImageOptions);
                    } catch (uploadError) {
                        message.error(t("Image upload failed"));
                    }
                } else {
                    message.error(t("Invalid presigned URL response"));
                }
            } catch (apiError) {
                message.error(t("Failed to get presigned URL"));
            }
        }
    };

    const addImageOption = () => {
        if (imageOptions.length < 10) {
            const newImageOption = {
                label: `Option ${String.fromCharCode(
                    65 + imageOptions.length
                )}`,
                id: imageOptions.length + 1,
                value: null,
            };

            const newImageOptions = [...imageOptions, newImageOption];
            setImageOptions(newImageOptions);
        }
    };

    const deleteOptionImage = async (index: number) => {
        const imageUrl = imageOptions[index].value;

        if (imageUrl) {
            const queryParams: QueryParams = {
                fileName: imageUrl.split("/").pop(),
                fileType: "IMAGE",
                resourceType: "QUESTION",
            };
            const authorization = localStorage.getItem("idToken");

            try {
                await deleteUploadedFile(queryParams, authorization);
                const newImageOptions = imageOptions
                    .filter((_, i) => i !== index)
                    .map((option, i) => ({
                        ...option,
                        label: `Option ${String.fromCharCode(65 + i)}`,
                        id: i + 1,
                    }));

                if (correctImageAnswer === imageOptions[index].label) {
                    setCorrectImageAnswer(null);
                } else {
                    const correctAnswerIndex = imageOptions.findIndex(
                        (option) => option.label === correctImageAnswer
                    );
                    if (
                        correctAnswerIndex !== -1 &&
                        correctAnswerIndex > index
                    ) {
                        setCorrectImageAnswer(
                            newImageOptions[correctAnswerIndex - 1].label
                        );
                    }
                }
                setImageOptions(newImageOptions);
            } catch (error) {
                message.error("Failed to delete image");
            }
        } else if (imageOptions[index].value === null) {
            const newImageOptions = imageOptions
                .filter((_, i) => i !== index)
                .map((option, i) => ({
                    ...option,
                    label: `Option ${String.fromCharCode(65 + i)}`,
                    id: i + 1,
                }));

            if (correctImageAnswer === imageOptions[index].label) {
                setCorrectImageAnswer(null);
            } else {
                const correctAnswerIndex = imageOptions.findIndex(
                    (option) => option.label === correctImageAnswer
                );
                if (correctAnswerIndex !== -1 && correctAnswerIndex > index) {
                    setCorrectImageAnswer(
                        newImageOptions[correctAnswerIndex - 1].label
                    );
                }
            }
            setImageOptions(newImageOptions);
        }
    };

    // Add New Question method
    const handleAddQuestion = async () => {
        setLoading(true);

        // Validate the question text
        if (question === null || question === "") {
            message.error(t("Question should not be empty"));
            setLoading(false);
            return;
        }

        // Validate MCQ-specific fields
        if (questionType === "MCQ") {
            if (answerType === "TEXT" && correctAnswer === null) {
                message.error(t("Correct answer should not be empty"));
                setLoading(false);
                return;
            } else if (answerType === "IMAGE" && correctImageAnswer === null) {
                message.error(t("Correct answer should not be empty"));
                setLoading(false);
                return;
            }

            // Trim and validate TEXT options
            if (answerType === "TEXT") {
                const trimmedOptions = options.map((opt) => ({
                    ...opt,
                    value: opt.value.trim(),
                }));

                // Check for empty options
                if (trimmedOptions.some((opt) => opt.value === "")) {
                    message.error("Optionen dürfen nicht leer sein");
                    setLoading(false);
                    return;
                }

                // Check for duplicate options
                const optionValues = trimmedOptions.map((opt) => opt.value);
                const hasDuplicates =
                    new Set(optionValues).size !== optionValues.length;

                if (hasDuplicates) {
                    message.error(t("Option values should not be the same"));
                    setLoading(false);
                    return;
                }

                // Update options with trimmed values
                setOptions(trimmedOptions);
            }
        } else if (questionType === "INPUT_BOX") {
            // Validate INPUT_BOX answers
            if (answers === null || answers.length === 0) {
                message.error(t("Add at least one blank"));
                setLoading(false);
                return;
            }

            // Check if we have fewer answers than blanks
            if (answers.length < blanksCount) {
                message.error(
                    "Die Anzahl der Antworten muss mindestens der Anzahl der Lücken entsprechen"
                );
                setLoading(false);
                return;
            }

            // Trim whitespace from all answers
            const trimmedAnswers = answers.map((answer) => answer.trim());

            // Take only the first N answers that match the blank count
            const answersToUse = trimmedAnswers.slice(0, blanksCount);

            // Check for empty answers in the answers we'll use
            if (answersToUse.some((answer) => answer === "")) {
                message.error("Antworten dürfen nicht leer sein");
                setLoading(false);
                return;
            }

            // Update answers with trimmed values (only the ones we need)
            setAnswers(answersToUse);
        }

        let formattedOptions = null;
        if (questionType === "MCQ" && answerType === "IMAGE") {
            formattedOptions = imageOptions.map((opt) => ({
                value: opt.value,
            }));
        } else if (questionType === "MCQ" && answerType === "TEXT") {
            formattedOptions = options.map((opt) => ({ value: opt.value }));
        }

        // Filter images, documents, and audio files separately
        const filteredImages = images.filter(
            (image) => image !== null && image !== undefined
        );
        const documentFiles = filteredImages.filter((file: any) =>
            file.includes(".pdf")
        );
        const audioFiles = filteredImages.filter(
            (file: any) => file.includes(".mp3") || file.includes(".wav")
        );
        const imageFiles = filteredImages.filter(
            (file: any) =>
                !file.includes(".pdf") &&
                !file.includes(".mp3") &&
                !file.includes(".wav")
        );

        // Prepare the payload
        const payload: quetionsDetail = {
            grade: grade,
            level: level,
            levelWiseNum: null,
            subject: subject,
            category: category,
            practiceType: practiceType,
            type: questionType,
            question: question,
            questionImage: imageFiles.length > 0,
            images: imageFiles,
            documentFiles: documentFiles,
            audioFiles: audioFiles,
            answerType: answerType,
            options: formattedOptions,
            correctAnswer:
                correctAnswer ||
                (correctImageAnswer ? parseInt(correctImageAnswer) : null),
            blanksCount: blanksCount || null,
            answers: answers,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await addQuestion(payload, authorization);
            if (response) {
                message.success(t("Question added successfully"));
                router.push("/knowledgeRally");
                localStorage.setItem("selectedGrade", grade);
                localStorage.setItem("selectedLevel", level);
                if (grade === "FIRST" || grade === "SECOND") {
                    localStorage.setItem("selectedSubject", subject);
                }
            }
        } catch (error: any) {
            if (error.message.includes("options.")) {
                message.error(t("Option field should not be empty"));
            } else {
                message.error(error.message || t("Failed to add Question"));
            }
        }
        setLoading(false);
    };

    // Edit Question method
    const handleSaveQuestion = async (questionId: string) => {
        setLoading(true);
        let formattedOptions = null;
        if (question === "") {
            message.error(t("Question should not be empty"));
            setLoading(false);
            return;
        }

        // Validate MCQ-specific fields
        if (questionType === "MCQ") {
            if (answerType === "TEXT" && correctAnswer === undefined) {
                message.error(t("Correct answer should not be empty"));
                setLoading(false);
                return;
            } else if (
                answerType === "IMAGE" &&
                correctImageAnswer === undefined
            ) {
                message.error(t("Correct answer should not be empty"));
                setLoading(false);
                return;
            }

            // Trim and validate TEXT options
            if (answerType === "TEXT") {
                const trimmedOptions = options.map((opt) => ({
                    ...opt,
                    value: opt.value.trim(),
                }));

                // Check for empty options
                if (trimmedOptions.some((opt) => opt.value === "")) {
                    message.error("Optionen dürfen nicht leer sein");
                    setLoading(false);
                    return;
                }

                // Check for duplicate options
                const optionValues = trimmedOptions.map((opt) => opt.value);
                const hasDuplicates =
                    new Set(optionValues).size !== optionValues.length;

                if (hasDuplicates) {
                    message.error(t("Option values should not be the same"));
                    setLoading(false);
                    return;
                }

                // Update options with trimmed values
                setOptions(trimmedOptions);
            }
        }

        // Prepare formatted options after validation
        if (questionType === "MCQ" && answerType === "IMAGE") {
            formattedOptions = imageOptions.map((opt) => ({
                value: opt.value,
            }));
        } else if (questionType === "MCQ" && answerType === "TEXT") {
            formattedOptions = options.map((opt) => ({ value: opt.value }));
        }

        let answersToUse;
        if (questionType === "INPUT_BOX") {
            if (answers === null || answers.length === 0) {
                message.error(t("Add atleast one blank"));
                setLoading(false);
                return;
            }

            // Trim whitespace from all answers
            const trimmedAnswers = answers.map((answer) => answer.trim());

            // Take only the first N answers that match the blank count
            answersToUse = trimmedAnswers.slice(0, blanksCount);

            // Check for empty answers in the answers we'll use
            if (answersToUse.some((answer) => answer === "")) {
                message.error("Antworten dürfen nicht leer sein");
                setLoading(false);
                return;
            }

            // Check if we have fewer answers than blanks
            if (answersToUse.length !== blanksCount) {
                message.error(
                    "Die Anzahl der Antworten muss mindestens der Anzahl der Lücken entsprechen"
                );
                setLoading(false);
                return;
            }

            // Update answers with trimmed values (only the ones we need)
            setAnswers(answersToUse);
        }

        const filteredImages = images.filter(
            (image) => image !== null && image !== undefined
        );
        const documentFiles = filteredImages.filter((file: any) =>
            file.includes(".pdf")
        );
        const audioFiles = filteredImages.filter(
            (file: any) => file.includes(".mp3") || file.includes(".wav")
        );
        const imageFiles = filteredImages.filter(
            (file: any) =>
                !file.includes(".pdf") &&
                !file.includes(".mp3") &&
                !file.includes(".wav")
        );

        const payload: quetionsDetail = {
            serialNum: parseInt(serialNo),
            grade: grade,
            level: level,
            levelWiseNum: null,
            subject: subject,
            category: category,
            practiceType: practiceType,
            type: questionType,
            question: question,
            questionImage: imageFiles.length > 0,
            images: imageFiles,
            documentFiles: documentFiles,
            audioFiles: audioFiles,
            answerType: answerType,
            options: formattedOptions,
            correctAnswer:
                (correctImageAnswer ? parseInt(correctImageAnswer) : null) ||
                correctAnswer,
            blanksCount: blanksCount || null,
            answers: answersToUse || answers,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await editQuestion(
                payload,
                questionId,
                authorization
            );
            if (response) {
                message.success("Question edited successfully");
                router.push("/knowledgeRally");
                localStorage.setItem("selectedGrade", grade);
                localStorage.setItem("selectedLevel", level);
                if (grade === "FIRST" || grade === "SECOND") {
                    localStorage.setItem("selectedSubject", subject);
                }
            }
        } catch (error: any) {
            message.error(error.message || "Failed to edit Question");
        }
        setLoading(false);
    };

    const [options, setOptions] = useState<
        { label: string; value: string; isCorrect?: boolean; order?: number }[]
    >([
        { label: "Option A", value: "" },
        { label: "Option B", value: "" },
        // { label: "Option C", value: "" },
        // { label: "Option D", value: "" },
    ]);

    // For set the question data in feilds
    useEffect(() => {
        const id = searchParams.get("id");
        if (id) {
            const fetchQuestion = async () => {
                try {
                    const authorization = localStorage.getItem("idToken");
                    if (!authorization) {
                        message.error(t("Authorization token is missing"));
                        return;
                    }

                    const response = await getQuestion(id, authorization);
                    if (response) {
                        setEditResponse(response);

                        const correctAnswerOrder =
                            response.options?.find(
                                (option: { isCorrect: any }) => option.isCorrect
                            )?.order ?? null;
                        if (response.answerType === "TEXT") {
                            setCorrectAnswer(correctAnswerOrder);
                        } else {
                            setCorrectImageAnswer(correctAnswerOrder);
                        }

                        setLevel(response.level);
                        setSubject(response.subject);
                        setGrade(response.grade);
                        setPracticeType(response.practiceType);
                        setQuestionType(response.type);
                        setCategory(response.category);
                        setSerialNo(response.serialNum.toString());
                        setQuestion(response.question);
                        setAnswerType(response.answerType);
                        setBlanksCount(response.blanksCount || 0);
                        // setImages(response.image ? [response.image] : []);
                        const allMedia = [
                            ...(response.images || []),
                            ...(response.documentFiles || []),
                            ...(response.audioFiles || []),
                        ];

                        setImages(allMedia);

                        if (response.type === "INPUT_BOX") {
                            setAnswers(response.answers[0].answer);
                        }

                        const mappedOptions = response.options.map(
                            (option: any) => ({
                                label: `Option ${String.fromCharCode(
                                    65 + option.order - 1
                                )}`,
                                value: option.value,
                                order: option.order,
                                isCorrect: option.isCorrect || false,
                            })
                        );

                        mappedOptions.sort(
                            (a: any, b: any) => a.order - b.order
                        );
                        if (response.answerType === "TEXT") {
                            setOptions(mappedOptions);
                        } else {
                            setImageOptions(mappedOptions);
                        }
                    }
                } catch (error) {
                    console.error(t("Error fetching question"));
                }
            };
            fetchQuestion();
        }
    }, [searchParams]);

    const [pdfModalOpen, setPdfModalOpen] = useState(false);
    const [audioModalOpen, setAudioModalOpen] = useState(false);
    const [selectedPdf, setSelectedPdf] = useState<string | null>(null);
    const [selectedAudio, setSelectedAudio] = useState<string | null>(null);

    const openPdfModal = (pdfUrl: string) => {
        setSelectedPdf(pdfUrl);
        setPdfModalOpen(true);
    };

    const openAudioModal = (audioUrl: string) => {
        setSelectedAudio(audioUrl);
        setAudioModalOpen(true);
    };

    // For detting the grade status
    const fetchGradeStatusData = async () => {
        const authorization = localStorage.getItem("idToken");
        try {
            setIsTableDataLoading(true);
            const response = await getAllGrade(authorization);

            if (response) {
                setGradeData(response);
                const disabled = response
                    .filter((grade: any) => grade.status === "DISABLED")
                    .map((grade: any) => grade.grade);

                setDisabledGrades(disabled);
            }
        } catch (error) {
            message.error(t("Data could not be retrieved"));
        } finally {
            setIsTableDataLoading(false);
        }
    };

    // useEffect(() => {
    //     if (questionType === "KEY_RULE") {
    //         return;
    //     } else if (question !== null) {
    //         setQuestion(question.replace(/<[^>]+>/g, ""));
    //     }
    // }, [questionType]);

    return (
        <div className={`${roboto.className}`}>
            <div
                className={`${roboto.className} shadow-md text-[25px] text-black font-[600] flex justify-between items-center px-4 z-10 bg-white h-auto py-3`}
                style={{ boxShadow: "0px 0px 8px 0px rgba(0, 0, 0, 0.15)" }}
            >
                <div>
                    <h1 className="text-[24px]">
                        {" "}
                        {questionMode === "view"
                            ? t("View Question")
                            : questionMode === "edit"
                            ? t("Edit Question")
                            : t("Add Question")}
                    </h1>
                </div>
                <div className={`flex `}>
                    <div className="flex justify-end">
                        {questionMode !== "view" && (
                            <>
                                {questionMode === "edit" ? (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] shadow-inner button font-roboto text-white text-[18px] w-[180px] h-[46px] font-medium rounded-xl"
                                        onClick={() =>
                                            handleSaveQuestion(
                                                searchParams.get("id") as string
                                            )
                                        }
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                ) : (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] shadow-inner button font-roboto text-white text-[18px] w-[180px] h-[46px] font-medium rounded-xl"
                                        onClick={handleAddQuestion}
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                )}
                            </>
                        )}
                    </div>
                    <div className="flex items-center px-4">
                        <Image
                            src="/images/cross.svg"
                            alt="cross"
                            width={28}
                            height={28}
                            className="cursor-pointer mr-[10px]"
                            onClick={() => router.back()}
                        />
                    </div>
                </div>
            </div>
            <div
                className={`w-full text-[18px] text-black font-semibold items-center bg-white ${roboto.className}`}
            >
                {isloading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-110px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <div
                        className={`mt-5 overflow-y-auto h-[calc(100vh-110px)] px-4 custom-scroll`}
                    >
                        <div className="flex items-center mb-4">
                            <h1 className="w-[160px]">Grade :</h1>
                            <Select
                                className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                                value={
                                    questionMode === "view" ||
                                    questionMode === "edit"
                                        ? grade
                                        : disabledGrades.includes(grade)
                                        ? grade
                                        : disabledGrades[0] || grade
                                }
                                onChange={(value) => setGrade(value)}
                                onClick={(e) => {
                                    if (questionMode === "view") {
                                        e.preventDefault();
                                    }
                                }}
                                // disabled={questionMode === "edit"}
                                style={{
                                    pointerEvents:
                                        questionMode === "view"
                                            ? "none"
                                            : "auto",
                                }}
                                suffixIcon={
                                    questionMode !== "view" && (
                                        <Image
                                            src="/images/arrowI.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                        />
                                    )
                                }
                            >
                                {(userType === "TEACHER"
                                    ? grades.slice(0, 2)
                                    : grades
                                ).map((option) => (
                                    <Option
                                        key={option.value}
                                        value={option.value}
                                        disabled={
                                            !disabledGrades.includes(
                                                option.value
                                            )
                                        }
                                    >
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[160px]">Level :</h1>
                            <Select
                                className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                                value={level}
                                onChange={(value) => setLevel(value)}
                                // disabled={questionMode === "edit"}
                                onClick={(e) => {
                                    if (questionMode === "view") {
                                        e.preventDefault();
                                    }
                                }}
                                style={{
                                    pointerEvents:
                                        questionMode === "view"
                                            ? "none"
                                            : "auto",
                                }}
                                suffixIcon={
                                    questionMode !== "view" && (
                                        <Image
                                            src="/images/arrowI.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                        />
                                    )
                                }
                            >
                                {levelOptions.map((option) => (
                                    <Option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[160px]">{t("Subject")} :</h1>
                            <Select
                                className="border-none custom-select h-[36px] font-roboto rounded-md w-[calc(100%-160px)] ml-2 font-[400]"
                                value={subject}
                                placeholder="Select subject"
                                onChange={(value) => setSubject(value)}
                                // disabled={questionMode === "view"}
                                onClick={(e) => {
                                    if (questionMode === "view") {
                                        e.preventDefault();
                                    }
                                }}
                                style={{
                                    pointerEvents:
                                        questionMode === "view"
                                            ? "none"
                                            : "auto",
                                }}
                                suffixIcon={
                                    questionMode !== "view" && (
                                        <Image
                                            src="/images/arrowI.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                        />
                                    )
                                }
                            >
                                {(grade === "THIRD"
                                    ? thirdGradeSubjects
                                    : forthGradeSubjects
                                ).map((option) => (
                                    <Option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        {grade !== "FIRST" && grade !== "SECOND" && (
                            <div className="flex items-center mb-4">
                                <h1 className="w-[160px]">
                                    {t("Practice Type")} :
                                </h1>
                                <Select
                                    className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                                    value={practiceType}
                                    onChange={(value) => setPracticeType(value)}
                                    onClick={(e) => {
                                        if (questionMode === "view") {
                                            e.preventDefault();
                                        }
                                    }}
                                    style={{
                                        pointerEvents:
                                            questionMode === "view"
                                                ? "none"
                                                : "auto",
                                    }}
                                    suffixIcon={
                                        questionMode !== "view" && (
                                            <Image
                                                src="/images/arrowI.svg"
                                                alt="More"
                                                width={20}
                                                height={20}
                                            />
                                        )
                                    }
                                >
                                    {practiceTypes.map((option) => (
                                        <Option
                                            key={option.value}
                                            value={option.value}
                                        >
                                            {option.label}
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                        )}
                        <div className="flex items-center mb-4">
                            <h1 className="w-[160px]">
                                {t("Question Type")} :
                            </h1>
                            <Select
                                className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                                value={questionType}
                                onChange={(value) => setQuestionType(value)}
                                // disabled={questionMode === "view"}
                                onClick={(e) => {
                                    if (questionMode === "view") {
                                        e.preventDefault();
                                    }
                                }}
                                style={{
                                    pointerEvents:
                                        questionMode === "view"
                                            ? "none"
                                            : "auto",
                                }}
                                suffixIcon={
                                    questionMode !== "view" && (
                                        <Image
                                            src="/images/arrowI.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                        />
                                    )
                                }
                            >
                                {questionTypes.map((option) => (
                                    <Option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[160px]">{t("category")} :</h1>
                            <Select
                                className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                                value={category}
                                onChange={(value) => setCategory(value)}
                                // disabled={questionMode === "view"}
                                onClick={(e) => {
                                    if (questionMode === "view") {
                                        e.preventDefault();
                                    }
                                }}
                                style={{
                                    pointerEvents:
                                        questionMode === "view"
                                            ? "none"
                                            : "auto",
                                }}
                                suffixIcon={
                                    questionMode !== "view" && (
                                        <Image
                                            src="/images/arrowI.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                        />
                                    )
                                }
                            >
                                {categoryOptions.map((option) => (
                                    <Option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[160px]">
                                {questionType === "KEY_RULE"
                                    ? t("Description")
                                    : t("Question")}{" "}
                                :
                            </h1>
                            {questionType === "KEY_RULE" ? (
                                <ReactQuill
                                    className={`${roboto.className} customQuill rounded-md font-roboto font-normal h-[150px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]`}
                                    style={{
                                        border: "1px solid #D3E3E3",
                                        borderRadius: "0.375rem",
                                        backgroundColor: "#F6FAF9",
                                        fontFamily: "Roboto",
                                    }}
                                    value={question || ""}
                                    onChange={(value: any) =>
                                        setQuestion(value)
                                    }
                                    placeholder={t("type_here")}
                                    readOnly={questionMode === "view"}
                                    modules={
                                        questionMode === "view"
                                            ? { toolbar: false }
                                            : modules
                                    }
                                />
                            ) : (
                                <TextArea
                                    className={`rounded-md pl-[11px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9] font-[400] ${roboto.className}`}
                                    value={question || ""}
                                    onChange={(e) =>
                                        setQuestion(e.target.value)
                                    }
                                    autoSize={{
                                        minRows: 4,
                                        maxRows: 4,
                                    }}
                                    placeholder={t("type_here")}
                                    onClick={(e) => {
                                        if (questionMode === "view") {
                                            e.preventDefault();
                                        }
                                    }}
                                    style={{
                                        pointerEvents: "auto",
                                        cursor:
                                            questionMode === "view"
                                                ? "default"
                                                : "text",
                                        resize: "none",
                                    }}
                                    readOnly={questionMode === "view"}
                                />
                            )}
                        </div>
                        {questionType === "INPUT_BOX" && (
                            <div className="text-right">
                                <h1 className=" text-[#67A1A3] text-[14px]">
                                    *For blanks, use [BLANK] or three or more
                                    underscores (&apos;___&apos;)
                                </h1>
                            </div>
                        )}
                        <div
                            className={`flex items-center ${
                                questionType === "KEY_RULE" ? "mt-14" : "mt-0"
                            }`}
                        >
                            <h1 className="w-[168px]"></h1>
                            <div className="flex flex-wrap gap-4 mb-4">
                                {images.map((file: any, index) => {
                                    if (!file || typeof file !== "string") {
                                        return (
                                            <div
                                                key={index}
                                                className="relative"
                                            >
                                                <label
                                                    htmlFor={`inputFile${index}`}
                                                    className="flex rounded-md h-[166px] w-[188px] justify-center items-center cursor-pointer border-[#D3E3E3] bg-[#F6FAF9]"
                                                    style={{
                                                        border: "1px solid #D3E3E3",
                                                    }}
                                                >
                                                    <Input
                                                        id={`inputFile${index}`}
                                                        type="file"
                                                        accept="image/*,audio/*,application/pdf"
                                                        style={{
                                                            display: "none",
                                                        }}
                                                        onChange={(e) =>
                                                            handleMediaChange(
                                                                e,
                                                                index
                                                            )
                                                        }
                                                        readOnly={
                                                            questionMode ===
                                                            "view"
                                                        }
                                                    />
                                                    {isUploading[index] ? (
                                                        <Spin size="small" />
                                                    ) : (
                                                        <p className="text-gray-400 font-roboto text-[16px] text-center px-2 font-[400]">
                                                            {t(
                                                                "Add Audio, Document or Image"
                                                            )}
                                                        </p>
                                                    )}
                                                </label>
                                                {index !== 0 && (
                                                    <div
                                                        className="absolute top-1 right-1 cursor-pointer"
                                                        onClick={() =>
                                                            deleteQuestionMedia(
                                                                index
                                                            )
                                                        }
                                                    >
                                                        {questionMode !==
                                                            "view" && (
                                                            <DeleteOutlined className="ml-2 text-[#67A1A3] cursor-pointer" />
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        ); // Skip rendering for undefined/null/invalid files
                                    }

                                    let isPdf = file.includes(".pdf");
                                    let isAudio =
                                        file.includes(".mp3") ||
                                        file.includes(".wav");
                                    let isImage = !isPdf && !isAudio;

                                    return (
                                        <div
                                            className="relative flex items-center"
                                            key={index}
                                        >
                                            {file ? (
                                                <>
                                                    {isImage ? (
                                                        <img
                                                            src={file}
                                                            alt="uploaded"
                                                            className="rounded-md h-[166px] w-[188px] border-[#D3E3E3] bg-[#F6FAF9] cursor-pointer"
                                                        />
                                                    ) : isPdf ? (
                                                        <img
                                                            src="/images/knowledge-rally/pdf.svg"
                                                            alt="PDF File"
                                                            className="rounded-md h-[166px] w-[188px] border-[#D3E3E3] bg-[#F6FAF9] cursor-pointer"
                                                            onClick={() =>
                                                                openPdfModal(
                                                                    file
                                                                )
                                                            }
                                                        />
                                                    ) : isAudio ? (
                                                        <img
                                                            src="/images/knowledge-rally/audio.svg"
                                                            alt="Audio File"
                                                            className="rounded-md h-[166px] w-[188px] border-[#D3E3E3] bg-[#F6FAF9] cursor-pointer"
                                                            onClick={() =>
                                                                openAudioModal(
                                                                    file
                                                                )
                                                            }
                                                        />
                                                    ) : null}

                                                    <div
                                                        className="absolute top-1 right-1 cursor-pointer"
                                                        onClick={() =>
                                                            deleteQuestionMedia(
                                                                index
                                                            )
                                                        }
                                                    >
                                                        {questionMode !==
                                                            "view" && (
                                                            <DeleteOutlined className="ml-2 text-[#67A1A3] cursor-pointer" />
                                                        )}
                                                    </div>
                                                </>
                                            ) : (
                                                <>
                                                    <label
                                                        htmlFor={`inputFile${index}`}
                                                        className="flex rounded-md h-[166px] w-[188px] justify-center items-center cursor-pointer border-[#D3E3E3] bg-[#F6FAF9]"
                                                        style={{
                                                            border: "1px solid #D3E3E3",
                                                        }}
                                                    >
                                                        <Input
                                                            id={`inputFile${index}`}
                                                            type="file"
                                                            accept="image/*,audio/*,application/pdf"
                                                            style={{
                                                                display: "none",
                                                            }}
                                                            onChange={(e) =>
                                                                handleMediaChange(
                                                                    e,
                                                                    index
                                                                )
                                                            }
                                                            readOnly={
                                                                questionMode ===
                                                                "view"
                                                            }
                                                        />
                                                        {isUploading[index] ? (
                                                            <Spin size="small" />
                                                        ) : (
                                                            <p className="text-gray-400 font-roboto text-[16px] text-center px-2">
                                                                {t(
                                                                    "Add Audio, Document or Image"
                                                                )}
                                                            </p>
                                                        )}
                                                    </label>
                                                    {index !== 0 && (
                                                        <div
                                                            className="absolute top-1 right-1 cursor-pointer"
                                                            onClick={() =>
                                                                deleteQuestionMedia(
                                                                    index
                                                                )
                                                            }
                                                        >
                                                            {questionMode !==
                                                                "view" && (
                                                                <DeleteOutlined className="ml-2 text-[#67A1A3] cursor-pointer" />
                                                            )}
                                                        </div>
                                                    )}
                                                </>
                                            )}
                                        </div>
                                    );
                                })}

                                {/* Add More Button */}
                                <div className="flex justify-center items-center">
                                    {images.length < 4 &&
                                        questionMode !== "view" && (
                                            <h1
                                                className="cursor-pointer text-[18px] mb-4 text-[#67A1A3] underline font-[500]"
                                                onClick={() =>
                                                    images.length > 0
                                                        ? images[
                                                              images.length - 1
                                                          ] !== undefined
                                                            ? addMore()
                                                            : message.warning(
                                                                  "Please select a file for the first field before add more"
                                                              )
                                                        : addMore()
                                                }
                                            >
                                                {images.length > 0
                                                    ? t("Add More")
                                                    : t(
                                                          "Add Audio, Document or Image"
                                                      )}
                                            </h1>
                                        )}
                                </div>
                            </div>

                            {/* PDF Viewer Modal */}
                            <Modal
                                title="PDF Viewer"
                                open={pdfModalOpen}
                                onCancel={() => {
                                    setPdfModalOpen(false);
                                    setSelectedPdf(null);
                                }}
                                footer={null}
                                width="70vw"
                                centered
                            >
                                {selectedPdf && (
                                    <iframe
                                        key={selectedPdf}
                                        src={selectedPdf}
                                        title="PDF Viewer"
                                        className="w-full h-[550px]"
                                    />
                                )}
                            </Modal>

                            {/* Audio Player Modal */}
                            <Modal
                                title="Audio Player"
                                open={audioModalOpen}
                                // onCancel={() => setAudioModalOpen(false)}
                                onCancel={() => {
                                    if (audioRef.current) {
                                        audioRef.current.pause(); // Pause the audio
                                        audioRef.current.currentTime = 0; // Reset playback to start
                                    }
                                    setAudioModalOpen(false);
                                    setSelectedAudio(null); // Reset selected audio (optional)
                                }}
                                footer={null}
                                centered
                            >
                                {selectedAudio && (
                                    <audio
                                        ref={audioRef}
                                        controls
                                        className="w-full"
                                    >
                                        <source
                                            src={selectedAudio}
                                            type="audio/mp3"
                                        />
                                        <source
                                            src={selectedAudio}
                                            type="audio/wav"
                                        />
                                        Your browser does not support the audio
                                        element.
                                    </audio>
                                )}
                            </Modal>
                        </div>
                        {questionType !== "INPUT_BOX" &&
                            questionType !== "KEY_RULE" && (
                                <div className="flex items-center mb-4">
                                    <h1 className="w-[160px]">
                                        {t("Answer Type")} :
                                    </h1>
                                    <Select
                                        className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                                        value={answerType}
                                        onChange={(value) =>
                                            setAnswerType(value)
                                        }
                                        // disabled={
                                        //     questionMode === "view"
                                        // }
                                        onClick={(e) => {
                                            if (questionMode === "view") {
                                                e.preventDefault();
                                            }
                                        }}
                                        style={{
                                            pointerEvents:
                                                questionMode === "view"
                                                    ? "none"
                                                    : "auto",
                                        }}
                                        suffixIcon={
                                            questionMode !== "view" && (
                                                <Image
                                                    src="/images/arrowI.svg"
                                                    alt="More"
                                                    width={20}
                                                    height={20}
                                                />
                                            )
                                        }
                                    >
                                        {answerTypes.map((option) => (
                                            <Option
                                                key={option.value}
                                                value={option.value}
                                            >
                                                {option.label}
                                            </Option>
                                        ))}
                                    </Select>
                                </div>
                            )}
                        {questionType === "MCQ" && answerType === "TEXT" && (
                            <div>
                                {options.map((option, index) => (
                                    <div
                                        className="flex items-center mb-4"
                                        key={index}
                                    >
                                        <h1 className="w-[160px]">
                                            {option.label} :
                                        </h1>
                                        <Input
                                            className="rounded-md font-roboto font-normal h-[36px] pl-[11px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                                            value={option.value}
                                            onChange={(e) =>
                                                handleOptionChange(
                                                    index,
                                                    e.target.value
                                                )
                                            }
                                            placeholder={t("type_here")}
                                            readOnly={questionMode === "view"}
                                            style={{
                                                pointerEvents:
                                                    questionMode === "view"
                                                        ? "none"
                                                        : "auto",
                                            }}
                                        />
                                        {index >= 2 &&
                                            questionMode !== "view" && (
                                                <DeleteOutlined
                                                    className="ml-2 text-[#67A1A3] cursor-pointer"
                                                    onClick={() =>
                                                        deleteOption(index)
                                                    }
                                                />
                                            )}
                                    </div>
                                ))}
                                {options.length < 10 &&
                                    questionMode !== "view" && (
                                        <div className="flex justify-end">
                                            <h1
                                                className=" cursor-pointer text-[18px] text-[#67A1A3] mr-2 underline mb-[10px]"
                                                onClick={addOption}
                                            >
                                                {t("Add Option")}
                                            </h1>
                                        </div>
                                    )}
                                <div className="flex items-center mb-4">
                                    <h1 className="w-[160px]">
                                        {t("Correct Answer")} :
                                    </h1>
                                    <Select
                                        className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[calc(100%-160px)] font-[400]"
                                        value={
                                            correctAnswer !== -1
                                                ? correctAnswer
                                                : undefined
                                        }
                                        placeholder={t("Select Correct Answer")}
                                        onChange={(value) =>
                                            setCorrectAnswer(value)
                                        }
                                        // disabled={
                                        //     questionMode === "view"
                                        // }
                                        onClick={(e) => {
                                            if (questionMode === "view") {
                                                e.preventDefault();
                                            }
                                        }}
                                        style={{
                                            pointerEvents:
                                                questionMode === "view"
                                                    ? "none"
                                                    : "auto",
                                        }}
                                        suffixIcon={
                                            questionMode !== "view" && (
                                                <Image
                                                    src="/images/arrowI.svg"
                                                    alt="More"
                                                    width={20}
                                                    height={20}
                                                />
                                            )
                                        }
                                    >
                                        {options.map((option, index) => (
                                            <Option
                                                key={index + 1}
                                                value={index + 1}
                                            >
                                                {option.label}
                                            </Option>
                                        ))}
                                    </Select>
                                </div>
                            </div>
                        )}
                        {questionType === "MCQ" && answerType === "IMAGE" && (
                            <>
                                <div className="flex flex-wrap">
                                    <h1 className="w-[168px]">Options :</h1>
                                    <div className="flex flex-wrap gap-4 w-[calc(100%-300px)]">
                                        {imageOptions.map(
                                            (imageOption, index) => (
                                                <div
                                                    key={index}
                                                    className="mb-4 relative"
                                                >
                                                    <h1 className="mb-1">{`Option ${String.fromCharCode(
                                                        65 + index
                                                    )}:`}</h1>
                                                    <label
                                                        className="flex  rounded-md h-[166px] w-[188px] justify-center items-center cursor-pointer border-[#D3E3E3] bg-[#F6FAF9]"
                                                        style={{
                                                            border: "1px solid #D3E3E3",
                                                        }}
                                                    >
                                                        <Input
                                                            type="file"
                                                            accept="image/*"
                                                            style={{
                                                                display: "none",
                                                            }}
                                                            onChange={(e) =>
                                                                handleImageOptionChange(
                                                                    e,
                                                                    index
                                                                )
                                                            }
                                                            readOnly={
                                                                questionMode ===
                                                                "view"
                                                            }
                                                        />
                                                        {imageOption.value ? (
                                                            <>
                                                                <img
                                                                    src={
                                                                        imageOption.value
                                                                    }
                                                                    alt={`Option ${String.fromCharCode(
                                                                        65 +
                                                                            index
                                                                    )}`}
                                                                    className="flex rounded-md font-roboto font-normal h-[166px] w-[188px] text-[20px] text-center bg-[#F6FAF9]"
                                                                />
                                                            </>
                                                        ) : (
                                                            <p className="text-gray-400 font-roboto font-normal text-[16px]">
                                                                {t("Add Image")}
                                                            </p>
                                                        )}
                                                    </label>
                                                    {index >= 2 && (
                                                        <DeleteOutlined
                                                            className="ml-1 mt-1 text-[#67A1A3] cursor-pointer absolute"
                                                            onClick={() =>
                                                                deleteOptionImage(
                                                                    index
                                                                )
                                                            }
                                                        />
                                                    )}
                                                </div>
                                            )
                                        )}
                                    </div>
                                    {imageOptions.length < 10 &&
                                        questionMode !== "view" && (
                                            <div className="flex items-center ml-[10px]">
                                                <h1
                                                    className="cursor-pointer text-[18px] text-[#67A1A3] underline mb-[10px]"
                                                    onClick={addImageOption}
                                                >
                                                    {t("Add Option")}
                                                </h1>
                                            </div>
                                        )}
                                </div>
                                <div className="flex items-center mb-4">
                                    <h1 className="w-[160px]">
                                        {t("Correct Answer")} :
                                    </h1>
                                    <Select
                                        className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 mt-3 w-[calc(100%-160px)] font-[400]"
                                        value={
                                            correctImageAnswer !== -1
                                                ? correctImageAnswer
                                                : undefined
                                        }
                                        placeholder={t("Select Correct Answer")}
                                        onChange={(value) =>
                                            setCorrectImageAnswer(value)
                                        }
                                        // disabled={
                                        //     questionMode === "view"
                                        // }
                                        onClick={(e) => {
                                            if (questionMode === "view") {
                                                e.preventDefault();
                                            }
                                        }}
                                        style={{
                                            pointerEvents:
                                                questionMode === "view"
                                                    ? "none"
                                                    : "auto",
                                        }}
                                        suffixIcon={
                                            questionMode !== "view" && (
                                                <Image
                                                    src="/images/arrowI.svg"
                                                    alt="More"
                                                    width={20}
                                                    height={20}
                                                />
                                            )
                                        }
                                    >
                                        {imageOptions.map(
                                            (imageOption, index) =>
                                                imageOption.value ? (
                                                    <Option
                                                        key={index + 1}
                                                        value={index + 1}
                                                    >
                                                        {imageOption.label}
                                                    </Option>
                                                ) : null
                                        )}
                                    </Select>
                                </div>
                            </>
                        )}
                        {questionType === "INPUT_BOX" && (
                            <div>
                                {Array.from({ length: blanksCount }).map(
                                    (_, index) => (
                                        <div
                                            key={index}
                                            className="flex items-center mb-4"
                                        >
                                            <h1 className="w-[160px]">
                                                {t("Correct Answer")}{" "}
                                                {index + 1} :
                                            </h1>
                                            <Input
                                                type="text"
                                                className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[calc(100%-160px)] border-[#D3E3E3] bg-[#F6FAF9]"
                                                value={
                                                    answers
                                                        ? answers[index]
                                                        : ""
                                                }
                                                onChange={(e) =>
                                                    handleAnswerChange(
                                                        index,
                                                        e.target.value
                                                    )
                                                }
                                                placeholder={`Enter Answer ${
                                                    index + 1
                                                }`}
                                                readOnly={
                                                    questionMode === "view"
                                                }
                                                style={{
                                                    pointerEvents:
                                                        questionMode === "view"
                                                            ? "none"
                                                            : "auto",
                                                }}
                                            />
                                        </div>
                                    )
                                )}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default AddQuestion;

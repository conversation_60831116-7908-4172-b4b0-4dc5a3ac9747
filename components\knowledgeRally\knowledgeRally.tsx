"use client";
import React, { useState, useEffect, useContext, useMemo } from "react";
import Navbar from "../Navbar/navbar";
import SideBar from "@/app/sideBar/page";
import "@/app/globals.css";
import Image from "next/image";
import {
    Dropdown,
    Radio,
    Menu,
    Table,
    Spin,
    Button,
    Modal,
    message,
    Segmented,
    Empty,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import { useRouter } from "next/navigation";
import {
    getAllQuestions,
    QueryParams,
    deleteQuestion,
    getQuestion,
    reorderQuestion,
    reorderQuestionPayload,
} from "@/src/services/knowledgeRally.api";
import {
    grades,
    Grade3rdLevel,
    Grade4thLevel,
    thirdGradeSubjects,
    forthGradeSubjects,
    questionTypes,
    germanCategories,
    mathsCategories,
    englishCategories,
    gradeFirstEnglishCategories,
    gradeFirstMathsCategories,
    gradeFirstGermanCategories,
} from "@/src/libs/constants";
import { CircularProgress } from "@mui/material";
import { Roboto } from "next/font/google";
import { DndContext, closestCenter, DragEndEvent } from "@dnd-kit/core";
import {
    arrayMove,
    SortableContext,
    useSortable,
    verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { HolderOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";
import {
    getAllGrade,
    getAllForTecherGrade,
} from "@/src/services/manage-grade.api";

interface questionByLevel {
    srNo: number;
    subject?: string;
    grade?: string;
    id?: string;
}

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

type Align = "Warm Up" | "Regular";

const KnowledgeRally = () => {
    const [selectedGrade, setSelectedGrade] = useState<string>(
        localStorage.getItem("selectedGrade") || "FIRST"
    );
    const [selectedLevel, setSelectedLevel] = useState<string>(
        localStorage.getItem("selectedLevel") || "LEVEL_1"
    );
    const [selectedSubject, setSelectedSubject] = useState<string>(
        localStorage.getItem("selectedSubject") || "GERMAN"
    );
    const [levelDropdownVisible, setLevelDropdownVisible] =
        useState<boolean>(false);
    const [subjectDropdownVisible, setSubjectDropdownVisible] =
        useState<boolean>(false);
    const [isTabledataLoading, setIsabledataLoading] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] =
        useState<boolean>(false);
    const [recordToDelete, setRecordToDelete] = useState<any>(null);
    const router = useRouter();
    const [userType, setUserType] = useState(
        localStorage.getItem("userType") || "ADMIN"
    );
    const [response, setResponse] = useState<any>(null);
    const [gradeData, setGradeData] = useState<any>([]);
    const [data, setData] = useState<questionByLevel[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState<number>(100);
    const [deleteQueation, setDeleteQueation] = useState(false);
    const [alignValue, setAlignValue] = React.useState<Align>("Regular");
    const [reorderingIsOngoing, setReorderingIsOngoing] = useState(true);
    const [isTableDataLoading, setIsTableDataLoading] = useState(false);
    const [disabledGrades, setDisabledGrades] = useState<string[]>([]);
    const { t } = useTranslation();

    const SortableRow = ({ ...props }) => {
        const { attributes, listeners, setNodeRef, transform, transition } =
            useSortable({
                id: props["data-row-key"],
            });

        const style = {
            ...props.style,
            transform: CSS.Transform.toString(
                transform && {
                    ...transform,
                    scaleY: 1,
                }
            ),
            transition,
        };

        const childrenArray = React.Children.toArray(props.children);

        return (
            <>
                {data && data.length > 0 && (
                    <tr {...props} ref={setNodeRef} style={style}>
                        <td
                            {...attributes}
                            {...listeners}
                            style={{ cursor: "move", padding: "8px" }}
                        >
                            <HolderOutlined className="pl-[12px]" />
                        </td>
                        {childrenArray.slice(1)}
                    </tr>
                )}
            </>
        );
    };

    const DraggableBodyRow = (props: any) => <SortableRow {...props} />;

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;

        if (active.id !== over?.id && reorderingIsOngoing) {
            setData((prevData) => {
                const oldIndex = prevData.findIndex(
                    (item) => item.id === active.id
                );
                const newIndex = prevData.findIndex(
                    (item) => item.id === over?.id
                );
                const updatedData = arrayMove(prevData, oldIndex, newIndex);
                const page = currentPage;
                const pageSize = 10;

                const updatedWithSrNo = updatedData.map((item, index) => ({
                    ...item,
                    srNo: (page - 1) * pageSize + index + 1,
                }));

                if (active?.id && newIndex !== -1) {
                    reorderQuestionById(active.id, newIndex + 1);
                }

                return updatedWithSrNo;
            });
        }
    };

    const reorderQuestionById = async (
        questionId: any,
        newPosition: number
    ) => {
        const payload: reorderQuestionPayload = {
            grade: selectedGrade,
            level: selectedLevel,
            questionId: questionId,
            newPosition: newPosition,
            ...((selectedGrade === "FIRST" || selectedGrade === "SECOND") && {
                subject: selectedSubject,
            }),
        };
        const authorization = localStorage.getItem("idToken");
        try {
            setReorderingIsOngoing(false);
            const reorderRes = await reorderQuestion(payload, authorization);
            if (reorderRes) {
                // message.success(reorderRes);
                fetchData(currentPage);
                setReorderingIsOngoing(true);
            }
        } catch (error) {
            message.error(t("Something went wrong")); // Something went wrong
            setReorderingIsOngoing(false);
        }
    };

    useEffect(() => {
        // setTimeout(() => {
        //     setIsLoading(false);
        // }, 2000);
        fetchGradeStatusData();
    }, []);

    useEffect(() => {
        const savedGrade = localStorage.getItem("selectedGrade");
        const savedLevel = localStorage.getItem("selectedLevel");
        const userType = localStorage.getItem("userType");
        if (userType) {
            setUserType(userType);
        }
        if (savedGrade === "FIRST" || savedGrade === "SECOND") {
            const savedSubject = localStorage.getItem("selectedSubject");

            if (savedSubject) {
                localStorage.removeItem("selectedSubject");
            }
        }
        if (savedGrade) {
            localStorage.removeItem("selectedGrade");
        }
        if (savedLevel) {
            localStorage.removeItem("selectedLevel");
        }

        if (selectedGrade === "FIRST" || selectedGrade === "SECOND") {
            setAlignValue("Regular");
        }

        fetchData(currentPage);
    }, [
        selectedGrade,
        currentPage,
        selectedLevel,
        pageSize,
        alignValue,
        selectedSubject,
    ]);

    const fetchData = async (page: number = 1) => {
        const queryParams: QueryParams = {
            grade: selectedGrade,
            level: selectedLevel,
            skip: (page - 1) * pageSize,
            take: pageSize,
            orderBy: "serialNum|asc",
            practiceType: alignValue === t("warm_up") ? "WARM_UP" : "NORMAL",
            ...((selectedGrade === "FIRST" || selectedGrade === "SECOND") && {
                subject: selectedSubject,
            }),
        };
        const authorization = localStorage.getItem("idToken");
        try {
            setIsabledataLoading(true);
            const response = await getAllQuestions(queryParams, authorization);
            setResponse(response);
            if (response) {
                const updatedData = response.list.map(
                    (item: any, index: any) => ({
                        ...item,
                        srNo: (page - 1) * pageSize + index + 1,
                    })
                );
                setData(updatedData);
                setTotal(response.total);
            } else {
                message.error(t("Empty response data"));
            }
            setIsabledataLoading(false);
        } catch (error) {
            message.error(t("Data could not be retrieved"));
        }
        setIsabledataLoading(false);
    };

    const handleGradeSelect = (grade: string) => {
        setSelectedGrade(grade);
        setSelectedLevel("LEVEL_1");
    };

    const handleLevelSelect = ({ key }: { key: string | number }) => {
        setSelectedLevel(key.toString());
        setLevelDropdownVisible(false);
    };

    const levelMenu = (
        <Menu onClick={handleLevelSelect}>
            {selectedGrade === "THIRD" ||
            selectedGrade === "FIRST" ||
            selectedGrade === "SECOND"
                ? Grade3rdLevel.map((level) => (
                      <Menu.Item key={level.value} className="text-[20px]">
                          <div className="flex items-center">
                              <span
                                  className={`text-[14px] font-roboto font-medium mr-[30px]  ${
                                      selectedLevel === level.value
                                          ? "text-black"
                                          : "text-gray-500"
                                  }`}
                              >
                                  {level.label}
                              </span>
                              <Radio
                                  value={level.value}
                                  checked={selectedLevel === level.value}
                                  onChange={() =>
                                      handleLevelSelect({ key: level.value })
                                  }
                                  className="custom-radio mr-0"
                              />
                          </div>
                      </Menu.Item>
                  ))
                : Grade4thLevel.map((level) => (
                      <Menu.Item key={level.value} className="text-[20px]">
                          <div className="flex items-center">
                              <span
                                  className={`text-[14px] font-roboto font-medium mr-[30px] ${
                                      selectedLevel === level.value
                                          ? "text-black-500"
                                          : "text-gray-500"
                                  }`}
                              >
                                  {level.label}
                              </span>
                              <Radio
                                  value={level.value}
                                  checked={selectedLevel === level.value}
                                  onChange={() =>
                                      handleLevelSelect({ key: level.value })
                                  }
                                  className="custom-radio mr-0"
                              />
                          </div>
                      </Menu.Item>
                  ))}
        </Menu>
    );

    const handleSubjectSelect = ({ key }: { key: string | number }) => {
        setSelectedSubject(key.toString());
        setSubjectDropdownVisible(false);
    };

    const subjectMenu = (
        <Menu onClick={handleSubjectSelect}>
            {(selectedGrade === "FIRST" || selectedGrade === "SECOND") &&
                forthGradeSubjects.map((subject) => (
                    <Menu.Item key={subject.value} className="text-[20px]">
                        <div
                            className="flex items-center justify-between w-[100px]"
                            onChange={() =>
                                handleSubjectSelect({ key: subject.value })
                            }
                        >
                            <div
                                className={`text-[14px] font-roboto font-medium ${
                                    selectedSubject === subject.value
                                        ? "text-black-500"
                                        : "text-gray-500"
                                }`}
                            >
                                {subject.label}
                            </div>
                            <Radio
                                value={subject.value}
                                checked={selectedSubject === subject.value}
                                onChange={() =>
                                    handleSubjectSelect({ key: subject.value })
                                }
                                className="custom-radio mr-0"
                            />
                        </div>
                    </Menu.Item>
                ))}
        </Menu>
    );

    const getLevelLabel = (levelValue: string) => {
        if (selectedGrade === "THIRD") {
            const level = Grade3rdLevel.find(
                (level) => level.value === levelValue
            );
            return level ? level.label : "";
        } else {
            const level = Grade4thLevel.find(
                (level) => level.value === levelValue
            );
            return level ? level.label : "";
        }
    };

    const handleMenuClick = async (e: any, record: any) => {
        e.domEvent.stopPropagation();
        setRecordToDelete(record.id);
        const { key } = e;
        if (key === "view") {
            localStorage.setItem("selectedGrade", selectedGrade);
            localStorage.setItem("selectedLevel", selectedLevel);
            if (selectedGrade === "FIRST" || selectedGrade === "SECOND") {
                localStorage.setItem("selectedSubject", selectedSubject);
            }
            localStorage.setItem("mode", "view");
            router.push(
                `/knowledgeRally/addQuestion?id=${record.id}`
            );
        } else if (key === "edit") {
            localStorage.setItem("selectedGrade", selectedGrade);
            localStorage.setItem("selectedLevel", selectedLevel);
            if (selectedGrade === "FIRST" || selectedGrade === "SECOND") {
                localStorage.setItem("selectedSubject", selectedSubject);
            }localStorage.setItem("mode", "edit");
            router.push(
                `/knowledgeRally/addQuestion?id=${record.id}`
            );
        } else if (key === "delete") {
            setIsDeleteModalVisible(true);
        }
    };

    const menu = (record: any) => (
        <Menu onClick={(e) => handleMenuClick(e, record)}>
            <Menu.Item key="view">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/eye.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    {t("View")}
                </div>
            </Menu.Item>
            <Menu.Item key="edit">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[10px] mb-1"
                    />
                    {t("Edit")}
                </div>
            </Menu.Item>
            <Menu.Item key="delete">
                <div className="flex text-[14px] font-roboto text-[#F1676D]">
                    <Image
                        src="/images/trash.svg"
                        alt="visibility"
                        width={23}
                        height={23}
                        className="cursor-pointer mr-[7px] mb-1"
                    />
                    {t("delete")}
                </div>
            </Menu.Item>
        </Menu>
    );

    const questionsByLevels: ColumnsType<questionByLevel> = [
        {
            title: "",
            dataIndex: "sort",
            className: `${roboto.className}`,
            align: "center",
            width: 40,
            render: () => <SortableRow className="pl-3" />,
        },
        {
            title: "Sr. No.",
            dataIndex: "srNo",
            key: "srNo",
            align: "center",
            className: `${roboto.className}`,
            width: 80,
            render: (srNo) => <div className="">{srNo}.</div>,
        },
        {
            title: t("Questions"),
            dataIndex: "question",
            key: "question",
            align: "center",
            className: `${roboto.className}`,
            width: 500,
            render: (question) => {
                // Function to strip HTML tags
                const stripHtml = (html: any) => {
                    if (!html) return "-";
                    const doc = new DOMParser().parseFromString(
                        html,
                        "text/html"
                    );
                    return doc.body.textContent || "-";
                };

                // Function to truncate text
                const truncateText = (text: any, maxLength = 200) => {
                    return text.length > maxLength
                        ? text.substring(0, maxLength) + "..."
                        : text;
                };

                const plainText = stripHtml(question);
                return (
                    <div className="text-start">{truncateText(plainText)}</div>
                );
            },
        },
        {
            title: t("Subject"),
            dataIndex: "subject",
            key: "subject",
            align: "center",
            className: `${roboto.className}`,
            render: (subject) => {
                const subjectLabel =
                    forthGradeSubjects.find((item) => item.value === subject)
                        ?.label || "-";
                return <div className="">{subjectLabel}</div>;
            },
        },
        {
            title: t("category"),
            dataIndex: "category",
            key: "category",
            align: "center",
            className: `${roboto.className}`,
            render: (category, record) => {
                let categoryLabel = "-";
                const categoryList =
                    record.grade === "FIRST" || record.grade === "SECOND"
                        ? record.subject === "ENGLISH"
                            ? gradeFirstEnglishCategories
                            : record.subject === "MATHS"
                            ? gradeFirstMathsCategories
                            : record.subject === "GERMAN"
                            ? gradeFirstGermanCategories
                            : []
                        : record.subject === "ENGLISH"
                        ? englishCategories
                        : record.subject === "MATHS"
                        ? mathsCategories
                        : record.subject === "GERMAN"
                        ? germanCategories
                        : [];

                categoryLabel =
                    categoryList.find((item) => item.value === category)
                        ?.label || "-";

                return <div className="">{categoryLabel}</div>;
            },
        },
        {
            title: t("Type"),
            dataIndex: "type",
            key: "type",
            align: "center",
            className: `${roboto.className}`,
            render: (type) => {
                const typeLabel =
                    questionTypes.find((item) => item.value === type)?.label ||
                    "-";
                return <div className="">{typeLabel}</div>;
            },
        },
        {
            title: t("More"),
            dataIndex: "more",
            key: "more",
            align: "center",
            className: `${roboto.className}`,
            width: 100,
            render: (more: string, record: questionByLevel) => {
                return record.id ? (
                    <Dropdown
                        overlay={menu(record)}
                        trigger={["click"]}
                        // onOpenChange={handleMoreMenuClick}
                        className="rounded-3xl"
                        placement="bottomLeft"
                    >
                        <div
                            className="flex justify-center"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <Image
                                src="/images/more.svg"
                                alt="More"
                                width={20}
                                height={20}
                                className="cursor-pointer"
                                onClick={(e) => e.preventDefault()}
                            />
                        </div>
                    </Dropdown>
                ) : (
                    <CircularProgress size={20} />
                );
            },
        },
    ];

    const questionsDisableGradeByLevels: ColumnsType<questionByLevel> = [
        {
            title: "",
            dataIndex: "sort",
            className: `${roboto.className}`,
            align: "center",
            width: 40,
            render: () => <SortableRow className="pl-3" />,
        },
        {
            title: "Sr. No.",
            dataIndex: "srNo",
            key: "srNo",
            align: "center",
            className: `${roboto.className}`,
            width: 80,
            render: (srNo) => <div className="">{srNo}.</div>,
        },
        {
            title: t("Questions"),
            dataIndex: "question",
            key: "question",
            align: "center",
            className: `${roboto.className}`,
            width: 500,
            render: (question) => {
                // Function to strip HTML tags
                const stripHtml = (html: any) => {
                    if (!html) return "-";
                    const doc = new DOMParser().parseFromString(
                        html,
                        "text/html"
                    );
                    return doc.body.textContent || "-";
                };

                // Function to truncate text
                const truncateText = (text: any, maxLength = 200) => {
                    return text.length > maxLength
                        ? text.substring(0, maxLength) + "..."
                        : text;
                };

                const plainText = stripHtml(question);
                return (
                    <div className="text-start">{truncateText(plainText)}</div>
                );
            },
        },
        {
            title: t("Subject"),
            dataIndex: "subject",
            key: "subject",
            align: "center",
            className: `${roboto.className}`,
            render: (subject) => {
                const subjectLabel =
                    forthGradeSubjects.find((item) => item.value === subject)
                        ?.label || "-";
                return <div className="">{subjectLabel}</div>;
            },
        },
        {
            title: t("category"),
            dataIndex: "category",
            key: "category",
            align: "center",
            className: `${roboto.className}`,
            render: (category, record) => {
                let categoryLabel = "-";
                const categoryList =
                    record.grade === "FIRST" || record.grade === "SECOND"
                        ? record.subject === "ENGLISH"
                            ? gradeFirstEnglishCategories
                            : record.subject === "MATHS"
                            ? gradeFirstMathsCategories
                            : record.subject === "GERMAN"
                            ? gradeFirstGermanCategories
                            : []
                        : record.subject === "ENGLISH"
                        ? englishCategories
                        : record.subject === "MATHS"
                        ? mathsCategories
                        : record.subject === "GERMAN"
                        ? germanCategories
                        : [];

                categoryLabel =
                    categoryList.find((item) => item.value === category)
                        ?.label || "-";

                return <div className="">{categoryLabel}</div>;
            },
        },
        {
            title: t("Type"),
            dataIndex: "type",
            key: "type",
            align: "center",
            width: 150,
            className: `${roboto.className}`,
            render: (type) => {
                const typeLabel =
                    questionTypes.find((item) => item.value === type)?.label ||
                    "-";
                return <div className="">{typeLabel}</div>;
            },
        },
    ];

    const paginationConfig = {
        pageSize: 100,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    const handleDeleteConfirm = async (id: string) => {
        try {
            setDeleteQueation(true);
            const authorization = localStorage.getItem("idToken");
            const response = await deleteQuestion(id, authorization);
            if (response) {
                setIsDeleteModalVisible(false);
                setDeleteQueation(false);
                fetchData(currentPage);
                message.success(t("Question deleted successfully")); // Question deleted successfully
            }
        } catch (error) {
            console.error(t("Failed to delete question")); // Failed to delete question
            setDeleteQueation(false);
            setIsDeleteModalVisible(true);
        }
    };

    // For detting the grade status
    const fetchGradeStatusData = async () => {
        const authorization = localStorage.getItem("idToken");
        try {
            setIsTableDataLoading(true);
            const response = await getAllGrade(authorization);

            if (response) {
                setGradeData(response);

                // Extract disabled grades and update state
                const disabled = response
                    .filter((grade: any) => grade.status === "DISABLED")
                    .map((grade: any) => grade.grade);

                setDisabledGrades(disabled);
                // setSelectedGrade(disabled[0]);
            }
        } catch (error) {
            message.error(t("Data could not be retrieved"));
        } finally {
            setIsTableDataLoading(false);
        }
    };

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`${roboto.className} bg-white text-[25px] text-black font-medium items-center mt-4`}
            >
                <div className="flex px-4 justify-between items-center">
                    <div className="space-x-3">
                        {(userType === "TEACHER"
                            ? grades.slice(0, 2)
                            : grades
                        ).map((grade) => (
                            <button
                                key={grade.value}
                                onClick={() => handleGradeSelect(grade.value)}
                                className={`rounded-[10px] w-[130px] h-[40px] text-[16px] ${
                                    selectedGrade === grade.value
                                        ? "bg-[#67A1A3] text-white"
                                        : "bg-[#D3E3E3] text-[#67A1A3]"
                                }`}
                            >
                                {grade.label}
                            </button>
                        ))}
                    </div>
                    <div className="text-[25px] text-black flex items-center space-x-3">
                        {userType === "ADMIN" &&
                            selectedGrade !== "FIRST" &&
                            selectedGrade !== "SECOND" && (
                                <div className="flex justify-center items-center">
                                    <Segmented
                                        defaultValue={t("Regular")}
                                        onChange={(value) =>
                                            setAlignValue(value as Align)
                                        }
                                        options={[t("warm_up"), t("Regular")]}
                                        className={`bg-[#D3E3E380] custom-segmented rounded-xl flex items-center p-1 text-[16px] font-[400] ${roboto.className}`}
                                    />
                                </div>
                            )}
                        {disabledGrades.includes(selectedGrade) && (
                            <button
                                className="rounded-xl shadow-inner w-[150px] h-[40px] text-[16px] px-2 bg-[#67A1A3] text-white flex justify-center items-center space-x-2"
                                onClick={() => {
                                    localStorage.setItem(
                                        "selectedGrade",
                                        selectedGrade
                                    );
                                    localStorage.setItem(
                                        "selectedLevel",
                                        selectedLevel
                                    );
                                    if (
                                        selectedGrade === "FIRST" ||
                                        selectedGrade === "SECOND"
                                    ) {
                                        localStorage.setItem(
                                            "selectedSubject",
                                            selectedSubject
                                        );
                                    }
                                    router.push("/knowledgeRally/addQuestion");
                                }}
                            >
                                <span className="flex">
                                    {t("Add Question")}
                                </span>
                            </button>
                        )}
                    </div>
                </div>
                <div className="flex justify-start items-center px-4 pt-3 space-x-3">
                    {(selectedGrade === "FIRST" ||
                        selectedGrade === "SECOND") && (
                        <Dropdown
                            overlay={subjectMenu}
                            trigger={["click"]}
                            placement="bottomLeft"
                            open={subjectDropdownVisible}
                            onOpenChange={setSubjectDropdownVisible}
                        >
                            <div className="flex items-center justify-center border-[1px] w-[130px] border-[#67A1A3] rounded-[10px] px-3 py-1 cursor-pointer">
                                <span
                                    className={`text-[20px] font-[500] ${roboto.className}`}
                                >
                                    {
                                        forthGradeSubjects.find(
                                            (level) =>
                                                level.value === selectedSubject
                                        )?.label
                                    }
                                </span>

                                <Image
                                    src={
                                        subjectDropdownVisible
                                            ? "/images/arrow-up.svg"
                                            : "/images/arrow-bottom.svg"
                                    }
                                    alt="toggle dropdown"
                                    width={30}
                                    height={30}
                                    className="cursor-pointer pl-[9px]"
                                />
                            </div>
                        </Dropdown>
                    )}
                    <Dropdown
                        overlay={levelMenu}
                        trigger={["click"]}
                        placement="bottomLeft"
                        open={levelDropdownVisible}
                        onOpenChange={setLevelDropdownVisible}
                    >
                        <div className="flex items-center justify-center border-[1px] w-[130px] border-[#67A1A3] rounded-[10px] px-3 py-1 cursor-pointer">
                            <span
                                className={`text-[20px] font-[500] ${roboto.className}`}
                            >
                                {getLevelLabel(selectedLevel)}
                            </span>

                            <Image
                                src={
                                    levelDropdownVisible
                                        ? "/images/arrow-up.svg"
                                        : "/images/arrow-bottom.svg"
                                }
                                alt="toggle dropdown"
                                width={30}
                                height={30}
                                className="cursor-pointer pl-[9px]"
                            />
                        </div>
                    </Dropdown>
                </div>
                {isTabledataLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-180px)] w-full">
                        <Spin size="default" />
                    </div>
                ) : (
                    <div className="mt-2 w-[100%]">
                        <DndContext
                            modifiers={[restrictToVerticalAxis]}
                            onDragEnd={handleDragEnd}
                        >
                            <SortableContext
                                // items={data.map((item) => item.id)}
                                items={data
                                    .map((item) => item.id)
                                    .filter((id): id is string => !!id)}
                                strategy={verticalListSortingStrategy}
                            >
                                <Table
                                    columns={
                                        disabledGrades.includes(
                                            selectedGrade
                                        ) ||
                                        localStorage.getItem("userId") ===
                                            "7744a84d-4ef4-4bbe-a633-e97337faf51c"
                                            ? questionsByLevels
                                            : questionsDisableGradeByLevels
                                    }
                                    dataSource={data}
                                    pagination={{
                                        ...paginationConfig,
                                        className:
                                            "custom-pagination custom-select",
                                    }}
                                    bordered={false}
                                    rowKey="id"
                                    className={`custom-table ${
                                        data.length > 5
                                            ? "custom-table-scroll"
                                            : "custom-table-scroll-hide"
                                    }  scrollbar font-[400]`}
                                    rowClassName="custom-table-row"
                                    components={
                                        selectedGrade
                                            ? {
                                                  body: {
                                                      row: DraggableBodyRow,
                                                  },
                                              }
                                            : undefined
                                    }
                                    // loading={isTabledataLoading}
                                    onRow={(record) => {
                                        return {
                                            onClick: () => {
                                                localStorage.setItem(
                                                    "selectedGrade",
                                                    selectedGrade
                                                );
                                                localStorage.setItem(
                                                    "selectedLevel",
                                                    selectedLevel
                                                );
                                                if (
                                                    selectedGrade === "FIRST" ||
                                                    selectedGrade === "SECOND"
                                                ) {
                                                    localStorage.setItem(
                                                        "selectedSubject",
                                                        selectedSubject
                                                    );
                                                }
                                                localStorage.setItem(
                                                    "mode",
                                                    "view"
                                                );
                                                router.push(
                                                    `/knowledgeRally/addQuestion?id=${record.id}`
                                                );
                                            },
                                        };
                                    }}
                                    scroll={{
                                        y: "61vh",
                                    }}
                                    locale={{
                                        emptyText: (
                                            <div
                                                className={`h-[61vh] flex items-center justify-center font-roboto font-[500] text-gray-500`}
                                            >
                                                <Empty
                                                    description="No data available"
                                                    image={
                                                        Empty.PRESENTED_IMAGE_SIMPLE
                                                    }
                                                />
                                            </div>
                                        ),
                                    }}
                                />
                            </SortableContext>
                        </DndContext>
                        {data && data.length === 0 && (
                            <div
                                className={`h-[61vh] flex items-center justify-center font-roboto font-[500] text-gray-500`}
                            >
                                <Empty
                                    description={t("No data available")}
                                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                                />
                            </div>
                        )}
                    </div>
                )}
            </div>

            <Modal
                open={isDeleteModalVisible}
                onCancel={() => setIsDeleteModalVisible(false)}
                footer={null}
                centered
                width={383}
                style={{ textAlign: "center" }}
                closable={false}
            >
                <Image
                    src="/images/modalDelete.svg"
                    alt="Delete"
                    width={100}
                    height={100}
                    className="mx-auto bg-white -mt-3"
                />

                <div className=" flex justify-center items-center w-full">
                    <h2 className="text-[16px] font-roboto font-[400] mb-8 w-[80%] flex justify-center items-center">
                        {t("Are you sure you want to delete this question?")}
                    </h2>
                </div>
                <div className="flex justify-center space-x-4">
                    <Button
                        onClick={() => setIsDeleteModalVisible(false)}
                        className="bg-transparent cancelbutton font-roboto text-[#F1676D] text-[18px] h-[46px] border-[1px] border-[#F1676D] w-[116px] font-medium rounded-xl"
                    >
                        {t("cancel")}
                    </Button>
                    <Button
                        type="primary"
                        className="bg-[#F1676D] shadow-inner deleteButton font-roboto text-white text-[18px] w-[116px] h-[46px] font-medium rounded-xl"
                        onClick={() => handleDeleteConfirm(recordToDelete)}
                        loading={deleteQueation}
                    >
                        {t("delete")}
                    </Button>
                </div>
            </Modal>
        </div>
    );
};

export default KnowledgeRally;

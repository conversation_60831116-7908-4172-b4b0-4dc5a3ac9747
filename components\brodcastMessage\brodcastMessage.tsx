"use client";
import React, { useState, useEffect } from "react";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import "@/app/globals.css";
import Image from "next/image";
import {
    Table,
    Spin,
    Button,
    message,
    MenuProps,
    Radio,
    Dropdown,
    Modal,
    Space,
    Select,
    Input,
    Empty,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import {
    getSubscribedEmails,
    QueryParams,
    downloadSubscribedEmails,
} from "@/src/services/users.api";
import { broadcastType } from "@/src/libs/constants";
import {
    brodcastMessageParam,
    brodcastMessageBody,
    getAllBrodcastMessage,
    toSendBrodcastMessage,
} from "@/src/services/brodcastMessage.api";
import { Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";
import broadcastStyles from "./broadcast.module.css";
import TextArea from "antd/es/input/TextArea";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const { Option } = Select;

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface broadcastMessages {
    body: string;
    id: string;
    broadcastType: string;
}

const modules = {
    toolbar: [
        ["bold", "italic", "underline", "strike"],
        // [
        //     { list: "ordered" },
        //     { list: "bullet" },
        //     { indent: "-1" },
        //     { indent: "+1" },
        // ],
        ["link"],
        ["clean"],
    ],
};

const BrodcastMessage = () => {
    const [isButtonLoading, setButtonIsLoading] = useState(false);
    const [data, setData] = useState<broadcastMessages[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [response, setResponse] = useState<any>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [isTabledataLoading, setIsabledataLoading] = useState(false);
    const [selectedFilterType, setSelectedFilterType] = useState("ALL");
    const [pageSize, setPageSize] = useState<number>(20);
    const [dropdownVisible, setDropdownVisible] = useState<boolean>(false);
    const [isSendModalisible, setIsSendModalisible] = useState(false);
    const [messageSendLoading, setMessageSendLoading] = useState(false);
    const [selectedBroadcastType, setSelectedBroadcastType] = useState<any>();
    const [messageBody, setMessageBody] = useState("");
    const [title, setTitle] = useState("");
    const { t } = useTranslation();

    const router = useRouter();

    useEffect(() => {
        fetchData(currentPage);
    }, [currentPage, pageSize, selectedFilterType]);

    const fetchData = async (page: number = 1) => {
        const authorization = localStorage.getItem("idToken");

        try {
            setIsabledataLoading(true);
            const queryParams: brodcastMessageParam = {
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: "createdAt|desc",
                filter: selectedFilterType,
            };
            const response = await getAllBrodcastMessage(
                queryParams,
                authorization
            );
            setResponse(response);
            if (response) {
                const updatedData = response.list.map(
                    (item: any, index: any) => ({
                        ...item,
                        srNo: (page - 1) * pageSize + index + 1,
                    })
                );
                setData(updatedData);
                setTotal(response.total);
            } else {
                message.error(t("Empty response data"));
            }
        } catch (error) {
            message.error(t("Data could not be retrieved"));
        }
        setIsabledataLoading(false);
    };

    const handleSendMessage = async () => {
        const sanitizeMessageBody = (body: string) => {
            return body
                .replace(/^(<p><br><\/p>|<p>\s*<\/p>)+/, "") // Trim blank tags at the start
                .replace(/(<p><br><\/p>|<p>\s*<\/p>)+$/, ""); // Trim blank tags at the end
        };

        const sanitizedMessageBody = sanitizeMessageBody(messageBody);

        if (
            title === "" &&
            (selectedBroadcastType === null ||
                selectedBroadcastType === undefined) &&
            sanitizedMessageBody === ""
        ) {
            message.warning(t("Add details for the required fields"));
            return;
        }

        if (title === "") {
            message.warning(t("Title for message should not be empty"));
            return;
        }

        if (
            selectedBroadcastType === null ||
            selectedBroadcastType === undefined
        ) {
            message.warning(t("Broadcast Type should not be empty"));
            return;
        }

        if (sanitizedMessageBody === "") {
            message.warning(t("Message should not be empty"));
            return;
        }

        try {
            setMessageSendLoading(true);
            const payload: brodcastMessageBody = {
                title: title,
                broadcastType: selectedBroadcastType,
                body: sanitizedMessageBody,
            };
            const authorization = localStorage.getItem("idToken");

            const res = await toSendBrodcastMessage(payload, authorization);
            if (res) {
                setIsSendModalisible(false);
                setMessageBody("");
                setSelectedBroadcastType(null);
                setTitle("");
                fetchData(currentPage);
                setMessageSendLoading(false);
                message.success(t("Message send successfully"));
            }
        } catch (error) {
            message.error(t("Having somthing issue when sending the message"));
            setIsSendModalisible(true);
            setMessageSendLoading(false);
        }
    };

    const handleItemClick = (key: any) => {
        setSelectedFilterType(key);
        setDropdownVisible(false);
    };

    const handleRadioChange = (e: any) => {
        setSelectedFilterType(e);
        setDropdownVisible(false);
    };

    const items: MenuProps["items"] = broadcastType.map((item) => ({
        key: item.value,
        label: (
            <div
                className="flex justify-between items-center"
                onClick={() => handleItemClick(item.value)}
            >
                <span
                    className={`text-[14px] font-roboto font-medium mr-[50px] ${
                        selectedFilterType === item.value
                            ? "text-black"
                            : "text-gray-500"
                    }`}
                >
                    {item.label}
                </span>
                <Radio
                    value={item.value}
                    checked={selectedFilterType === item.value}
                    className={`custom-radio ${
                        selectedFilterType === item.value
                            ? "accent-[#67A1A3]"
                            : ""
                    }`}
                    onChange={() => handleRadioChange(item.value)}
                />
            </div>
        ),
    }));

    const subscribedEmailDataColumns: ColumnsType<broadcastMessages> = [
        {
            title: <div className="ml-2">{t("Sr. No.")}</div>,
            dataIndex: "srNo",
            key: "srNo",
            align: "left",
            className: `${roboto.className}`,
            width: 80,
            render: (srNo) => <div className="pl-[18px]">{srNo}.</div>,
        },
        {
            title: "Title",
            dataIndex: "title",
            key: "title",
            align: "center",
            className: `${roboto.className}`,
            width: "150px",
            render: (email) => (
                <div className="flex justify-center ">
                    <p className="text-center">{email}</p>
                </div>
            ),
        },
        {
            title: "Message",
            dataIndex: "body",
            key: "body",
            align: "center",
            className: `${roboto.className}`,
            render: (email) => {
                const stripHtml = (html: any) => {
                    const doc = new DOMParser().parseFromString(
                        html,
                        "text/html"
                    );
                    return doc.body.textContent || "";
                };
                return (
                    <div className="px-[20px] flex justify-start">
                        <p className="text-left">{stripHtml(email)}</p>
                    </div>
                );
            },
        },
        {
            title: <div className="text-center">Broadcast Type</div>,
            dataIndex: "broadcastType",
            key: "broadcastType",
            align: "left",
            width: "200px",
            render: (broadcastType: string) => (
                <div className="flex justify-center items-center">
                    <p className="text-center w-[200px]">
                        {" "}
                        {broadcastType === "ALL"
                            ? "All"
                            : broadcastType === "PARENT"
                            ? "Parent"
                            : "Child"}
                    </p>
                </div>
            ),
            className: `${roboto.className}`,
        },
    ];

    const paginationConfig = {
        pageSize: pageSize,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    const handleChange = (value: string) => {
        setMessageBody(value);
    };

    const handleBlur = () => {
        setMessageBody((prev) => prev.trim());
    };

    const handleCancel = () => {
        setIsSendModalisible(false);
        setMessageBody("");
        setSelectedBroadcastType(null);
        setTitle("");
    };

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`${roboto.className} bg-white text-[24px] text-black items-center mt-3`}
            >
                <>
                    <div className="flex justify-between items-center px-4 mb-4">
                        <div>
                            <div className="flex items-center">
                                <div
                                    className={`flex items-center text-[23px] font-[600] ${roboto.className}`}
                                >
                                    {t("Broadcast Message")}
                                </div>
                                <span className="text-gray-400 font-semibold text-[18px] font-roboto ml-1 mt-[2px]">
                                    ({total})
                                </span>
                                <Dropdown
                                    menu={{ items }}
                                    trigger={["click"]}
                                    placement="bottomLeft"
                                    open={dropdownVisible}
                                    onOpenChange={setDropdownVisible}
                                >
                                    <Image
                                        src={
                                            dropdownVisible
                                                ? "/images/arrow-up.svg"
                                                : "/images/arrow-bottom.svg"
                                        }
                                        alt="toggle dropdown"
                                        width={30}
                                        height={30}
                                        className="cursor-pointer pl-[9px]"
                                    />
                                </Dropdown>
                            </div>
                            <div className="text-black text-[16px] font-normal">
                                {selectedFilterType === "ALL"
                                    ? "All"
                                    : selectedFilterType === "PARENT"
                                    ? "Parent"
                                    : "Child"}{" "}
                            </div>
                        </div>
                        <div className="text-[25px] text-black flex items-center space-x-3">
                            <Button
                                type="primary"
                                size="large"
                                onClick={() => setIsSendModalisible(true)}
                                style={{ backgroundColor: "#67A1A3" }}
                                className="w-[220px] h-[40px] font-medium font-roboto bg-[#67A1A3] text-white shadow-inner"
                                loading={isButtonLoading}
                            >
                                {t("Send Broadcast Message")}
                            </Button>
                        </div>
                    </div>
                </>
                {isTabledataLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-180px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <div className="">
                        <Table
                            columns={subscribedEmailDataColumns}
                            dataSource={data}
                            pagination={{
                                ...paginationConfig,
                                className: "custom-pagination custom-select",
                            }}
                            bordered={false}
                            rowKey="srNo"
                            className={`custom-table font-[400] ${
                                data.length > 5
                                    ? "custom-table-scroll"
                                    : "custom-table-scroll-hide"
                            }`}
                            loading={{
                                indicator: <Spin size="default" />,
                                spinning: isTabledataLoading,
                            }}
                            scroll={{
                                y: "66vh",
                            }}
                            locale={{
                                emptyText: (
                                    <div
                                        className={`h-[61vh] flex items-center justify-center font-roboto font-[500]`}
                                    >
                                        <Empty
                                            description={t("No data available")}
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        />
                                    </div>
                                ),
                            }}
                        />
                    </div>
                )}
            </div>
            <Modal
                title={
                    <div
                        className={`text-black text-[22px] font-[500] ${roboto.className}`}
                    >
                        Send Broadcast Message
                    </div>
                }
                open={isSendModalisible}
                onCancel={() => setIsSendModalisible(false)}
                footer={null}
                className={`rounded-lg border-black w-[651px] h-auto ${broadcastStyles.modal}`}
                centered
                width={651}
                style={{ textAlign: "center" }}
                closable={false}
                maskClosable={false}
            >
                {/* {modalDataLoading ? (
                    <div className="flex justify-center items-center h-[316px]">
                        <Spin size="default" />
                    </div>
                ) : ( */}
                <>
                    <Space
                        direction="vertical"
                        style={{ width: "100%" }}
                        className="mt-2"
                    >
                        <div className="flex justify-between items-center w-full">
                            <label className="text-[16px] flex justify-start font-roboto font-semibold w-[150px]">
                                Title
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>{" "}
                                <span className="ml-1">:</span>
                            </label>
                            <Input
                                placeholder="Enter Tiltle"
                                value={title}
                                onChange={(e) => setTitle(e.target.value)}
                                onBlur={() => setTitle(title.trim())}
                                className="text-[14px] border-[#D3E3E3] bg-[#F6FAF9] font-roboto w-[495px] h-[40px]"
                            />
                        </div>
                        <div className="flex justify-between items-center w-full">
                            <label className="text-[16px] flex justify-start font-roboto font-semibold w-[150px]">
                                {t("Broadcast Type")}
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>{" "}
                                <span className="ml-1">:</span>
                            </label>
                            <Select
                                placeholder={t("Select Broadcast Type")}
                                value={selectedBroadcastType}
                                onChange={(value) => {
                                    setSelectedBroadcastType(value);
                                }}
                                suffixIcon={
                                    <Image
                                        src="/images/arrowI.svg"
                                        alt="More"
                                        width={20}
                                        height={20}
                                    />
                                }
                                className="custom-select align-start text-[16px] font-roboto font-[400] w-[495px] h-[40px]"
                            >
                                {broadcastType.map((broadcast) => (
                                    <Option
                                        key={broadcast.value}
                                        value={broadcast.value}
                                    >
                                        {broadcast.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex justify-start items-start w-full">
                            <label className="text-[16px] flex justify-start font-roboto font-semibold w-[150px]">
                                {t("Message")}
                                <span className="text-red-500 font-[400]">
                                    *
                                </span>{" "}
                                <span className="ml-1">:</span>
                            </label>
                            <ReactQuill
                                className={`${roboto.className} customQuill rounded-md font-roboto font-normal w-[495px] h-[250px] border-[#D3E3E3] bg-[#F6FAF9]`}
                                style={{
                                    border: "1px solid #D3E3E3",
                                    borderRadius: "0.375rem",
                                    backgroundColor: "#F6FAF9",
                                    fontFamily: "Roboto",
                                }}
                                value={messageBody}
                                onChange={(value: any) => handleChange(value)}
                                onBlur={handleBlur}
                                placeholder={t("Enter Message Description")}
                                modules={modules}
                            />
                        </div>
                    </Space>

                    <div className="pt-16 flex justify-center space-x-4">
                        <Button
                            onClick={handleCancel}
                            className={`${broadcastStyles.button} bg-transparent font-roboto text-[#67A1A3] border-[1px] border-[#67A1A3] text-[18px] w-[287px] h-[45px] font-medium rounded-xl`}
                        >
                            {t("cancel")}
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                handleSendMessage();
                            }}
                            className={`${broadcastStyles.button} bg-[#67A1A3] shadow-inner font-roboto text-white text-[18px] w-[287px] h-[45px] font-medium rounded-xl`}
                            loading={messageSendLoading}
                        >
                            {t("Send")}
                        </Button>
                    </div>
                </>
                {/* )} */}
            </Modal>
        </div>
    );
};

export default BrodcastMessage;

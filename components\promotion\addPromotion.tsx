"use client";
import React, { useEffect, useState } from "react";
import SideBar from "@/app/sideBar/page";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { Input, Select, Button, message, Spin } from "antd";
import {
    promotionType,
    isPromotionSponser,
    promotionCurrency,
} from "@/src/libs/constants";
import {
    QueryParams,
    deleteUploadedFile,
    uploadFile,
} from "@/src/services/upload.api";
import {
    addPromotionQueryParams,
    promotionDetails,
    updatePromotionPostById,
    getPromotionPostById,
    addNewPromotionPost,
} from "@/src/services/promotion.api";
import { Dropdown, Menu } from "antd";
import { Roboto } from "next/font/google";
import { DatePicker } from "antd";
// import moment from "moment";
import moment, { Moment } from "moment";
import dayjs from "dayjs";
import TextArea from "antd/es/input/TextArea";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const { Option } = Select;

const AddPromotion = () => {
    const [title, setTitle] = useState("");
    const [place, setPlace] = useState<string | null>(null);
    const [type, setType] = useState(null);
    const [selectedSponsor, setSelectedSponsor] = useState(null);
    const [link, setLink] = useState("");
    const [description, setDescription] = useState("");
    const [loading, setLoading] = useState(false);
    const [currency, setCurrency] = useState(null);
    const [price, setPrice] = useState<number | null>(null);
    const [promotionImage, setPromotionImage] = useState<
        {
            label: string;
            id: number;
            value: string | null;
        }[]
    >([{ label: "image1", id: 1, value: null }]);
    const [isImageUploadingAndDelete, setIsImageUploadingAndDelete] =
        useState(false);
    const [startDate, setStartDate] = useState<any | null>(null);
    const [expirationDate, setExpirationDate] = useState<any | null>(null);
    const maxLength = 1000;
    const { t } = useTranslation();

    // Function to handle text input and ensure max length
    const handleChange = (
        e: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>
    ) => {
        const value = e.target.value;
        if (value.length <= maxLength) {
            setDescription(value);
        }
    };

    const handleBlur = () => {
        setDescription((prev) => prev.trim());
    };

    const disabledStartDate = (current: any) => {
        return current && current < moment().endOf("day");
    };

    const disabledEndDate = (current: any) => {
        return (
            current &&
            (current < moment().endOf("day") ||
                (startDate && current < startDate.endOf("day")))
        );
    };

    const handleStartDateChange = (date: Moment | null) => {
        setStartDate(date);
        if (expirationDate && date && expirationDate.isBefore(date, "day")) {
            setExpirationDate(null);
        }
    };

    const handleEndDateChange = (date: Moment | null) => {
        setExpirationDate(date);
    };

    const router = useRouter();

    const handleImageChange = async (
        e: React.ChangeEvent<HTMLInputElement>,
        index: number
    ) => {
        const file = e.target.files?.[0];
        if (file) {
            const trimmedFileName = file.name.replace(/\s+/g, "_");
            const queryParams: QueryParams = {
                fileName: trimmedFileName,
                fileType: "IMAGE",
                resourceType: "PROMOTION",
            };
            const authorization = localStorage.getItem("idToken");

            try {
                setIsImageUploadingAndDelete(true);
                const response = await uploadFile(queryParams, authorization);

                if (response && response.preSignedUrl) {
                    const url = response.preSignedUrl;
                    try {
                        await fetch(url, {
                            method: "PUT",
                            body: file,
                            headers: {
                                "Content-Type": file.type,
                            },
                        });

                        const newPromotionImages = [...promotionImage];
                        newPromotionImages[index].value = response.outPutUrl;
                        setPromotionImage(newPromotionImages);
                    } catch (uploadError) {
                        message.error(t("Image upload failed"));
                    }
                    setIsImageUploadingAndDelete(false);
                } else {
                    message.error(t("Invalid presigned URL response"));
                    setIsImageUploadingAndDelete(false);
                }
            } catch (apiError) {
                message.error(t("Failed to get presigned URL"));
                setIsImageUploadingAndDelete(false);
            }
        }
    };

    const deletePromotionImage = async (index: number) => {
        const imageUrl = promotionImage[index].value;

        if (imageUrl) {
            const queryParams: QueryParams = {
                fileName: imageUrl.split("/").pop(),
                fileType: "IMAGE",
                resourceType: "PROMOTION",
            };
            const authorization = localStorage.getItem("idToken");
            try {
                setIsImageUploadingAndDelete(true);
                await deleteUploadedFile(queryParams, authorization);
                const newPromotionImages = [...promotionImage];
                newPromotionImages[index] = {
                    ...newPromotionImages[index],
                    value: null,
                };
                setPromotionImage(newPromotionImages);
                setIsImageUploadingAndDelete(false);
            } catch (error) {
                message.error(t("Error deleting image"));
                setIsImageUploadingAndDelete(false);
            }
        }
    };

    const handleAddFromGallery = (index: number) => {
        const inputFileElement = document.getElementById(`inputFile${index}`);
        if (inputFileElement) {
            (inputFileElement as HTMLInputElement).click();
        }
    };

    const menu = (index: any) => (
        <Menu>
            <Menu.Item
                key="1"
                onClick={() => handleAddFromGallery(index)}
                className="font-roboto"
            >
                <div className={`flex text-[14px] ${roboto.className}`}>
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    {t("Add From gallery")}
                </div>
            </Menu.Item>
            {promotionImage?.[0]?.value !== null && (
                <Menu.Item
                    key="2"
                    onClick={() => deletePromotionImage(index)}
                    className="font-roboto"
                >
                    <div
                        className={`text-[#F1676D] flex text-[14px] ${roboto.className}`}
                    >
                        <Image
                            src="/images/trash.svg"
                            alt="visibility"
                            width={23}
                            height={23}
                            className="cursor-pointer mr-[7px] mb-1"
                        />
                        Delete this image
                    </div>
                </Menu.Item>
            )}
        </Menu>
    );

    const urlRegex =
        /((http|https):\/\/)?(www\.)?[a-zA-Z0-9@:%._\+~#?&//=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%._\+~#?&//=]*)/;

    const handleAddPromotionPost = async () => {
        setLoading(true);

        if (
            (title === null || title === "") &&
            type === null &&
            expirationDate === null &&
            startDate === null &&
            place === null
        ) {
            message.error(t("Fill all required field")); //Required field should not be empty
            setLoading(false);
            return;
        }

        if (title === null || title === "") {
            message.error(t("Promotion title should not be empty")); //Promotion title should not be empty
            setLoading(false);
            return;
        }

        if (type === null) {
            message.error(t("Type should not be empty")); //Type should not be empty
            setLoading(false);
            return;
        }

        if (expirationDate === null && startDate === null) {
            message.error(t("Start and expiration dates should not be empty"));
            setLoading(false);
            return;
        }

        if (expirationDate === null) {
            message.error(t("Expiration Date should not be empty")); //Expiration Date should not be empty

            setLoading(false);
            return;
        } else if (startDate === null) {
            message.error(t("Start date should not be empty"));
            setLoading(false);
            return;
        }

        if (place === null) {
            message.error(t("Place field should not be empty"));
            setLoading(false);
            return;
        }

        if (link === "") {
            message.error(t("Link should not be empty"));
            setLoading(false);
            return;
        }

        if (link.trim() === "" || !urlRegex.test(link)) {
            message.error(t("Provide a valid link URL"));
            setLoading(false);
            return;
        }

        const allImagesUploaded = promotionImage.every(
            (image) => image.value !== null
        );

        if (!allImagesUploaded) {
            message.error(t("Please upload an image for the promotion"));
            setLoading(false);
            return;
        }

        const payload: promotionDetails = {
            picture: promotionImage[0]?.value || "",
            title: title,
            type: type,
            currency: currency,
            price: price,
            sponsored: selectedSponsor,
            startingDate: startDate,
            expirationDate: expirationDate,
            place: place,
            websiteLink: link,
            description: description,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await addNewPromotionPost(payload, authorization);
            message.success(t("Promotion added successfully"));
            router.push("/promotion");
        } catch (error: any) {
            message.error(error.message || t("Failed to add the Promotion"));
        }
        setLoading(false);
    };

    const handleEditPromotionPost = async (postId: string) => {
        setLoading(true);

        if (
            (title === null || title === "") &&
            type === null &&
            expirationDate === null &&
            startDate === null &&
            place === null
        ) {
            message.error(t("Please enter required field"));
            setLoading(false);
            return;
        }

        if (title === null || title === "") {
            message.error(t("Promotion title should not be empty"));
            setLoading(false);
            return;
        }

        if (type === null) {
            message.error(t("Type should not be empty"));
            setLoading(false);
            return;
        }

        if (expirationDate === null && startDate === null) {
            message.error(t("Start and expiration dates should not be empty"));
            setLoading(false);
            return;
        }

        if (expirationDate === null) {
            message.error(t("Expiration Date should not be empty"));
            setLoading(false);
            return;
        } else if (startDate === null) {
            message.error(t("Start date should not be empty"));
            setLoading(false);
            return;
        }

        if (place === null || place === "") {
            message.error(t("Place field should not be empty"));
            setLoading(false);
            return;
        }

        if (link.trim() === "" || !urlRegex.test(link)) {
            message.error(t("Link should not be empty"));
            setLoading(false);
            return;
        }

        if (link === "") {
            message.error(t("Provide a valid link URL"));
            setLoading(false);
            return;
        }

        const allImagesUploaded = promotionImage.every(
            (image) => image.value !== null
        );

        if (!allImagesUploaded) {
            message.error(t("Please upload an image for the promotion"));
            setLoading(false);
            return;
        }

        const payload: promotionDetails = {
            picture: promotionImage[0]?.value || "",
            title: title,
            type: type,
            currency: currency,
            price: price,
            sponsored: selectedSponsor,
            startingDate: startDate,
            expirationDate: expirationDate,
            place: place,
            websiteLink: link,
            description: description,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await updatePromotionPostById(
                postId,
                payload,
                authorization
            );
            message.success(t("Promotion successfully edited"));
            router.push("/promotion");
        } catch (error: any) {
            message.error(error.message || t("Failed to edit the Promotion"));
        }
        setLoading(false);
    };

    const searchParams = useSearchParams();

    useEffect(() => {
        const id = searchParams.get("id");

        if (id) {
            const fetchPromotionData = async () => {
                const authorization = localStorage.getItem("idToken");
                const response = await getPromotionPostById(id, authorization);

                const updatedImages = [
                    {
                        label: "image1",
                        id: 1,
                        value: response.picture,
                    },
                ];

                const startDateForDatePicker = dayjs(response.startingDate);
                const endDateForDatePicker = dayjs(response.expirationDate);

                setPromotionImage(updatedImages);
                setTitle(response.title);
                setPlace(response.place);
                setCurrency(response.currency);
                setPrice(response.price);
                setStartDate(startDateForDatePicker);
                setExpirationDate(endDateForDatePicker);
                setType(response.type);
                setSelectedSponsor(response.sponsored);
                setDescription(response.description);
                setLink(response.websiteLink);
            };
            fetchPromotionData();
        }
    }, [searchParams]);

    return (
        <div className={`${roboto.className}`}>
            <div className="w-full shadow-md text-[25px] text-black font-bold h-auto flex justify-between items-center px-4 py-3 z-10 bg-white">
                <div>
                    <h1 className="text-[24px]">
                        {searchParams.get("mode") === "view"
                            ? t("View Promotion")
                            : searchParams.get("mode") === "edit"
                            ? t("Edit Promotion")
                            : t("Add Promotion")}
                    </h1>
                </div>
                <div className="text-[20px] font-roboto text-black flex items-center px-4">
                    <Image
                        src="/images/cross.svg"
                        alt="cross"
                        width={28}
                        height={28}
                        className="cursor-pointer mr-[10px]"
                        onClick={() => router.back()}
                    />
                </div>
            </div>
            <div className=" flex justify-start font-roboto w-full text-[18px] text-black font-semibold  px-4 z-0 bg-white overflow-y-auto h-[calc(100vh-100px)] custom-scroll mt-5">
                <div className="flex flex-col gap-4 w-[40%]">
                    <div className="h-[259px] w-full bg-[#D3E3E3] relative rounded-lg">
                        <Input
                            id="inputFile0"
                            type="file"
                            accept="image/*"
                            style={{ cursor: "pointer", display: "none" }}
                            onChange={(e) => handleImageChange(e, 0)}
                        />
                        {isImageUploadingAndDelete ? (
                            <div className="flex justify-center items-center w-full h-full absolute inset-0 z-10">
                                <Spin size="default" />
                            </div>
                        ) : (
                            promotionImage[0]?.value && (
                                <img
                                    src={promotionImage[0].value}
                                    alt="Uploaded"
                                    className="w-full h-full object-cover rounded-lg"
                                />
                            )
                        )}
                        {searchParams.get("mode") !== "view" && (
                            <Dropdown
                                overlay={menu(0)}
                                trigger={["click"]}
                                placement="bottomRight"
                            >
                                <Image
                                    src="/images/more.svg"
                                    alt="More"
                                    width={20}
                                    height={20}
                                    onClick={(e) => e.preventDefault()}
                                    className="absolute top-3 right-2 cursor-pointer"
                                    style={{ fontSize: "20px" }}
                                />
                            </Dropdown>
                        )}
                    </div>
                </div>
                <div className="ml-4 flex w-[60%] flex-col justify-between">
                    <div className={`${roboto.className}`}>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">
                                Title{" "}
                                {searchParams.get("mode") !== "view" && (
                                    <span className="text-red-500 font-[400]">
                                        *
                                    </span>
                                )}{" "}
                                :
                            </h1>
                            <Input
                                className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                type="text"
                                value={title}
                                onChange={(e) => setTitle(e.target.value)}
                                onBlur={() => setTitle(title.trim())}
                                placeholder={t("Enter Title")}
                                readOnly={searchParams.get("mode") === "view"}
                                style={{
                                    pointerEvents:
                                        searchParams.get("mode") === "view"
                                            ? "none"
                                            : "auto",
                                }}
                            />
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">
                                {t("Type")}
                                {searchParams.get("mode") !== "view" && (
                                    <span className="text-red-500 font-[400]">
                                        *
                                    </span>
                                )}{" "}
                                :
                            </h1>
                            <Select
                                className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[38vw] font-[400]"
                                placeholder={t("Select Type")}
                                value={type}
                                onChange={(value) => setType(value)}
                                onClick={(e) => {
                                    if (searchParams.get("mode") === "view") {
                                        e.preventDefault();
                                    }
                                }}
                                style={{
                                    pointerEvents:
                                        searchParams.get("mode") === "view"
                                            ? "none"
                                            : "auto",
                                }}
                                suffixIcon={
                                    searchParams.get("mode") !== "view" && (
                                        <Image
                                            src="/images/arrowI.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                        />
                                    )
                                }
                            >
                                {promotionType.map((option) => (
                                    <Option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">{t("Sponsored")} : </h1>
                            <Select
                                className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[38vw] font-[400]"
                                placeholder={t("Is Sponsored?")}
                                value={selectedSponsor}
                                onChange={(value) => setSelectedSponsor(value)}
                                onClick={(e) => {
                                    if (searchParams.get("mode") === "view") {
                                        e.preventDefault();
                                    }
                                }}
                                style={{
                                    pointerEvents:
                                        searchParams.get("mode") === "view"
                                            ? "none"
                                            : "auto",
                                }}
                                suffixIcon={
                                    searchParams.get("mode") !== "view" && (
                                        <Image
                                            src="/images/arrowI.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                        />
                                    )
                                }
                            >
                                {isPromotionSponser.map((option) => (
                                    <Option
                                        key={option.label}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">{t("Currency")} :</h1>
                            <Select
                                className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-[38vw] font-[400]"
                                placeholder={t("Select Type")}
                                value={currency}
                                onChange={(value) => setCurrency(value)}
                                onClick={(e) => {
                                    if (searchParams.get("mode") === "view") {
                                        e.preventDefault();
                                    }
                                }}
                                style={{
                                    pointerEvents:
                                        searchParams.get("mode") === "view"
                                            ? "none"
                                            : "auto",
                                }}
                                suffixIcon={
                                    searchParams.get("mode") !== "view" && (
                                        <Image
                                            src="/images/arrowI.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                        />
                                    )
                                }
                            >
                                {promotionCurrency.map((option) => (
                                    <Option
                                        key={option.value}
                                        value={option.value}
                                    >
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">{t("Price")} :</h1>
                            <Input
                                className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                type="text"
                                value={price !== null ? price : ""}
                                // onChange={(e) => setPrice(e.target.value)}
                                onChange={(e) => {
                                    const value = e.target.value;
                                    if (
                                        /^\d*$/.test(value) &&
                                        value.length <= 10
                                    ) {
                                        setPrice(
                                            value === "" ? null : Number(value)
                                        );
                                    }
                                }}
                                placeholder={t("Enter Price")}
                                readOnly={searchParams.get("mode") === "view"}
                                style={{
                                    pointerEvents:
                                        searchParams.get("mode") === "view"
                                            ? "none"
                                            : "auto",
                                }}
                            />
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">
                                {t("Starting Date")}
                                {searchParams.get("mode") !== "view" && (
                                    <span className="text-red-500 font-[400]">
                                        *
                                    </span>
                                )}{" "}
                                :
                            </h1>
                            <DatePicker
                                className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                disabledDate={disabledStartDate}
                                value={startDate}
                                onChange={handleStartDateChange}
                                format="DD/MM/YYYY"
                                placeholder={t("Select Starting Date")}
                                disabled={searchParams.get("mode") === "view"}
                                style={{
                                    pointerEvents:
                                        searchParams.get("mode") === "view"
                                            ? "none"
                                            : "auto",
                                }}
                            />
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">
                                {t("Expiration Date")}
                                {searchParams.get("mode") !== "view" && (
                                    <span className="text-red-500 font-[400]">
                                        *
                                    </span>
                                )}{" "}
                                :
                            </h1>
                            <DatePicker
                                className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                disabledDate={disabledEndDate}
                                value={expirationDate}
                                onChange={handleEndDateChange}
                                format="DD/MM/YYYY"
                                placeholder={t("Select Expiration Date")}
                                disabled={searchParams.get("mode") === "view"}
                                style={{
                                    pointerEvents:
                                        searchParams.get("mode") === "view"
                                            ? "none"
                                            : "auto",
                                }}
                            />
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">
                                {t("Place")}
                                {searchParams.get("mode") !== "view" && (
                                    <span className="text-red-500 font-[400]">
                                        *
                                    </span>
                                )}{" "}
                                :
                            </h1>
                            <Input
                                className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                type="text"
                                value={place !== null ? place : ""}
                                onChange={(e) => setPlace(e.target.value)}
                                onBlur={() =>
                                    setPlace(place ? place.trim() : "")
                                }
                                placeholder={t("Enter Place")}
                                readOnly={searchParams.get("mode") === "view"}
                                style={{
                                    pointerEvents:
                                        searchParams.get("mode") === "view"
                                            ? "none"
                                            : "auto",
                                }}
                            />
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">
                                Website Link
                                {searchParams.get("mode") !== "view" && (
                                    <span className="text-red-500 font-[400]">
                                        *
                                    </span>
                                )}{" "}
                                :
                            </h1>
                            <Input
                                className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                type="text"
                                value={link}
                                onChange={(e) => setLink(e.target.value)}
                                onBlur={() => setLink(link.trim())}
                                placeholder={t("Enter Website link")}
                                readOnly={searchParams.get("mode") === "view"}
                                style={{
                                    pointerEvents:
                                        searchParams.get("mode") === "view"
                                            ? "none"
                                            : "auto",
                                }}
                            />
                        </div>
                        <div className="flex flex-col mb-4">
                            <div className="flex items-center mb-2">
                                <h1 className="w-[150px]">
                                    {t("description")} :
                                </h1>
                                {/* <Input
                                    className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                    type="text"
                                    value={description}
                                    onChange={handleChange}
                                    placeholder="Enter Description"
                                    readOnly={
                                        searchParams.get("mode") === "view"
                                    }
                                    style={{
                                        pointerEvents:
                                            searchParams.get("mode") === "view"
                                                ? "none"
                                                : "auto",
                                    }}
                                /> */}
                                <TextArea
                                    className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-[38vw] border-[#D3E3E3] bg-[#F6FAF9]"
                                    value={description}
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                    placeholder={t("Enter Description")}
                                    onClick={(e) => {
                                        if (
                                            searchParams.get("mode") === "view"
                                        ) {
                                            e.preventDefault();
                                        }
                                    }}
                                    style={{
                                        pointerEvents:
                                            searchParams.get("mode") === "view"
                                                ? "none"
                                                : "auto",
                                    }}
                                />
                            </div>
                            {/* <div
                                className={`text-right text-[12px] text-[black] font-[400] ${roboto.className}`}
                            >
                                {maxLength - description.length}
                            </div> */}
                        </div>
                    </div>
                    <div className="flex justify-end">
                        {searchParams.get("mode") !== "view" && (
                            <>
                                {searchParams.get("mode") === "edit" ? (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] mr-2 shadow-inner button font-roboto text-white text-[18px] w-[180px] h-[46px] font-medium rounded-xl"
                                        onClick={() =>
                                            handleEditPromotionPost(
                                                searchParams.get("id") as string
                                            )
                                        }
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                ) : (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] mr-2 shadow-inner button font-roboto text-white text-[18px] w-[180px] h-[46px] font-medium rounded-xl"
                                        onClick={handleAddPromotionPost}
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AddPromotion;

import { auth } from "@/firebase.config";
import {
    signInWithEmailAndPassword,
    sendPasswordResetEmail,
    fetchSignInMethodsForEmail,
    updatePassword,
} from "firebase/auth";
import { fetch } from "@/src/libs/helpers";

export const signInWithFirebase = async (
    email: string,
    password: string
): Promise<any> => {
    const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
    );
    const idToken = await userCredential.user.getIdToken();
    return { userCredential, idToken };
};

export const forgotPasswordWithFirebase = async (
    email: string
): Promise<void> => {
    try {
        await sendPasswordResetEmail(auth, email);
        // console.log("Password reset email sent");
    } catch (error) {
        console.error("Error sending password reset email:", error);
        throw error;
    }
};

export const changePasswordWithFirebase = async (
    email: string,
    currentPassword: string,
    newPassword: string,
    confirmPassword: string
): Promise<any> => {
    if (newPassword !== confirmPassword) {
        throw new Error("New password and confirm password do not match");
    }
    const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        currentPassword
    );
    const user = userCredential.user;
    await updatePassword(user, newPassword);

    return { message: "Password updated successfully" };
};

export const authenticateWithAPI = async (
    email: string,
    idToken: string,
    fcmToken: string
): Promise<any> => {
    const authorization = `Bearer ${idToken}`;
    const authApiResponse = await fetch({
        url: "/auth",
        method: "POST",
        data: {
            username: email,
            type: "ADMIN",
            userPlatform: "WEB",
            notificationToken: fcmToken,
        },
        headers: {
            Authorization: authorization,
        },
    });
    return authApiResponse;
};

export const doLogout = async (
    sessionId: string | null,
    authorization: string | null
): Promise<any> => {
    const logoutApiResponse = await fetch({
        url: `/auth/${sessionId}/logout`,
        method: "PUT",
        headers: {
            Authorization: authorization,
        },
    });
    return logoutApiResponse;
};

"use client";
import React, { useEffect, useRef, useState } from "react";
import {
    Avatar,
    Dropdown,
    Menu,
    MenuProps,
    Modal,
    Input,
    Button,
    Space,
    message,
    Tooltip,
} from "antd";
import Image from "next/image";
import navbarStyles from "./navbar.module.css";
import { useRouter } from "next/navigation";
import { doLogout, changePasswordWithFirebase } from "@/src/services/auth.api";
import { Roboto } from "next/font/google";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";
import moment from "moment";
import { messaging } from "@/firebase.config";
import { deleteToken } from "firebase/messaging";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const Navbar = () => {
    const [selectedButton, setSelectedButton] = useState<string | null>(null);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);
    const [isLogoutModalVisible, setIsLogoutModalVisible] = useState(false);
    const [currentPassword, setCurrentPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [isloading, setIsLoading] = useState(false);
    const router = useRouter();
    const { t } = useTranslation();

    useEffect(() => {
        const session = localStorage.getItem("session");
        if (session === null) {
            router.push("/");
        }
    }, []);

    const firstRender = useRef<any>(false);

    useEffect(() => {
        const idToken = localStorage.getItem("idToken");
        if (idToken) {
            const time: any = localStorage.getItem("adminSessionTime");
            const sessionTime = JSON.parse(time);

            if (sessionTime) {
                const now = moment();
                const savedTime = moment(sessionTime);

                const diffInDays = now.diff(savedTime, "days");

                if (diffInDays > 2) {
                    if (!firstRender.current) {
                        forceLogout();
                        firstRender.current = true;
                    }
                }
            }
        }
    }, []);

    const forceLogout = async () => {
        try {
            setIsLoading(true);
            const sessionId = localStorage.getItem("session");
            const authorization = localStorage.getItem("idToken");

            const res = await doLogout(sessionId, authorization);
            if (res) {
                localStorage.clear();
                sessionStorage.clear();
                router.push("/");
                message.error(
                    "Session expired. Please log in again to continue."
                );
                setIsLoading(false);
                setIsLogoutModalVisible(false);
            }
        } catch (error) {
            console.error(`Logout failed`);
            setIsLoading(false);
        }
    };

    const [errors, setErrors] = useState({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
    });

    const handleButtonClick = (button: string) => {
        setSelectedButton(button);
        if (button === "Change_Password") {
            setIsModalVisible(true);
        } else if (button === "Log_out") {
            setIsLogoutModalVisible(true);
        }
    };

    const handelChangePassword = async () => {
        let validationErrors = {
            currentPassword: "",
            newPassword: "",
            confirmPassword: "",
        };
        let valid = true;

        if (!currentPassword) {
            validationErrors.currentPassword = t(
                "Current password is required"
            );
            valid = false;
        }
        if (!newPassword) {
            validationErrors.newPassword = t("New password is required");
            valid = false;
        } else if (newPassword.length < 8) {
            validationErrors.newPassword = t(
                "New password must be at least 8 characters"
            );
            valid = false;
        } else if (
            !/(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\W)(?!.*\s).{8,}/.test(
                newPassword
            )
        ) {
            validationErrors.newPassword = t(
                "Password must contain at least 1 uppercase letter, 1 lowercase letter, 1 digit, and 1 special character."
            );
            valid = false;
        }
        if (currentPassword === newPassword && currentPassword && newPassword) {
            validationErrors.newPassword = t(
                "New password should not be same as current password"
            ); // New password should not be same as current password
            valid = false;
        }
        if (!confirmPassword) {
            validationErrors.confirmPassword = t(
                "Confirm password is required"
            ); // Confirm password is required
            valid = false;
        } else if (newPassword !== confirmPassword) {
            validationErrors.confirmPassword = t("Password does not match"); // Password does not match
            valid = false;
        }

        setErrors(validationErrors);

        if (valid) {
            try {
                setIsLoading(true);
                const email = localStorage.getItem("email");
                if (!email) {
                    throw new Error(t("Email not found in localStorage")); // Email not found in localStorage
                }
                const changePassRes = await changePasswordWithFirebase(
                    email,
                    currentPassword,
                    newPassword,
                    confirmPassword
                );

                if (changePassRes) {
                    setIsModalVisible(false);
                    setIsSuccessModalVisible(true);
                }
                // const sessionId = localStorage.getItem("session");
                // const authorization = localStorage.getItem("idToken");
                // await doLogout(sessionId, authorization);
                // router.push("/");
            } catch (err) {
                if (err instanceof Error) {
                    setErrors((prevErrors) => ({
                        ...prevErrors,
                        currentPassword: t("Current Password does not match"), // Current Password does not match
                    }));
                } else {
                    setErrors((prevErrors) => ({
                        ...prevErrors,
                        currentPassword: t("An unknown error occurred"), // An unknown error occurred
                    }));
                }
            } finally {
                setIsLoading(false);
            }
        }
    };

    const handleCancel = () => {
        setIsModalVisible(false);
        setCurrentPassword("");
        setNewPassword("");
        setConfirmPassword("");
        setErrors({
            currentPassword: "",
            newPassword: "",
            confirmPassword: "",
        });
    };

    const handelChangePasswordSuccess = async () => {
        setIsSuccessModalVisible(false);
        const sessionId = localStorage.getItem("session");
        const authorization = localStorage.getItem("idToken");
        const res = await doLogout(sessionId, authorization);
        if (res) {
            router.push("/");
            localStorage.clear();
            sessionStorage.clear();
        }
    };

    const handleLogout = async () => {
        try {
            setIsLoading(true);
            const sessionId = localStorage.getItem("session");
            const authorization = localStorage.getItem("idToken");

            const response = await doLogout(sessionId, authorization);
            if (response) {
                router.push("/");
                localStorage.clear();
                sessionStorage.clear();
                const res = await deleteToken(messaging);
                message.success(t("Logged out successfully"));
                setIsLoading(false);
            }
        } catch (error) {
            forceLogout();
            setIsLoading(false);
        }
    };

    const handleLogoutCancel = () => {
        setIsLogoutModalVisible(false);
    };

    const items: MenuProps["items"] = [
        {
            key: "1",
            label: (
                <div
                    className={`flex ${roboto.className} items-center px-[20px] py-[10px] w-[210px] rounded hover:bg-white hover:bg-opacity-10`}
                    onClick={() => handleButtonClick("Change_Password")}
                >
                    <Image
                        src="/images/sideBar/Edit.svg"
                        alt="Change_Password"
                        width={20}
                        height={20}
                        className="mr-[5px] -ml-[1px]"
                    />
                    <span className="text-black font-bold text-[15px]">
                        {t("Change Password")}
                    </span>
                </div>
            ),
        },
        {
            key: "2",
            label: (
                <div
                    className={`flex items-center ${roboto.className} px-[20px] py-[10px] w-[210px] rounded hover:bg-white hover:bg-opacity-10`}
                    onClick={() => handleButtonClick("Log_out")}
                >
                    <Image
                        src="/images/sideBar/log out b.svg"
                        alt="Log_out"
                        width={15}
                        height={15}
                        className="mr-[10px]"
                    />
                    <span className="text-black font-bold text-[15px]">
                        {t("logout")}
                    </span>
                </div>
            ),
        },
    ];

    const userName = localStorage.getItem("username");
    const profilePic = localStorage.getItem("profilePic");

    const formatUsername = (username: any) => {
        if (userName) {
            const atIndex = username.indexOf("@");
            if (atIndex !== -1) {
                const trimmedName = username.substring(0, atIndex);
                return (
                    trimmedName.charAt(0).toUpperCase() + trimmedName.slice(1)
                );
            } else {
                return username.charAt(0).toUpperCase() + username.slice(1);
            }
        }
    };

    return (
        <div
            className={`${roboto.className} flex justify-between items-center h-auto px-4 py-3`}
            style={{ boxShadow: "0px 0px 8px 0px rgba(0, 0, 0, 0.15)" }}
        >
            <div
                className={`bg-white w-full text-[24px] text-black flex items-center font-[600] ${roboto.className}`}
            >
                {t("Welcome back,")} {userName ? formatUsername(userName) : "-"}
                !
            </div>
            <div
                className={`bg-white text-[20px] text-black flex items-center px-4 font-[500] ${roboto.className}`}
            >
                <Dropdown menu={{ items }} trigger={["click"]}>
                    <Tooltip title="Profile">
                        <Avatar
                            size="large"
                            className="cursor-pointer border-none"
                        >
                            {profilePic && profilePic !== "null" ? (
                                <Image
                                    src={profilePic}
                                    alt="Profile Picture"
                                    layout="fill"
                                    objectFit="cover"
                                />
                            ) : (
                                <span
                                    className={`${roboto.className} text-[25px]`}
                                >
                                    {userName
                                        ? formatUsername(userName)[0]
                                        : "-"}
                                </span>
                            )}
                        </Avatar>
                    </Tooltip>
                </Dropdown>
            </div>

            <Modal
                open={isModalVisible}
                onCancel={handleCancel}
                footer={null}
                className={`rounded-lg border-black ${navbarStyles.modal}  ${roboto.className}`}
                centered
                width={383}
                style={{ textAlign: "center" }}
                closable={false}
            >
                <h2 className={`text-[28px] ${roboto.className} font-bold`}>
                    {t("Change Password")}
                </h2>
                <Space
                    direction="vertical"
                    style={{ width: "100%" }}
                    className="mt-10"
                >
                    <Input.Password
                        placeholder={t("current_password")}
                        value={currentPassword}
                        onChange={(e) => {
                            setCurrentPassword(e.target.value);
                            setErrors({ ...errors, currentPassword: "" });
                        }}
                        iconRender={(visible) => (
                            <Image
                                src={`${
                                    visible
                                        ? "/images/eye.svg"
                                        : "/images/eye-slash.svg"
                                }`}
                                alt="visibility"
                                width={22}
                                height={22}
                                style={{ cursor: "pointer" }}
                            />
                        )}
                        className={`text-[16px] border-[#A3CBC1] bg-[#F6FAF9] ${roboto.className} font-[500] h-[50px] custom-password-field `}
                        style={{ fontFamily: "Roboto" }}
                    />
                    {errors.currentPassword && (
                        <div
                            className={`text-red-500 ${roboto.className} text-sm text-start`}
                        >
                            {errors.currentPassword}
                        </div>
                    )}
                    <Input.Password
                        placeholder={t("new_password")}
                        value={newPassword}
                        onChange={(e) => {
                            setNewPassword(e.target.value);
                            setErrors({ ...errors, newPassword: "" });
                        }}
                        iconRender={(visible) => (
                            <Image
                                src={`${
                                    visible
                                        ? "/images/eye.svg"
                                        : "/images/eye-slash.svg"
                                }`}
                                alt="visibility"
                                width={22}
                                height={22}
                                style={{ cursor: "pointer" }}
                            />
                        )}
                        className={`text-[16px] border-[#A3CBC1] bg-[#F6FAF9]  h-[50px] ${roboto.className} font-[500] h-[50px] custom-password-field`}
                    />
                    {errors.newPassword && (
                        <div
                            className={`text-red-500 ${roboto.className} text-[14px] text-start`}
                        >
                            {errors.newPassword}
                        </div>
                    )}
                    <Input.Password
                        placeholder={t("confirm_password")}
                        value={confirmPassword}
                        onChange={(e) => {
                            setConfirmPassword(e.target.value);
                            setErrors({ ...errors, confirmPassword: "" });
                        }}
                        iconRender={(visible) => (
                            <Image
                                src={`${
                                    visible
                                        ? "/images/eye.svg"
                                        : "/images/eye-slash.svg"
                                }`}
                                alt="visibility"
                                width={22}
                                height={22}
                                style={{ cursor: "pointer" }}
                            />
                        )}
                        className={`text-[16px] border-[#A3CBC1] bg-[#F6FAF9] ${roboto.className} h-[50px] font-[500] custom-password-field`}
                        style={{ fontFamily: "Roboto" }}
                    />
                    {errors.confirmPassword && (
                        <div
                            className={`text-red-500 ${roboto.className} text-[14PX] text-start`}
                        >
                            {errors.confirmPassword}
                        </div>
                    )}
                </Space>
                <div className="mt-10 flex justify-center space-x-4 w-full">
                    <Button
                        onClick={handleCancel}
                        className={`${navbarStyles.button} bg-transparent ${roboto.className} text-[#67A1A3] border-[1px] border-[#67A1A3] text-[18px] w-[50%] h-[46px] font-medium rounded-xl`}
                    >
                        {t("cancel")}
                    </Button>
                    <Button
                        type="primary"
                        onClick={handelChangePassword}
                        className={`${navbarStyles.button} bg-[#67A1A3] shadow-inner ${roboto.className} text-white text-[18px] w-[50%] h-[46px] font-medium rounded-xl`}
                        loading={isloading}
                    >
                        {t("save")}
                    </Button>
                </div>
            </Modal>
            <Modal
                open={isSuccessModalVisible}
                onOk={handelChangePasswordSuccess}
                footer={null}
                className={`rounded-lg border-black ${navbarStyles.modal}`}
                centered
                width={280}
                style={{ textAlign: "center", border: "black" }}
                closable={false}
            >
                <Image
                    src="/images/thumbs.svg"
                    alt="Success"
                    width={70}
                    height={70}
                    className="mx-auto w-[120px] h-[120px]"
                />
                <p className={`text-[16px] ${roboto.className} font-medium`}>
                    {t("your_password_has_been_changed")}
                </p>
                <Button
                    type="primary"
                    onClick={handelChangePasswordSuccess}
                    className={`${navbarStyles.button} bg-[#67A1A3] shadow-inner ${roboto.className} text-white text-[18px] w-full h-[46px] font-medium rounded-xl mt-5`}
                >
                    {t("back_to_login")}
                </Button>
            </Modal>
            <Modal
                open={isLogoutModalVisible}
                onCancel={handleLogoutCancel}
                footer={null}
                className={`rounded-lg border-black ${navbarStyles.modal}`}
                centered
                width={320}
                style={{ textAlign: "center" }}
                closable={false}
            >
                <Image
                    src="/images/logOutIcon.svg"
                    alt="Success"
                    width={100}
                    height={100}
                    className="mx-auto w-[100px] h-[100px] -mt-3"
                />

                <div className=" flex justify-center items-center w-full">
                    <h2
                        className={`text-[16px] ${roboto.className} font-[400] mb-8 w-[80%] flex justify-center items-center`}
                    >
                        {t("are_you_sure_you_want_to_logout")}
                    </h2>
                </div>
                <div className="flex justify-center space-x-4">
                    <Button
                        onClick={handleLogoutCancel}
                        className={`${navbarStyles.button} bg-transparent ${roboto.className} text-[#67A1A3] text-[18px] h-[46px] border-[1px] border-[#67A1A3] w-[136px] font-medium rounded-xl`}
                    >
                        {t("cancel")}
                    </Button>
                    <Button
                        type="primary"
                        className={`${navbarStyles.button} bg-[#67A1A3] shadow-inner ${roboto.className} text-white text-[18px] w-[136px] h-[46px] font-medium rounded-xl`}
                        onClick={handleLogout}
                        loading={isloading}
                    >
                        {t("logout")}
                    </Button>
                </div>
            </Modal>
        </div>
    );
};

export default Navbar;

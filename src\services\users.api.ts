import { fetch } from "@/src/libs/helpers";

export interface QueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    search_column?: String | string[];
    order?: string;
    startDate?: string;
    endDate?: string;
}

export const getParentUserdata = async (
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/user/parent-user?search_column=email&search_column=username",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getChilUserdata = async (
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/user/child-user?include=parentChildRelation.parent&search_column=parentChildRelation.parent.username&search_column=name",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getChilUserdataV2 = async (
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/user/child-user/v2",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const changeUserStatus = async (
    // queryParams: QueryParams,
    authorization: string | null,
    userId: any
): Promise<any> => {
    return fetch({
        url: `/user/${userId}`,
        method: "PATCH",
        // params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const getSubscribedEmails = async (
    queryParams: QueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: "/user/saved-emails",
        method: "GET",
        params: queryParams,
        headers: {
            Authorization: authorization,
        },
    });
};

export const downloadSubscribedEmails = async (
    authorization: string | null,
    queryParams: QueryParams
): Promise<any> => {
    return fetch({
        url: "/email-csv/download",
        method: "GET",
        headers: {
            Authorization: authorization,
        },
        params: queryParams,
    });
};

"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { Input, Select, Button, message, Spin } from "antd";
import { promotionType } from "@/src/libs/constants";
import {
    QueryParams,
    deleteUploadedFile,
    uploadFile,
} from "@/src/services/upload.api";
import {
    courseQueryParams,
    courseBody,
    updateCourse,
    createTrainingCourse,
} from "@/src/services/course.api";
import { Dropdown, Menu } from "antd";
import { Roboto } from "next/font/google";
import TextArea from "antd/es/input/TextArea";
import { useTranslation } from "react-i18next";
import modalStyle from "./course.module.css";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const roboto = Roboto({ weight: "500", subsets: ["latin"] });
const { Option } = Select;

const AddEditCourseModal = ({
    isAddEditCourseOpen,
    setIsAddEditCourseOpen,
    fetchedCategories,
    record,
    setRecord,
    mode,
}: any) => {
    const [title, setTitle] = useState("");
    const [description, setDescription] = useState("");
    const [videoUrl, setVideoUrl] = useState<any>("");
    const [category, setCategory] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);
    const [thumbnailUrl, setThumbnailUrl] = useState("");
    const [courseVideo, setCourseVideo] = useState<
        {
            label: string;
            id: number;
            value: string | null;
            thumbnail: string | null;
        }[]
    >([{ label: "image1", id: 1, value: null, thumbnail: null }]);
    const [isVideoUploadingAndDelete, setIsVideoUploadingAndDelete] =
        useState(false);
    const { t } = useTranslation();

    const handleChange = (value: string) => {
        setDescription(value);
    };

    const handleBlur = () => {
        setDescription((prev) => prev.trim());
    };

    const router = useRouter();

    /**
     * Generate a thumbnail from a video file.
     * @param file - The video file.
     * @returns A promise that resolves with a thumbnail as a data URL.
     */
    const generateVideoThumbnail = (file: File): Promise<string> => {
        return new Promise((resolve, reject) => {
            const videoElement = document.createElement("video");
            videoElement.src = URL.createObjectURL(file); // Create object URL for the video.
            videoElement.crossOrigin = "anonymous"; // Handle CORS if needed.

            // Set up the canvas
            const canvas = document.createElement("canvas");
            const ctx = canvas.getContext("2d");

            if (!ctx) {
                reject(new Error("Failed to create canvas context"));
                return;
            }

            // Wait for the video metadata to load
            videoElement.onloadedmetadata = () => {
                // Set canvas size to match the video aspect ratio
                canvas.width = videoElement.videoWidth;
                canvas.height = videoElement.videoHeight;

                // Seek to 1 second or any specified time to avoid a blank frame
                videoElement.currentTime = 1;
            };

            // Capture the thumbnail once the desired frame is available
            videoElement.onseeked = () => {
                try {
                    ctx.drawImage(
                        videoElement,
                        0,
                        0,
                        canvas.width,
                        canvas.height
                    );
                    const thumbnailUrl = canvas.toDataURL("image/jpeg");
                    resolve(thumbnailUrl);

                    // Clean up
                    URL.revokeObjectURL(videoElement.src);
                } catch (err) {
                    reject(new Error("Error drawing video frame to canvas"));
                }
            };

            // Handle video load errors
            videoElement.onerror = () => {
                reject(
                    new Error("Failed to load video for thumbnail generation")
                );
            };
        });
    };

    // const handleVideoChange = async (
    //     e: React.ChangeEvent<HTMLInputElement>,
    //     index: number
    //   ) => {
    //     const file = e.target.files?.[0];
    //     if (file) {
    //       console.log('file ==>', file);
    //       // Create a URL for the video file
    //       const videoUrl = URL.createObjectURL(file);

    //       console.log('videoUrl ==>', videoUrl);
    //       // Set the video URL to state
    //       setVideoUrl(videoUrl); // Assuming `setVideoUrl` is a function that updates your state
    //     }
    //   };

    const handleVideoChange = async (
        e: React.ChangeEvent<HTMLInputElement>,
        index: number
    ) => {
        const file = e.target.files?.[0];
        if (file) {
            const trimmedFileName = file.name.replace(/\s+/g, "_");
            const authorization = localStorage.getItem("idToken");

            // Video query params
            const videoQueryParams: QueryParams = {
                fileName: trimmedFileName,
                fileType: "VIDEO",
                resourceType: "COURSE",
            };

            try {
                setIsVideoUploadingAndDelete(true);

                // Get pre-signed URL for the video
                const videoResponse = await uploadFile(
                    videoQueryParams,
                    authorization
                );

                if (videoResponse && videoResponse.preSignedUrl) {
                    const videoUrl = videoResponse.preSignedUrl;

                    // Upload the video
                    await fetch(videoUrl, {
                        method: "PUT",
                        body: file,
                        headers: {
                            "Content-Type": "video/mp4",
                        },
                    });

                    // Generate thumbnail
                    const thumbnailDataUrl = await generateVideoThumbnail(file);

                    // Create a Blob for the thumbnail to upload it
                    const thumbnailBlob = await fetch(thumbnailDataUrl).then(
                        (res) => res.blob()
                    );

                    // Thumbnail query params
                    const thumbnailQueryParams: QueryParams = {
                        fileName: `${trimmedFileName}_thumbnail.jpg`,
                        fileType: "IMAGE",
                        resourceType: "COURSE",
                    };

                    // Get pre-signed URL for the thumbnail
                    const thumbnailResponse = await uploadFile(
                        thumbnailQueryParams,
                        authorization
                    );

                    if (thumbnailResponse && thumbnailResponse.preSignedUrl) {
                        const thumbnailUrl = thumbnailResponse.preSignedUrl;

                        // Upload the thumbnail
                        await fetch(thumbnailUrl, {
                            method: "PUT",
                            body: thumbnailBlob,
                            headers: {
                                "Content-Type": "image/jpeg", // Specify the content type for the thumbnail
                            },
                        });

                        // Update the courseVideo state
                        setCourseVideo((prev) =>
                            prev.map((video, idx) =>
                                idx === index
                                    ? {
                                          ...video,
                                          value: videoResponse.outPutUrl, // Video URL
                                          thumbnail:
                                              thumbnailResponse.outPutUrl, // Thumbnail URL
                                      }
                                    : video
                            )
                        );

                        setVideoUrl(videoResponse.outPutUrl);
                        setThumbnailUrl(thumbnailResponse.outPutUrl); // Update video URL for preview
                    }
                } else {
                    message.error(
                        t("Invalid presigned URL response for video")
                    );
                }

                setIsVideoUploadingAndDelete(false);
            } catch (error) {
                message.error(t("Failed to upload video or thumbnail"));
                setIsVideoUploadingAndDelete(false);
            }
        }
    };

    const deleteCourseVideo = async (index: number) => {
        const courseVideoUrl = courseVideo[index].value;

        if (courseVideoUrl) {
            const queryParams: QueryParams = {
                fileName: courseVideoUrl.split("/").pop(),
                fileType: "VIDEO",
                resourceType: "COURSE",
            };
            const authorization = localStorage.getItem("idToken");
            try {
                setIsVideoUploadingAndDelete(true);
                await deleteUploadedFile(queryParams, authorization);
                const newCourseVideo = [...courseVideo];
                newCourseVideo[index] = {
                    ...newCourseVideo[index],
                    value: null,
                    thumbnail: null,
                };
                setCourseVideo(newCourseVideo);
                setVideoUrl("");
                setThumbnailUrl("");
                setIsVideoUploadingAndDelete(false);
            } catch (error) {
                message.error(t("Error deleting video"));
                setIsVideoUploadingAndDelete(false);
            }
        }
    };

    const handleAddFromGallery = (index: number) => {
        const inputFileElement = document.getElementById(`inputFile${index}`);
        if (inputFileElement) {
            (inputFileElement as HTMLInputElement).click();
        }
    };

    const menu = (index: any) => (
        <Menu>
            <Menu.Item
                key="1"
                onClick={() => handleAddFromGallery(index)}
                className="font-roboto"
            >
                <div className={`flex text-[14px] ${roboto.className}`}>
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    {t("Add From gallery")}
                </div>
            </Menu.Item>
            {courseVideo?.[0]?.value !== null && (
                <Menu.Item
                    key="2"
                    onClick={() => deleteCourseVideo(index)}
                    className="font-roboto"
                >
                    <div
                        className={`text-[#F1676D] flex text-[14px] ${roboto.className}`}
                    >
                        <Image
                            src="/images/trash.svg"
                            alt="visibility"
                            width={23}
                            height={23}
                            className="cursor-pointer mr-[7px] mb-1"
                        />
                        Delete this Video
                    </div>
                </Menu.Item>
            )}
        </Menu>
    );

    const handleAddVideo = async () => {
        setLoading(true);

        if (
            (title === null || title === "") &&
            (videoUrl === null || videoUrl === "") &&
            // category === null &&
            description === ""
        ) {
            message.warning(t("Fill all required field"));
            setLoading(false);
            return;
        }

        if (title === null || title === "") {
            message.warning(t("Course Title should not be empty"));
            setLoading(false);
            return;
        } else if (title.length > 200) {
            message.warning(t("Course Title cannot exceed 200 characters"));
            setLoading(false);
            return;
        }

        // if (category === null || category === "") {
        //     message.warning(t("Course category should not be empty"));
        //     setLoading(false);
        //     return;
        // }

        if (videoUrl === null || videoUrl === "") {
            message.warning(t("Course video should not be empty"));
            setLoading(false);
            return;
        }

        if (description === null || description === "") {
            message.warning(t("Course description should not be empty"));
            setLoading(false);
            return;
        }

        const allVideoUploaded = courseVideo.every(
            (image) => image.value !== null
        );

        if (!allVideoUploaded) {
            message.warning(t("Please upload an image for the Blog"));
            setLoading(false);
            return;
        }

        const payload: courseBody = {
            title: title,
            videoUrl: videoUrl,
            description: description,
            thumbnailUrl: thumbnailUrl,
        };
        const param: courseQueryParams = {
            categoryId: category !== null || category !== "" ? category : null,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await createTrainingCourse(
                payload,
                param,
                // category,
                authorization
            );
            if (response) {
                setIsAddEditCourseOpen(!isAddEditCourseOpen);
                message.success(t("Course created successfully"));
            }
            router.push("/training-course");
        } catch (error: any) {
            message.error(error.message || t("Failed to create the Course"));
        }
        setLoading(false);
    };

    const handleEditCourse = async () => {
        setLoading(true);

        if (
            (title === null || title === "") &&
            (videoUrl === null || videoUrl === "") &&
            // category === null &&
            description === ""
        ) {
            message.error(t("Fill all required field"));
            setLoading(false);
            return;
        }

        if (title === null || title === "") {
            message.warning(t("Course Title should not be empty"));
            setLoading(false);
            return;
        } else if (title.length > 200) {
            message.warning(t("Course Title cannot exceed 200 characters"));
            setLoading(false);
            return;
        }

        // if (category === null || category === "") {
        //     message.warning(t("Course category should not be empty"));
        //     setLoading(false);
        //     return;
        // }

        if (videoUrl === null || videoUrl === "") {
            message.warning(t("Course videoUrl should not be empty"));
            setLoading(false);
            return;
        }

        if (description === null || description === "") {
            message.warning(t("Course description should not be empty"));
            setLoading(false);
            return;
        }

        const allVideoUploaded = courseVideo.every(
            (image) => image.value !== null
        );

        if (!allVideoUploaded) {
            message.warning(t("Please upload an image for the Course"));
            setLoading(false);
            return;
        }

        const courseId = record?.id;

        const payload: courseBody = {
            title: title,
            videoUrl: videoUrl,
            description: description,
            thumbnailUrl: thumbnailUrl,
        };

        const parems: courseQueryParams = {
            videoId: courseId,
            categoryId: category !== null || category !== "" ? category : null,
        };

        try {
            const authorization = localStorage.getItem("idToken");
            const response = await updateCourse(
                courseId,
                parems,
                payload,
                authorization
            );
            if (response) {
                setIsAddEditCourseOpen(!isAddEditCourseOpen);
                message.success(t("Course successfully edited"));
            }
        } catch (error: any) {
            message.error(
                error.message === "Kurskategorie wurde deaktiviert."
                    ? "Course category is disabled. Please enable the category to edit!"
                    : t("Failed to edit the Course")
            );
        }
        setLoading(false);
    };

    useEffect(() => {
        if (record !== null && (mode === "edit" || mode === "view")) {
            setTitle(record?.title);
            setVideoUrl(record?.videoUrl);
            setCategory(record?.courseCategoryId);
            setDescription(record?.description);
            setThumbnailUrl(record?.thumbnailUrl);
            setCourseVideo((prev) => [
                {
                    ...prev[0],
                    value: record?.videoUrl,
                    thumbnail: record?.thumbnailUrl,
                },
            ]);
            setVideoUrl(record?.videoUrl);
        }
    }, [record !== null && (mode === "edit" || mode === "view")]);

    return (
        <div className={`${roboto.className} flex flex-col`}>
            <div
                className={`${roboto.className} w-full shadow-md text-[25px] text-black font-bold h-auto flex justify-between items-center px-4 py-3 z-10 bg-white`}
            >
                <div>
                    <h1 className="text-[24px]">
                        {mode === "view"
                            ? t("View Tutorial")
                            : mode === "edit"
                            ? t("Edit Tutorial")
                            : t("Add Tutorial")}
                    </h1>
                </div>
                <div className="text-[20px] font-roboto text-black h-auto flex items-center px-4">
                    <div className="flex justify-end">
                        {mode !== "view" && (
                            <>
                                {mode === "edit" ? (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] mr-2 shadow-inner button right-0 font-roboto text-white text-[18px] w-[180px] h-[46px] font-medium rounded-xl"
                                        onClick={handleEditCourse}
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                ) : (
                                    <Button
                                        type="primary"
                                        className="bg-[#67A1A3] mr-2 shadow-inner button right-0 font-roboto text-white text-[18px] w-[180px] h-[46px] font-medium rounded-xl"
                                        onClick={handleAddVideo}
                                        loading={loading}
                                    >
                                        {t("save")}
                                    </Button>
                                )}
                            </>
                        )}
                    </div>
                    <Image
                        src="/images/cross.svg"
                        alt="cross"
                        width={28}
                        height={28}
                        className="cursor-pointer mr-[10px]"
                        onClick={() => {
                            setIsAddEditCourseOpen(!isAddEditCourseOpen);
                            setRecord(null);
                        }}
                    />
                </div>
            </div>
            <div
                className={`${roboto.className} w-full ${mode === "view" ? "h-[calc(100vh-76px)]" : "h-[calc(100vh-86px)]"} text-[18px] text-black font-semibold px-4 z-0 bg-white scrollbar overflow-y-auto mt-4`}
            >
                <div className="flex gap-5">
                    <div
                        className="flex justify-start items-center w-[50%]"
                        // style={{ height: "calc(100vh - 70px)" }}
                    >
                        {videoUrl ? (
                            <div className="relative w-full h-[300px]">
                                <video
                                    src={videoUrl}
                                    controls
                                    className="rounded-lg w-full h-full bg-[#D3E3E3]"
                                />
                                {mode !== "view" && (
                                    <Dropdown
                                        overlay={menu(0)}
                                        trigger={["click"]}
                                        placement="bottomRight"
                                    >
                                        <div
                                            className="absolute top-4 right-5 cursor-pointer bg-white rounded-md bg-opacity-80 border-2"
                                            onClick={(e) => e.stopPropagation()}
                                        >
                                            <Image
                                                src="/images/more.svg"
                                                alt="More"
                                                width={20}
                                                height={20}
                                                className="cursor-pointer"
                                            />
                                        </div>
                                    </Dropdown>
                                )}
                            </div>
                        ) : (
                            <label className="w-full h-[300px]">
                                <div className="bg-[#D3E3E3] border-2 border-dashed border-[#67A1A3] border-opacity-65 w-full h-full rounded-lg">
                                    {isVideoUploadingAndDelete ? (
                                        <div className="flex justify-center items-center w-full h-full">
                                            <Spin size="default" />
                                        </div>
                                    ) : (
                                        <div className="flex flex-col justify-center items-center w-full h-full cursor-pointer">
                                            <Image
                                                src="/images/upload.svg"
                                                alt="More"
                                                width={108}
                                                height={108}
                                                className="cursor-pointer"
                                            />
                                            <p
                                                className={`text-[20px] font-[500] ${roboto.className}`}
                                            >
                                                {t("Upload Video")}
                                            </p>
                                        </div>
                                    )}
                                </div>
                                {!isVideoUploadingAndDelete && (
                                    <input
                                        id="inputFile0"
                                        type="file"
                                        accept="video/*"
                                        onChange={(e) =>
                                            handleVideoChange(e, 0)
                                        }
                                        style={{
                                            cursor: "pointer",
                                            display: "none",
                                        }}
                                    />
                                )}
                            </label>
                        )}
                    </div>
                    <div className="w-[50%] h-[300px] bg-[#D3E3E3] relative rounded-lg">
                        {isVideoUploadingAndDelete ? (
                            <div className="flex justify-center items-center w-full h-full absolute inset-0 z-10">
                                <Spin size="default" />
                            </div>
                        ) : thumbnailUrl ? (
                            <img
                                src={thumbnailUrl}
                                alt="Uploaded"
                                className="w-full h-full object-contain rounded-lg"
                            />
                        ) : (
                            <p
                                className={`w-full h-full flex flex-col justify-center items-center text-[20px] font-[500] ${roboto.className}`}
                            >
                                <Image
                                    src="/images/upload.svg"
                                    alt="More"
                                    width={108}
                                    height={108}
                                    className=""
                                />
                                {t("Thumbnail")}
                            </p>
                        )}
                    </div>
                </div>
                <div className="w-full mt-5">
                    <div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">
                                Title{" "}
                                {mode !== "view" && (
                                    <span className="text-red-500 font-[400]">
                                        *
                                    </span>
                                )}{" "}
                                :
                            </h1>
                            <Input
                                className="rounded-md font-roboto font-normal pl-[11px] h-[36px] ml-2 w-full border-[#D3E3E3] bg-[#F6FAF9]"
                                value={title}
                                onChange={(e) => setTitle(e.target.value)}
                                onBlur={() => setTitle(title.trim())}
                                placeholder={t("Enter title")}
                                readOnly={mode === "view"}
                                style={{
                                    pointerEvents:
                                        mode === "view" ? "none" : "auto",
                                }}
                            />
                        </div>
                        <div className="flex items-center mb-4">
                            <h1 className="w-[150px]">
                                {t("Category")}
                                {/* {mode !== "view" && (
                                    <span className="text-red-500 font-[400]">
                                        *
                                    </span>
                                )}{" "} */}
                                :
                            </h1>
                            <Select
                                className="border-none custom-select h-[36px] font-roboto rounded-md ml-2 w-full font-[400]"
                                placeholder={t("Select Category")}
                                value={category}
                                onChange={(value) => setCategory(value)}
                                onClick={(e) => {
                                    if (mode === "view") {
                                        e.preventDefault();
                                    }
                                }}
                                style={{
                                    pointerEvents:
                                        mode === "view" ? "none" : "auto",
                                }}
                                suffixIcon={
                                    mode !== "view" && (
                                        <Image
                                            src="/images/arrowI.svg"
                                            alt="More"
                                            width={20}
                                            height={20}
                                        />
                                    )
                                }
                            >
                                {fetchedCategories &&
                                    fetchedCategories.map((option: any) => (
                                        <Option
                                            key={option.id}
                                            value={option.id}
                                            disabled={
                                                option.status !== "ENABLED"
                                            }
                                        >
                                            {option.category}
                                        </Option>
                                    ))}
                            </Select>
                        </div>
                        <div className="flex mb-4">
                            <h1 className="w-[150px]">
                                {t("description")}{" "}
                                {mode !== "view" && (
                                    <span className="text-red-500 font-[400]">
                                        *
                                    </span>
                                )}{" "}
                                :
                            </h1>
                            <TextArea
                                className="rounded-md font-roboto font-normal pl-[11px] h-[72px] ml-2 w-full border-[#D3E3E3] bg-[#F6FAF9] resize-none overflow-hidden"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                onBlur={() =>
                                    setDescription(description.trim())
                                }
                                rows={5}
                                placeholder={t("Enter Description")}
                                readOnly={mode === "view"}
                                style={{
                                    pointerEvents:
                                        mode === "view" ? "none" : "auto",
                                }}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AddEditCourseModal;

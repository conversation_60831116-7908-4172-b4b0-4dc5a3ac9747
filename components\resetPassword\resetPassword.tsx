"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import { Input, Button, message } from "antd";
import { useRouter } from "next/navigation";
import { forgotPasswordWithFirebase } from "@/src/services/auth.api";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";
import { Roboto } from "next/font/google";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const ResetPassword = () => {
    const [email, setEmail] = useState("");
    const [isSent, setIsSent] = useState(false);
    const [emailError, setEmailError] = useState("");
    const [loading, setLoading] = useState(false);
    const router = useRouter();
    const { t } = useTranslation();

    useEffect(() => {
        const session = localStorage.getItem("session");
        handleChangeLanguage("en");
        if (session !== null) {
            router.push("/users");
        }
    }, []);

    const validateEmail = (email: string) => {
        const re = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/;
        return re.test(email);
    };

    const containsUppercase = (email: string) => {
        const re = /[A-Z]/;
        return re.test(email);
    };

    const handleSendClick = async (e: React.FormEvent) => {
        e.preventDefault();
        let emailValid = true;
        if (!email) {
            setEmailError(t("Email is a required field"));
            emailValid = false;
        } else if (containsUppercase(email) && !validateEmail(email)) {
            setEmailError(
                t("Email must be in lowercase and with valid email format")
            );
            emailValid = false;
            emailValid = false;
        } else if (containsUppercase(email)) {
            setEmailError(t("Email must be in lowercase"));
            emailValid = false;
        } else if (!validateEmail(email)) {
            setEmailError(t("Email must be in valid email format"));
            emailValid = false;
        } else {
            setEmailError("");
        }
        if (emailValid) {
            setEmailError("");
            setLoading(true);
            try {
                await forgotPasswordWithFirebase(email);
                setIsSent(true);
                message.success(t("Password reset email sent successfully.")); // Password reset email sent successfully.
            } catch (error) {
                setEmailError(t("User not found")); // User not found
            } finally {
                setLoading(false);
            }
        }
    };

    return (
        <div className={`${roboto.className} bg-white h-screen select-none`}>
            <div className="flex pl-[60px] pt-[20px] h-auto">
                <div>
                    <Image
                        src="/images/Logo_2.svg"
                        alt="login"
                        width={60}
                        height={60}
                        className="shadowed-element rounded-full cursor-pointer"
                        onClick={() => {
                            router.push("/");
                        }}
                    />
                </div>
                <div>
                    <h1
                        className="text-[#004F53] ml-[10px] text-[32px] pt-[5px] font-[600] cursor-pointer"
                        onClick={() => {
                            router.push("/");
                        }}
                    >
                        {t("gymi")}
                    </h1>
                </div>
            </div>
            <div className="flex h-[calc(100vh-80px)] justify-center items-center w-full">
                <div className="w-[50%] flex items-center justify-center">
                    <img
                        src="/images/gymiLogin1.svg"
                        alt="login"
                        className="-mr-[150px] w-[70vw] h-[70vh]"
                    />
                </div>
                <div className="w-[50%] flex items-center justify-center">
                    {!isSent ? (
                        <div
                            className={`${roboto.className} w-[400px] flex flex-col items-center justify-center`}
                        >
                            <h1
                                className={`${roboto.className} font-[600] text-[28px] text-[#18181B]`}
                            >
                                {t("reset_password")}
                            </h1>
                            <h2 className="mt-4 mb-10 text-sm text-gray-500 text-center">
                                {/* Enter your email address and we’ll send you a
                                link <br />
                                to reset your password */}
                                {t("enter_your_email_address_and_send")}
                            </h2>
                            <form
                                className="mt-5 w-[400px]"
                                onSubmit={handleSendClick}
                            >
                                <div className="relative">
                                    <label
                                        htmlFor="email"
                                        className="block text-black text-[16px] mb-1"
                                    >
                                        {/* Enter Email */}
                                    </label>
                                    <Input
                                        type="text"
                                        id="email"
                                        value={email}
                                        onChange={(e) => {
                                            setEmail(e.target.value);
                                            setEmailError("");
                                        }}
                                        className={`w-[400px] h-[50px] ${
                                            emailError
                                                ? "border-red-500"
                                                : "border-[#A3CBC1] bg-[#F6FAF9]"
                                        }`}
                                        placeholder={t("enter_your_email")}
                                        suffix={
                                            <Image
                                                src="/images/mail.svg"
                                                alt="mail"
                                                width={20}
                                                height={20}
                                                className="mr-[10px]"
                                            />
                                        }
                                        onPressEnter={handleSendClick}
                                    />
                                    {emailError && (
                                        <p className="text-red-500 text-[14px] absolute">
                                            {emailError}
                                        </p>
                                    )}
                                </div>
                                <div className="mb-14 text-black flex justify-end">
                                    <h1
                                        onClick={() => router.push("/")}
                                        className="hover:text-[#67A1A3] text-[14px] cursor-pointer mt-1"
                                    >
                                        Login
                                    </h1>
                                </div>
                                <div className="flex items-center justify-center mt-6 cursor-pointer">
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        className={`${roboto.className} flex items-center justify-center text-[16px] w-[267px] h-[50px] shadow-inner text-white font-[500]`}
                                        style={{ backgroundColor: "#67A1A3" }}
                                        loading={loading}
                                    >
                                        {t("send")}
                                    </Button>
                                </div>
                            </form>
                        </div>
                    ) : (
                        <div
                            className={`${roboto.className} w-[400px] flex flex-col items-center justify-center`}
                        >
                            <Image
                                src="/images/sended.svg"
                                alt="login"
                                width={64}
                                height={64}
                                className="pt-4"
                            />
                            <h1
                                className={`${roboto.className} font-[500] text-[32px] text-[#18181B] mt-4 text-center`}
                            >
                                {t("sent_successfully")}
                            </h1>
                            <h3
                                className={`${roboto.className} mt-4 text-sm text-gray-500 text-center`}
                            >
                                {/* If your account is registered with this email,
                                <br /> we&apos;ve successfully sent the password
                                reset link <br />{" "} */}
                                Wenn Ihr Konto mit dieser E-Mail registriert
                                ist,
                                <br /> Wir haben den Link zum Zurücksetzen des
                                Passworts erfolgreich gesendet{" "}
                                <span className="font-[600] text-black">
                                    {email}
                                </span>
                            </h3>
                            <form className="mt-16 w-[400px]">
                                <div className="flex items-center justify-center cursor-pointer">
                                    <Button
                                        type="primary"
                                        className={`${roboto.className} flex w-[287px] h-[50px] shadow-inner text-[16px] items-center justify-center text-white font-medium`}
                                        style={{
                                            backgroundColor: "#67A1A3",
                                        }}
                                        onClick={() => router.push("/")}
                                    >
                                        {t("back_to_login")}
                                    </Button>
                                </div>
                            </form>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ResetPassword;

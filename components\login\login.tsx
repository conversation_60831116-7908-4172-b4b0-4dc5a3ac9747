"use client";
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Input, Button, message } from "antd";
import { useRouter } from "next/navigation";
import {
    signInWithFirebase,
    authenticateWithAPI,
} from "@/src/services/auth.api";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";
import { getToken } from "firebase/messaging";
import { messaging } from "@/firebase.config";
import { Roboto } from "next/font/google";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const Login = () => {
    const [passwordVisible, setPasswordVisible] = useState(false);
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [emailError, setEmailError] = useState("");
    const [passwordError, setPasswordError] = useState("");
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState("");
    const router = useRouter();
    const { t } = useTranslation();
    const [fcmToken, setFcmToken] = useState("");
    const isInitialized = useRef(false);

    useEffect(() => {
        const registerServiceWorkerAndGetToken = async () => {
            try {
                if (Notification.permission !== "granted") {
                    const permission = await Notification.requestPermission();
                    if (permission !== "granted") {
                        console.error("Notification permission denied.");
                        return;
                    }
                }

                // 🔥 Unregister existing service workers to prevent duplicates
                const registrations =
                    await navigator.serviceWorker.getRegistrations();
                if (registrations.length > 0) {
                    await Promise.all(
                        registrations.map((reg) => reg.unregister())
                    );
                }

                // ✅ Register the service worker properly
                const registration = await navigator.serviceWorker.register(
                    "/firebase-messaging-sw.js"
                );

                // ✅ Ensure the registration is ready
                await navigator.serviceWorker.ready;

                // ✅ Get the FCM token
                const token = await getToken(messaging, {
                    vapidKey: process.env.VAPIKEY,
                    serviceWorkerRegistration: registration,
                });

                if (token) {
                    setFcmToken(token);
                } else {
                    console.error("No FCM Token available.");
                }
            } catch (error) {
                console.error(
                    "Error during Service Worker or FCM setup:",
                    error
                );
            }
        };

        if (!isInitialized.current && "serviceWorker" in navigator) {
            isInitialized.current = true; // Mark as initialized
            registerServiceWorkerAndGetToken();
        }
    }, []);

    useEffect(() => {
        const session = localStorage.getItem("session");
        handleChangeLanguage("en");
        if (session !== null) {
            router.push("/users");
        }
    }, []);

    const togglePasswordVisibility = () => {
        setPasswordVisible(!passwordVisible);
    };

    // const validateEmail = (email: string) => {
    //     // const re = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-zA-Z]{2,}$/;
    //     // return re.test(String(email).toLowerCase());
    //     const re = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/;
    //     return re.test(email);
    // };

    const validateEmailFormat = (email: string) => {
        const re = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/;
        return re.test(email);
    };

    const containsUppercase = (email: string) => {
        const re = /[A-Z]/;
        return re.test(email);
    };

    const handleLogin = async () => {
        let emailValid = true;
        let passwordValid = true;

        if (!email) {
            setEmailError(t("Email is a required"));
            emailValid = false;
        } else if (containsUppercase(email) && !validateEmailFormat(email)) {
            setEmailError(
                t("Email must be in lowercase and with valid email format")
            );
            emailValid = false;
        } else if (containsUppercase(email)) {
            setEmailError(t("Email must be in lowercase"));
            emailValid = false;
        } else if (!validateEmailFormat(email)) {
            setEmailError(t("Email must be in valid email format"));
            emailValid = false;
        } else {
            setEmailError("");
        }

        if (!password) {
            setPasswordError(t("Password is a required"));
            setError("");
            passwordValid = false;
        } else {
            setPasswordError("");
            setError("");
        }

        if (passwordValid && emailValid) {
            try {
                setLoading(true);
                const { idToken } = await signInWithFirebase(email, password);
                const login = await authenticateWithAPI(
                    email,
                    idToken,
                    fcmToken
                );
                if (
                    login &&
                    (login?.user?.type === "ADMIN" ||
                        login?.user?.type === "TEACHER") &&
                    !login?.user?.isDeleted
                ) {
                    localStorage.setItem("idToken", `Bearer ${idToken}`);
                    localStorage.setItem("session", login?.userSessions?.id);
                    localStorage.setItem("email", login?.user?.email);
                    localStorage.setItem("username", login?.user?.username);
                    localStorage.setItem("profilePic", login?.user?.profilePic);
                    localStorage.setItem("userId", login?.userSessions?.userId);
                    localStorage.setItem("userType", login?.user?.type);
                    localStorage.setItem(
                        "adminSessionTime",
                        JSON.stringify(Date.now())
                    );

                    const userName = localStorage.getItem("email");
                    if (userName === "<EMAIL>") {
                        router.push("/highSchool");
                    } else if (login?.user?.type === "TEACHER") {
                        router.push("/knowledgeRally");
                    } else {
                        router.push("/users");
                    }
                    message.success("Logged in successfully");
                } else {
                    message.error(t("Invalid Credentials"));
                    setError(t("Invalid Credentials"));
                }
            } catch (error: unknown) {
                if (error instanceof Error) {
                    const errorMessage = error.message
                        ? "Your Email ID or Password doesn’t match"
                        : "";
                    setError(errorMessage);
                } else {
                    message.error(t("An unknown error occurred")); //An unknown error occurred
                }
            } finally {
                setLoading(false);
            }
        }
    };

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setEmail(e.target.value.trim());
    };

    const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setPassword(e.target.value);
    };

    return (
        <div className={`${roboto.className} bg-white h-screen select-none`}>
            <div className="flex pl-[60px] pt-[20px] h-auto">
                <div>
                    <Image
                        src="/images/Logo_2.svg"
                        alt="login"
                        width={60}
                        height={60}
                        className="shadowed-element rounded-full cursor-pointer"
                        onClick={() => {
                            router.push("/");
                        }}
                    />
                </div>
                <div>
                    <h1
                        className="text-[#004F53] ml-[10px]  text-[32px] pt-[5px] font-bold cursor-pointer"
                        onClick={() => {
                            router.push("/");
                        }}
                    >
                        {t("gymi")}
                    </h1>
                </div>
            </div>
            <div className="flex h-[calc(100vh-80px)] justify-center items-center w-full">
                <div className="w-[50%] flex items-center justify-center">
                    <img
                        src="/images/gymiLogin1.svg"
                        alt="login"
                        className="-mr-[150px] w-[70vw] h-[70vh]"
                    />
                </div>
                <div className="w-[50%] flex items-center justify-center">
                    <div className="flex flex-col justify-center">
                        <h1 className="font-semibold text-[28px] mb-[40px] text-[#18181B] text-center">
                            {t("Welcome to GYMi")}
                        </h1>
                        <form className="w-[400px] mt-[30px] text-black">
                            <div className="mb-2">
                                <label
                                    htmlFor="email"
                                    className="block text-[16px] mb-1"
                                >
                                    {/* Enter Email */}
                                    {t("enter_your_email")}
                                </label>
                                <Input
                                    type="email"
                                    id="email"
                                    size="large"
                                    value={email}
                                    onChange={handleEmailChange}
                                    placeholder={t("email")}
                                    className={`w-[400px] h-[50px] pb-[10px] custom-caret ${
                                        emailError || error
                                            ? "border-red-500"
                                            : "border-[#A3CBC1] bg-[#F6FAF9]"
                                    }`}
                                    suffix={
                                        <Image
                                            src="/images/username.svg"
                                            alt="visibility"
                                            width={22}
                                            height={22}
                                        />
                                    }
                                />
                                {emailError && (
                                    <p className="text-red-500 text-[14px] absolute">
                                        {emailError}
                                    </p>
                                )}
                            </div>
                            <div className="mb-4">
                                <label
                                    htmlFor="password"
                                    className="block text-[16px] mt-[20px] mb-1"
                                >
                                    {t("enter_your_password")}
                                </label>
                                <Input.Password
                                    id="password"
                                    size="large"
                                    value={password}
                                    onChange={handlePasswordChange}
                                    placeholder="Password"
                                    visibilityToggle
                                    onPressEnter={handleLogin}
                                    iconRender={(visible) =>
                                        visible ? (
                                            <Image
                                                src="/images/eye.svg"
                                                alt="visibility"
                                                width={22}
                                                height={22}
                                                onClick={
                                                    togglePasswordVisibility
                                                }
                                                style={{ cursor: "pointer" }}
                                            />
                                        ) : (
                                            <Image
                                                src="/images/eye-slash.svg"
                                                alt="visibility"
                                                width={22}
                                                height={22}
                                                onClick={
                                                    togglePasswordVisibility
                                                }
                                                style={{ cursor: "pointer" }}
                                            />
                                        )
                                    }
                                    className={`w-[400px] h-[50px] pb-[10px] custom-caret ${
                                        passwordError || error
                                            ? "border-red-500"
                                            : "border-[#A3CBC1] bg-[#F6FAF9]"
                                    }`}
                                />
                                {passwordError && (
                                    <p className="text-red-500 text-[14px] absolute">
                                        {passwordError}
                                    </p>
                                )}
                                {error && (
                                    <p className="text-red-500 text-[14px] absolute">
                                        {error}
                                    </p>
                                )}
                            </div>
                            <div className="mb-6 text-black flex justify-end">
                                <h1
                                    onClick={() =>
                                        router.push("/resetPassword")
                                    }
                                    className="hover:text-[#67A1A3] text-[14px] cursor-pointer mt-1"
                                >
                                    {t("forgot_password")}
                                </h1>
                            </div>
                            <div className="flex justify-center mt-[60px]">
                                <Button
                                    type="primary"
                                    size="large"
                                    onClick={handleLogin}
                                    style={{ backgroundColor: "#67A1A3" }}
                                    className={`${roboto.className} w-[287px] h-[50px] font-[500] bg-[#67A1A3] text-white shadow-inner`}
                                    loading={loading}
                                >
                                    Login
                                </Button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Login;

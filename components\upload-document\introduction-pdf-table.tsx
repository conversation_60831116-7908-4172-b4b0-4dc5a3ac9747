"use client";
import React, { useState, useEffect } from "react";
import "@/app/globals.css";
import {
    Table,
    Spin,
    Empty,
    Dropdown,
    Menu,
    Modal,
    But<PERSON>,
    message,
} from "antd";
import type { ColumnsType } from "antd/es/table";
import Image from "next/image";
import { Roboto } from "next/font/google";
import { useTranslation } from "react-i18next";
import { CircularProgress } from "@mui/material";
import { Grade4thLevel, forthGradeSubjects } from "@/src/libs/constants";
import {
    uploadDocumentParams,
    deleteIntroductionPdf,
} from "@/src/services/upload-document.api";
import AddDocumentModal from "./add-intoduction-document-modal";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface subscribedUser {
    email: string;
    id: string;
}

const IntriductionPdfTable = ({
    isLoading,
    data,
    paginationConfig,
    fetchData,
}: any) => {
    const { t } = useTranslation();
    const [selectedKeyOp, setSeletedKeyOp] = useState(null);
    const [materialRowRecordId, setMaterialRowRecordId] = useState<any>(null);
    const [recordDataForEdit, setRecordDataForEdit] = useState<any>(null);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [pdfModalOpen, setPdfModalOpen] = useState(false);
    const [selectedPdf, setSelectedPdf] = useState<string | null>(null);
    const [isDelete, setIsDelete] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] =
        useState<boolean>(false);

    const handleMenuClick = async (e: any, record: any) => {
        const { key } = e;
        setSeletedKeyOp(key);
        setMaterialRowRecordId(record.id);
        if (key === "view") {
            setPdfModalOpen(true);
            setSelectedPdf(record?.docUrl);
        } else if (key === "edit") {
            setIsModalVisible(true);
            setRecordDataForEdit(record);
        } else if (key === "delete") {
            setIsDeleteModalVisible(true);
        }
    };

    const menu = (record: any) => (
        <Menu onClick={(e) => handleMenuClick(e, record)}>
            <Menu.Item key="view">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/eye.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[9px] mb-1"
                    />
                    {t("View")}
                </div>
            </Menu.Item>
            <Menu.Item key="edit">
                <div className="flex text-[14px] font-roboto">
                    <Image
                        src="/images/edit.svg"
                        alt="visibility"
                        width={20}
                        height={20}
                        className="cursor-pointer mr-[10px] mb-1"
                    />
                    {t("Edit")}
                </div>
            </Menu.Item>
            <Menu.Item key="delete">
                <div className="flex text-[14px] font-roboto text-[#F1676D]">
                    <Image
                        src="/images/trash.svg"
                        alt="visibility"
                        width={23}
                        height={23}
                        className="cursor-pointer mr-[7px] mb-1"
                    />
                    {t("delete")}
                </div>
            </Menu.Item>
        </Menu>
    );

    const IntriductionPdfDataColumns: ColumnsType<subscribedUser> = [
        {
            title: "Sr.No.",
            dataIndex: "sr_no",
            key: "id",
            align: "center",
            className: `${roboto.className}`,
            width: 100,
            render: (srNo) => <div>{srNo}.</div>,
        },
        {
            title: "Grade",
            dataIndex: "grade",
            key: "grade",
            align: "center",
            className: `${roboto.className}`,
            render: (grade) => (
                <div className="px-[20px] flex justify-center">
                    <p className="text-left">
                        {grade === "FIRST"
                            ? "Grade 1"
                            : grade === "SECOND"
                            ? "Grade 2"
                            : grade === "THIRD"
                            ? "Grade 3"
                            : "Grade 4"}
                    </p>
                </div>
            ),
        },
        {
            title: "Subject",
            dataIndex: "subject",
            key: "subject",
            align: "center",
            className: `${roboto.className}`,
            render: (more: string, record: any) => (
                <div className="px-[20px] flex justify-center">
                    <p className="text-left">
                        {forthGradeSubjects.find(
                            (subject) => subject?.value === record?.subject
                        )?.label || "Unknown subject"}
                    </p>
                </div>
            ),
        },
        {
            title: t("File"),
            dataIndex: "docUrl",
            key: "docUrl",
            align: "center",
            className: `${roboto.className}`,
            render: (docUrl: string, record: any) => {
                return (
                    <div className="flex justify-center items-center">
                        {docUrl ? (
                            <div
                                className="cursor-pointer flex justify-center items-center gap-2"
                                onClick={() => {
                                    setPdfModalOpen(true);
                                    setSelectedPdf(record?.docUrl);
                                }}
                            >
                                {record.fileName}
                                <Image
                                    src="/images/pdf.svg"
                                    alt="File available"
                                    width={30}
                                    height={30}
                                />
                            </div>
                        ) : (
                            "-"
                        )}
                    </div>
                );
            },
        },
        {
            title: t("More"),
            dataIndex: "more",
            key: "more",
            align: "center",
            className: `${roboto.className}`,
            width: 130,
            render: (more: string, record: any) => {
                return record.id ? (
                    <Dropdown
                        overlay={menu(record)}
                        trigger={["click"]}
                        // placement="bottomRight"
                    >
                        <div
                            className="flex justify-center"
                            onClick={(e) => e.stopPropagation()}
                        >
                            <Image
                                src="/images/more.svg"
                                alt="More"
                                width={20}
                                height={20}
                                className="cursor-pointer"
                                onClick={(e) => e.preventDefault()}
                            />
                        </div>
                    </Dropdown>
                ) : (
                    <CircularProgress size={20} />
                );
            },
        },
    ];

    const handleDeleteConfirm = async (id: string) => {
        try {
            setIsDelete(true);
            const authorization = localStorage.getItem("idToken");
            const queryParams: uploadDocumentParams = {
                introDocId: id,
            };

            const response = await deleteIntroductionPdf(
                id,
                queryParams,
                authorization
            );
            if (response) {
                setIsDeleteModalVisible(false);
                fetchData();
                message.success(t("Document deleted successfully"));
                setIsDelete(false);
            }
        } catch (error) {
            message.error(t("Failed to delete Document"));
            setIsDelete(false);
        }
    };

    return (
        <div className="flex">
            {isLoading ? (
                <div className="flex justify-center items-center h-[calc(100vh-180px)] w-full">
                    <Spin size="default" />
                </div>
            ) : (
                <>
                    <div className="mt-4">
                        <Table
                            columns={IntriductionPdfDataColumns}
                            dataSource={data}
                            // pagination={{
                            //     ...paginationConfig,
                            //     className: "custom-pagination custom-select",
                            // }}
                            pagination={false}
                            bordered={false}
                            rowKey="id"
                            className={`custom-table ${
                                data.length > 8
                                    ? "custom-table-scroll"
                                    : "custom-table-scroll-hide"
                            }  scrollbar`}
                            loading={{
                                indicator: <Spin size="default" />,
                                spinning: isLoading,
                            }}
                            scroll={{
                                y: "66vh",
                            }}
                            locale={{
                                emptyText: (
                                    <div
                                        className={`h-[61vh] flex items-center justify-center font-[500]`}
                                    >
                                        <Empty
                                            description="No data available"
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        />
                                    </div>
                                ),
                            }}
                        />
                    </div>
                </>
            )}
            {/* PDF Viewer Modal */}
            <Modal
                title="PDF Viewer"
                open={pdfModalOpen}
                onCancel={() => {
                    setPdfModalOpen(false);
                    setSelectedPdf(null);
                }}
                footer={null}
                width="70vw"
                centered
            >
                {selectedPdf && (
                    <iframe
                        key={selectedPdf}
                        src={selectedPdf}
                        title="PDF Viewer"
                        className="w-full h-[550px]"
                    />
                )}
            </Modal>
            <Modal
                open={isDeleteModalVisible}
                onCancel={() => setIsDeleteModalVisible(false)}
                footer={null}
                centered
                width={300}
                style={{ textAlign: "center" }}
                closable={false}
            >
                <Image
                    src="/images/modalDelete.svg"
                    alt="Delete"
                    width={100}
                    height={100}
                    className="mx-auto bg-white -mt-3"
                />

                <div className=" flex justify-center items-center w-full">
                    <h2 className="text-[16px] font-roboto font-[400] mb-8 w-[80%] flex justify-center items-center">
                        {t("Are you sure you want to delete this Document?")}
                    </h2>
                </div>
                <div className="flex justify-center space-x-4">
                    <Button
                        onClick={() => {
                            setIsDeleteModalVisible(false);
                            setSeletedKeyOp(null);
                        }}
                        className="bg-transparent cancelbutton font-roboto text-[#F1676D] text-[18px] h-[46px] border-[1px] border-[#F1676D] w-[116px] font-medium rounded-xl"
                    >
                        {t("cancel")}
                    </Button>
                    <Button
                        type="primary"
                        className="bg-[#F1676D] shadow-inner deleteButton font-roboto text-white text-[18px] w-[116px] h-[46px] font-medium rounded-xl"
                        onClick={() => {
                            handleDeleteConfirm(materialRowRecordId);
                        }}
                        loading={isDelete}
                    >
                        {t("delete")}
                    </Button>
                </div>
            </Modal>
            <AddDocumentModal
                isModalVisible={isModalVisible}
                setIsModalVisible={setIsModalVisible}
                fetchData={fetchData}
                selectedKeyOp={selectedKeyOp}
                setSeletedKeyOp={setSeletedKeyOp}
                recordDataForEdit={recordDataForEdit}
                setRecordDataForEdit={setRecordDataForEdit}
            />
        </div>
    );
};

export default IntriductionPdfTable;

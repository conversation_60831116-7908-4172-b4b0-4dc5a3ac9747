import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import ClientLayout from "@/components/clientLayout/clientLayout";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
    title: "GYMi - Verwaltung für Eltern und Kinder",
    description:
        "Das GYMi-Admin-Panel zur effizienten Verwaltung von Eltern- und Kinderprozessen mit optimierter Benutzersteuerung 😊.",
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (

        <html lang="en">
            <head>
                <link rel="icon" href="/favicon/favicon.svg" />
                <meta name="msapplication-TileColor" content="#da532c" />
            </head>
            <body className={inter.className}>
                <ClientLayout>{children}</ClientLayout>
            </body>
        </html>
    );
}

on:
    push:
      branches:
        - dev
concurrency:
    group: dev
    cancel-in-progress: true
name: 🚀 Deploy dev to DigitalOcean Server
jobs:
    deploy:
      name: 🎉 Deploy
      runs-on: ubuntu-latest
      steps:
      - name: 🚚 SSH Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PRODUCTION_SERVER_HOST }}
          username: ${{ secrets.PRODUCTION_SERVER_USER }}
          key: ${{ secrets.PRODUCTION_SERVER_KEY }}
          port: ${{ secrets.PRODUCTION_SERVER_PORT }}
          script: |
              cd /var/www/dev-gymi-admin-react-web/
              git reset --hard
              git checkout dev
              git pull origin dev
              yarn
              yarn build
              pm2 restart "dev-gymi-admin"
              pm2 save
#start new proccess: sudo pm2 start npx --name "dev-gymi-admin"

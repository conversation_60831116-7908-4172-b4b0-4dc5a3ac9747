"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Navbar from "./navbar";
import Footer from "./footer";
import { Input, Button, message, Spin } from "antd";
import { MailOutlined } from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { <PERSON>o, Lato } from "next/font/google";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const lato = Lato({
    weight: ["100", "300", "400", "700"],
    subsets: ["latin"],
});
const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const SelectedBlog = ({ selectedAnyBlog, setSelectedAnyBlog, record }: any) => {
    const router = useRouter();
    const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0);

    return (
        <div className={`h-full overflow-y-auto custom-scroll ${roboto.className}`}>
            <div className="relative z-10">
                <Navbar
                    selectedAnyBlog={selectedAnyBlog}
                    setSelectedAnyBlog={setSelectedAnyBlog}
                />
            </div>
            <div className="md:h-[90vh] h-[70vh] w-full">
                <div
                    className={`relative bg-cover bg-no-repeat bg-center flex md:justify-start md:h-[80vh] h-[70vh] md:items-center items-end gap-20 text-black pt-[11vh] md:p-20 p-10 shadow-xl ${roboto.className}`}
                    style={{
                        backgroundImage: `url(${record?.image})`,
                        backgroundSize: "cover",
                    }}
                >
                    <div className="absolute inset-0 bg-gradient-to-r from-[#004F53]/[0.3] via-[#004F53]/[0.2] to-transparent opacity-100 w-[100%] bg-black bg-opacity-50"></div>
                    <p
                        className={`relative text-left text-white md:text-[75px] text-[28px] font-[700] mt-5 md:w-[60%] md:leading-[90px] ${lato.className}`}
                        style={{
                            textShadow: "1px 1px 2px rgba(0, 0, 0, 1)",
                        }}
                        data-aos="fade-right"
                    >
                        {record?.mainTitle}
                    </p>
                </div>

                <div className="flex justify-center px-4">
                    <div
                        className={`text-black p-3 w-full md:w-[90%] mt-10 border-2 border-[#D3E3E3] shadow-md rounded-lg ${roboto.className}`}
                        data-aos="zoom-in"
                        data-aos-once="true"
                    >
                        <p className="text-[18px] md:text-[24px] text-black font-[600] leading-[1.5] mt-4 text-justify">
                            {record?.subTitle}
                        </p>
                        {record?.shortDescription !== null && (
                            <p className="text-[16px] md:text-[18px] text-black font-[400] leading-[1.5] mt-4 text-justify">
                                {record?.shortDescription}
                            </p>
                        )}
                        <p className="text-black border-2 border-[#D3E3E3] py-1 w-[200px] rounded-lg text-center mt-3">
                            {record?.category?.category}
                        </p>
                    </div>
                </div>
                <div className="flex justify-center px-4">
                    <div
                        className={`text-black p-6 md:p-10 w-full md:w-[90%] border-2 border-[#D3E3E3] mt-5 shadow-lg pb-14 rounded-lg`}
                        data-aos="zoom-in"
                        data-aos-once="true"
                    >
                        {/* Display the HTML content */}
                        <div
                            className={`text-[14px] md:text-[16px] text-gray-900 font-[400] leading-[1.5] text-justify  ${roboto.className}`}
                            dangerouslySetInnerHTML={{
                                __html: record?.description,
                            }}
                        />
                    </div>
                </div>

                <div className="md:mt-16 mt-8">
                    <Footer />
                </div>
            </div>
        </div>
    );
};

export default SelectedBlog;

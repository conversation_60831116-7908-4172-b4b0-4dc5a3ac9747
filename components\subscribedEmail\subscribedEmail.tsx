"use client";
import React, { useState, useEffect } from "react";
import Navbar from "../Navbar/navbar";
import SideBar from "@/components/sideBar/sideBar";
import "@/app/globals.css";
import Image from "next/image";
import { Table, Spin, Button, message, Empty } from "antd";
import type { ColumnsType } from "antd/es/table";
import {
    getSubscribedEmails,
    QueryParams,
    downloadSubscribedEmails,
} from "@/src/services/users.api";
import { CircularProgress, Switch } from "@mui/material";
import { Roboto } from "next/font/google";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { handleChangeLanguage } from "@/components/context/languageContext";

const roboto = Roboto({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface subscribedUser {
    email: string;
    id: string;
}

const SubscribedEmail = () => {
    const [isButtonLoading, setButtonIsLoading] = useState(false);
    const [data, setData] = useState<subscribedUser[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [response, setResponse] = useState<any>(null);
    const [currentPage, setCurrentPage] = useState(1);
    const [isTabledataLoading, setIsabledataLoading] = useState(false);
    const [pageSize, setPageSize] = useState<number>(20);
    const { t } = useTranslation();

    const router = useRouter();

    useEffect(() => {
        fetchData(currentPage);
    }, [currentPage, pageSize]);

    const fetchData = async (page: number = 1) => {
        const authorization = localStorage.getItem("idToken");

        try {
            setIsabledataLoading(true);
            const queryParams: QueryParams = {
                skip: (page - 1) * pageSize,
                take: pageSize,
                orderBy: "createdAt|desc",
            };
            const response = await getSubscribedEmails(
                queryParams,
                authorization
            );
            setResponse(response);
            if (response) {
                const updatedData = response.list.map(
                    (item: any, index: any) => ({
                        ...item,
                        srNo: (page - 1) * pageSize + index + 1,
                    })
                );
                setData(updatedData);
                setTotal(response.total);
            } else {
                message.error(t("Empty response data"));
            }
        } catch (error) {
            message.error(t("Data could not be retrieved"));
        }
        setIsabledataLoading(false);
    };

    const subscribedEmailDataColumns: ColumnsType<subscribedUser> = [
        {
            title: "Sr.No.",
            dataIndex: "srNo",
            key: "srNo",
            align: "center",
            className: `${roboto.className}`,
            render: (srNo) => <div>{srNo}.</div>,
        },
        {
            title: "E-Mail",
            dataIndex: "email",
            key: "email",
            align: "center",
            className: `${roboto.className}`,
            render: (email) => (
                <div className="flex justify-center">
                    <p className="text-left">{email}</p>
                </div>
            ),
        },
        {
            title: "Subscribed on",
            dataIndex: "createdAt",
            key: "createdAt",
            align: "center",
            render: (updatedAt) => {
                const date = new Date(updatedAt);
                const formattedDate = date.toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "short",
                    year: "2-digit",
                });
                const formattedTime = date.toLocaleTimeString("en-GB", {
                    hour: "2-digit",
                    minute: "2-digit",
                });
                return (
                    <div className="flex justify-center">
                        <p className="text-left">
                            {formattedDate} : {formattedTime}
                        </p>
                    </div>
                );
            },
            className: `${roboto.className}`,
        },
    ];

    const paginationConfig = {
        pageSize: pageSize,
        total: total,
        current: currentPage,
        onChange: (page: number) => {
            setCurrentPage(page);
        },
    };

    const handleDownloadCSVFile = async () => {
        try {
            setButtonIsLoading(true);
            const idToken = localStorage.getItem("idToken");

            const queryParams: QueryParams = {
                order: "desc",
            };
            const response = await downloadSubscribedEmails(
                idToken,
                queryParams
            );
            if (response) {
                const emailsArray = response
                    .split("\n")
                    .map((email: any) => email.trim());
                const csvContent =
                    "data:text/csv;charset=utf-8," + emailsArray.join("\n");

                const encodedUri = encodeURI(csvContent);
                const downloadLink = document.createElement("a");

                downloadLink.href = encodedUri;
                downloadLink.download = "subscribed_emails.csv";

                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            } else {
                message.error(t("Downloading the CSV file failed"));
            }
        } catch (error) {
            message.error(t("Error downloading CSV file"));
        }
        setButtonIsLoading(false);
    };

    return (
        <div className="flex flex-col">
            <Navbar />
            <div
                className={`${roboto.className} w-full bg-white text-[24px] text-black items-center mt-4`}
            >
                <>
                    <div className="flex items-center justify-between px-4">
                        <div className="flex items-center">
                            <div
                                className={`flex items-center font-[600] text-[23px] ${roboto.className}`}
                            >
                                {t("Subscribed Emails")}
                            </div>
                            <span className="text-gray-400 font-semibold text-[18px] font-roboto ml-1 mt-[2px]">
                                ({total})
                            </span>
                        </div>
                        <div className="text-[25px] text-black flex items-center space-x-3">
                            <Button
                                type="primary"
                                size="large"
                                onClick={handleDownloadCSVFile}
                                style={{ backgroundColor: "#67A1A3" }}
                                className="w-[190px] h-[40px] font-medium font-roboto bg-[#67A1A3] text-white shadow-inner"
                                loading={isButtonLoading}
                            >
                                {t("Export CSV File")}
                            </Button>
                        </div>
                    </div>
                </>
                {isTabledataLoading ? (
                    <div className="flex justify-center items-center h-[calc(100vh-150px)]">
                        <Spin size="default" />
                    </div>
                ) : (
                    <div className="mt-[26px]">
                        <Table
                            columns={subscribedEmailDataColumns}
                            dataSource={data}
                            pagination={{
                                ...paginationConfig,
                                className: "custom-pagination custom-select",
                            }}
                            bordered={false}
                            rowKey="srNo"
                            className={`custom-table ${
                                data.length > 5
                                    ? "custom-table-scroll"
                                    : "custom-table-scroll-hide"
                            }  scrollbar font-[400]`}
                            loading={{
                                indicator: <Spin size="default" />,
                                spinning: isTabledataLoading,
                            }}
                            scroll={{
                                y: "66vh",
                            }}
                            locale={{
                                emptyText: (
                                    <div
                                        className={`h-[61vh] flex items-center justify-center font-roboto font-[500]`}
                                    >
                                        <Empty
                                            description={t("No data available")}
                                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                                        />
                                    </div>
                                ),
                            }}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default SubscribedEmail;
